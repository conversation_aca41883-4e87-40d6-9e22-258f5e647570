﻿﻿namespace IRepairIT.FRMS.FREPORTS
{
    partial class FRM_REPORTS_LIST
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.kryptonPanel1 = new Krypton.Toolkit.KryptonPanel();
            this.kryptonGroupBox1 = new Krypton.Toolkit.KryptonGroupBox();
            this.dtpEndDate = new Krypton.Toolkit.KryptonDateTimePicker();
            this.kryptonLabel2 = new Krypton.Toolkit.KryptonLabel();
            this.dtpStartDate = new Krypton.Toolkit.KryptonDateTimePicker();
            this.kryptonLabel1 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonGroupBox2 = new Krypton.Toolkit.KryptonGroupBox();
            this.btnOverdueDebtsReport = new Krypton.Toolkit.KryptonButton();
            this.btnCustomerDebtsReport = new Krypton.Toolkit.KryptonButton();
            this.btnPaymentsReport = new Krypton.Toolkit.KryptonButton();
            this.btnDebtsReport = new Krypton.Toolkit.KryptonButton();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).BeginInit();
            this.kryptonPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1.Panel)).BeginInit();
            this.kryptonGroupBox1.Panel.SuspendLayout();
            this.kryptonGroupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox2.Panel)).BeginInit();
            this.kryptonGroupBox2.Panel.SuspendLayout();
            this.kryptonGroupBox2.SuspendLayout();
            this.SuspendLayout();
            //
            // kryptonPanel1
            //
            this.kryptonPanel1.Controls.Add(this.kryptonGroupBox2);
            this.kryptonPanel1.Controls.Add(this.kryptonGroupBox1);
            this.kryptonPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel1.Location = new System.Drawing.Point(0, 0);
            this.kryptonPanel1.Name = "kryptonPanel1";
            this.kryptonPanel1.Size = new System.Drawing.Size(584, 461);
            this.kryptonPanel1.TabIndex = 0;
            //
            // kryptonGroupBox1
            //
            this.kryptonGroupBox1.Location = new System.Drawing.Point(12, 12);
            this.kryptonGroupBox1.Name = "kryptonGroupBox1";
            //
            // kryptonGroupBox1.Panel
            //
            this.kryptonGroupBox1.Panel.Controls.Add(this.dtpEndDate);
            this.kryptonGroupBox1.Panel.Controls.Add(this.kryptonLabel2);
            this.kryptonGroupBox1.Panel.Controls.Add(this.dtpStartDate);
            this.kryptonGroupBox1.Panel.Controls.Add(this.kryptonLabel1);
            this.kryptonGroupBox1.Size = new System.Drawing.Size(560, 100);
            this.kryptonGroupBox1.TabIndex = 0;
            this.kryptonGroupBox1.Values.Heading = "Période";
            //
            // dtpEndDate
            //
            this.dtpEndDate.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.dtpEndDate.Location = new System.Drawing.Point(350, 20);
            this.dtpEndDate.Name = "dtpEndDate";
            this.dtpEndDate.Size = new System.Drawing.Size(150, 25);
            this.dtpEndDate.TabIndex = 3;
            //
            // kryptonLabel2
            //
            this.kryptonLabel2.Location = new System.Drawing.Point(300, 20);
            this.kryptonLabel2.Name = "kryptonLabel2";
            this.kryptonLabel2.Size = new System.Drawing.Size(44, 24);
            this.kryptonLabel2.TabIndex = 2;
            this.kryptonLabel2.Values.Text = "Au:";
            //
            // dtpStartDate
            //
            this.dtpStartDate.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.dtpStartDate.Location = new System.Drawing.Point(100, 20);
            this.dtpStartDate.Name = "dtpStartDate";
            this.dtpStartDate.Size = new System.Drawing.Size(150, 25);
            this.dtpStartDate.TabIndex = 1;
            //
            // kryptonLabel1
            //
            this.kryptonLabel1.Location = new System.Drawing.Point(50, 20);
            this.kryptonLabel1.Name = "kryptonLabel1";
            this.kryptonLabel1.Size = new System.Drawing.Size(44, 24);
            this.kryptonLabel1.TabIndex = 0;
            this.kryptonLabel1.Values.Text = "Du:";
            //
            // kryptonGroupBox2
            //
            this.kryptonGroupBox2.Location = new System.Drawing.Point(12, 118);
            this.kryptonGroupBox2.Name = "kryptonGroupBox2";
            //
            // kryptonGroupBox2.Panel
            //
            this.btnRepairsReport = new Krypton.Toolkit.KryptonButton();
            this.btnMonthlyRepairsReport = new Krypton.Toolkit.KryptonButton();
            this.btnMonthlyRevenueReport = new Krypton.Toolkit.KryptonButton();

            this.kryptonGroupBox2.Panel.Controls.Add(this.btnMonthlyRevenueReport);
            this.kryptonGroupBox2.Panel.Controls.Add(this.btnMonthlyRepairsReport);
            this.kryptonGroupBox2.Panel.Controls.Add(this.btnOverdueDebtsReport);
            this.kryptonGroupBox2.Panel.Controls.Add(this.btnCustomerDebtsReport);
            this.kryptonGroupBox2.Panel.Controls.Add(this.btnPaymentsReport);
            this.kryptonGroupBox2.Panel.Controls.Add(this.btnRepairsReport);
            this.kryptonGroupBox2.Panel.Controls.Add(this.btnDebtsReport);
            this.kryptonGroupBox2.Size = new System.Drawing.Size(560, 331);
            this.kryptonGroupBox2.TabIndex = 1;
            this.kryptonGroupBox2.Values.Heading = "Rapports";
            //
            // btnOverdueDebtsReport
            //
            this.btnOverdueDebtsReport.Location = new System.Drawing.Point(50, 230);
            this.btnOverdueDebtsReport.Name = "btnOverdueDebtsReport";
            this.btnOverdueDebtsReport.Size = new System.Drawing.Size(450, 40);
            this.btnOverdueDebtsReport.TabIndex = 3;
            this.btnOverdueDebtsReport.Values.Text = "Rapport des dettes échues";
            this.btnOverdueDebtsReport.Click += new System.EventHandler(this.BtnOverdueDebtsReport_Click);
            //
            // btnCustomerDebtsReport
            //
            this.btnCustomerDebtsReport.Location = new System.Drawing.Point(50, 180);
            this.btnCustomerDebtsReport.Name = "btnCustomerDebtsReport";
            this.btnCustomerDebtsReport.Size = new System.Drawing.Size(450, 40);
            this.btnCustomerDebtsReport.TabIndex = 2;
            this.btnCustomerDebtsReport.Values.Text = "Rapport des dettes par client";
            this.btnCustomerDebtsReport.Click += new System.EventHandler(this.BtnCustomerDebtsReport_Click);
            //
            // btnPaymentsReport
            //
            this.btnPaymentsReport.Location = new System.Drawing.Point(50, 130);
            this.btnPaymentsReport.Name = "btnPaymentsReport";
            this.btnPaymentsReport.Size = new System.Drawing.Size(450, 40);
            this.btnPaymentsReport.TabIndex = 1;
            this.btnPaymentsReport.Values.Text = "Rapport des paiements";
            this.btnPaymentsReport.Click += new System.EventHandler(this.BtnPaymentsReport_Click);
            //
            // btnDebtsReport
            //
            this.btnDebtsReport.Location = new System.Drawing.Point(50, 80);
            this.btnDebtsReport.Name = "btnDebtsReport";
            this.btnDebtsReport.Size = new System.Drawing.Size(450, 40);
            this.btnDebtsReport.TabIndex = 0;
            this.btnDebtsReport.Values.Text = "Rapport des dettes";
            this.btnDebtsReport.Click += new System.EventHandler(this.BtnDebtsReport_Click);
            //
            // btnRepairsReport
            //
            this.btnRepairsReport.Location = new System.Drawing.Point(50, 30);
            this.btnRepairsReport.Name = "btnRepairsReport";
            this.btnRepairsReport.Size = new System.Drawing.Size(450, 40);
            this.btnRepairsReport.TabIndex = 4;
            this.btnRepairsReport.Values.Text = "Rapport des réparations";
            this.btnRepairsReport.Click += new System.EventHandler(this.BtnRepairsReport_Click);
            //
            // btnMonthlyRepairsReport
            //
            this.btnMonthlyRepairsReport.Location = new System.Drawing.Point(50, 280);
            this.btnMonthlyRepairsReport.Name = "btnMonthlyRepairsReport";
            this.btnMonthlyRepairsReport.Size = new System.Drawing.Size(450, 40);
            this.btnMonthlyRepairsReport.TabIndex = 5;
            this.btnMonthlyRepairsReport.Values.Text = "Réparations mensuelles (2025)";
            this.btnMonthlyRepairsReport.Click += new System.EventHandler(this.BtnMonthlyRepairsReport_Click);
            //
            // btnMonthlyRevenueReport
            //
            this.btnMonthlyRevenueReport.Location = new System.Drawing.Point(50, 330);
            this.btnMonthlyRevenueReport.Name = "btnMonthlyRevenueReport";
            this.btnMonthlyRevenueReport.Size = new System.Drawing.Size(450, 40);
            this.btnMonthlyRevenueReport.TabIndex = 6;
            this.btnMonthlyRevenueReport.Values.Text = "Revenus mensuels (2025)";
            this.btnMonthlyRevenueReport.Click += new System.EventHandler(this.BtnMonthlyRevenueReport_Click);
            //
            // FRM_REPORTS_LIST
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(584, 461);
            this.Controls.Add(this.kryptonPanel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FRM_REPORTS_LIST";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Rapports";
            this.Load += new System.EventHandler(this.FRM_REPORTS_LIST_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).EndInit();
            this.kryptonPanel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1.Panel)).EndInit();
            this.kryptonGroupBox1.Panel.ResumeLayout(false);
            this.kryptonGroupBox1.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1)).EndInit();
            this.kryptonGroupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox2.Panel)).EndInit();
            this.kryptonGroupBox2.Panel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox2)).EndInit();
            this.kryptonGroupBox2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonPanel kryptonPanel1;
        private Krypton.Toolkit.KryptonGroupBox kryptonGroupBox2;
        private Krypton.Toolkit.KryptonButton btnOverdueDebtsReport;
        private Krypton.Toolkit.KryptonButton btnCustomerDebtsReport;
        private Krypton.Toolkit.KryptonButton btnPaymentsReport;
        private Krypton.Toolkit.KryptonButton btnDebtsReport;
        private Krypton.Toolkit.KryptonButton btnRepairsReport;
        private Krypton.Toolkit.KryptonButton btnMonthlyRepairsReport;
        private Krypton.Toolkit.KryptonButton btnMonthlyRevenueReport;
        private Krypton.Toolkit.KryptonGroupBox kryptonGroupBox1;
        private Krypton.Toolkit.KryptonDateTimePicker dtpEndDate;
        private Krypton.Toolkit.KryptonLabel kryptonLabel2;
        private Krypton.Toolkit.KryptonDateTimePicker dtpStartDate;
        private Krypton.Toolkit.KryptonLabel kryptonLabel1;
    }
}
