using IRepairIT.FRMS.FCUSTOMERS;
using Krypton.Toolkit;
using System;
using System.Windows.Forms;

namespace IRepairIT
{
    /// <summary>
    /// Classe de test pour le nouveau wizard de création de client
    /// </summary>
    public static class TestWizard
    {
        /// <summary>
        /// Méthode pour tester le wizard indépendamment
        /// </summary>
        public static void TestCustomerWizard()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // Créer et afficher le wizard
                using (var wizard = new FRM_CUSTOMERS_NEW_WIZARD())
                {
                    var result = wizard.ShowDialog();
                    
                    if (result == DialogResult.OK)
                    {
                        // Afficher les résultats
                        string message = "Wizard terminé avec succès!\n\n";
                        
                        if (wizard.CreatedCustomer != null)
                        {
                            message += $"Client créé:\n";
                            message += $"- Nom: {wizard.CreatedCustomer.name}\n";
                            message += $"- Téléphone: {wizard.CreatedCustomer.phone}\n";
                            message += $"- Email: {wizard.CreatedCustomer.email}\n\n";
                        }
                        
                        if (wizard.CreatedDevice != null)
                        {
                            message += $"Appareil créé:\n";
                            message += $"- Type: {wizard.CreatedDevice.Type}\n";
                            message += $"- Marque: {wizard.CreatedDevice.Brand}\n";
                            message += $"- Modèle: {wizard.CreatedDevice.Model}\n";
                            message += $"- Problème: {wizard.CreatedDevice.Problem}\n\n";
                        }
                        
                        if (wizard.CreatedRepairOrder != null)
                        {
                            message += $"Ordre de réparation créé:\n";
                            message += $"- N° commande: {wizard.CreatedRepairOrder.OrderNumber}\n";
                            message += $"- Statut: {wizard.CreatedRepairOrder.Status}\n";
                            message += $"- Coût total: {wizard.CreatedRepairOrder.TotalCost:C2}\n";
                        }
                        
                        KryptonMessageBox.Show(message, "Test Wizard - Succès",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    }
                    else
                    {
                        KryptonMessageBox.Show("Wizard annulé par l'utilisateur.", "Test Wizard - Annulé",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du test du wizard:\n{ex.Message}", "Test Wizard - Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// Point d'entrée pour tester le wizard en mode standalone
        /// </summary>
        [STAThread]
        public static void Main()
        {
            TestCustomerWizard();
        }
    }
}
