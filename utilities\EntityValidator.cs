﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace IRepairIT.utilities
{
    public class EntityValidator : IDataErrorInfo
    {
        [Browsable(false)]
        public string this[string columnName]
        {
            get
            {
                if (string.IsNullOrEmpty(columnName))
                    throw new ArgumentNullException(nameof(columnName));

                var propertyDescriptor = TypeDescriptor.GetProperties(this)[columnName];
                if (propertyDescriptor == null)
                    return string.Empty;

                var value = propertyDescriptor.GetValue(this);
                var validationContext = new ValidationContext(this) { MemberName = columnName };
                var validationResults = new List<ValidationResult>();

                bool isValid = Validator.TryValidateProperty(value, validationContext, validationResults);
                return isValid ? string.Empty : validationResults.FirstOrDefault()?.ErrorMessage ?? string.Empty;
            }
        }

        [Browsable(false)]
        public string Error
        {
            get
            {
                var validationResults = new List<ValidationResult>();
                var validationContext = new ValidationContext(this);

                bool isValid = Validator.TryValidateObject(this, validationContext, validationResults, true);
                return isValid ? string.Empty : string.Join(Environment.NewLine, validationResults.Select(r => r.ErrorMessage));
            }
        }

        [Browsable(false)]
        public bool IsValid => string.IsNullOrEmpty(Error);
    }
}
