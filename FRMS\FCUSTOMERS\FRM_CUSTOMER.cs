﻿﻿using IRepairIT.Data.Common;
using IRepairIT.Models;
using Krypton.Toolkit;
using MySql.Data.MySqlClient;
using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FCUSTOMERS
{
    public partial class FRM_CUSTOMER : KryptonForm
    {
        private readonly CustomerCommands _customerCommands;
        private Customer _customer;
        private bool _isEditMode;

        public bool HasChanges { get; private set; }

        public FRM_CUSTOMER()
        {
            InitializeComponent();
            _customerCommands = new CustomerCommands();
            _customer = new Customer();
            _isEditMode = false;
        }

        public FRM_CUSTOMER(int customerId)
        {
            InitializeComponent();
            _customerCommands = new CustomerCommands();
            _isEditMode = true;
            LoadCustomerAsync(customerId);
        }

        private async void LoadCustomerAsync(int customerId)
        {
            try
            {
                _customer = await _customerCommands.GetByIdAsync(customerId);
                if (_customer != null)
                {
                    FillFormWithCustomerData();
                }
                else
                {
                    KryptonMessageBox.Show("Client non trouvé.", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du client: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                this.Close();
            }
        }

        private void FRM_CUSTOMER_Load(object sender, EventArgs e)
        {
            InitializeControls();

            if (_isEditMode)
            {
                this.Text = "Modifier un client";
            }
            else
            {
                this.Text = "Ajouter un client";
            }
        }

        private void InitializeControls()
        {
            // Set up error provider
            errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;
        }

        private void FillFormWithCustomerData()
        {
            txtName.Text = _customer.name;
            txtPhone.Text = _customer.phone;
            txtEmail.Text = _customer.email;
            txtAddress.Text = _customer.address;
            txtNotes.Text = _customer.notes;
        }

        private async Task<bool> ValidateForm()
        {
            errorProvider.Clear();
            bool isValid = true;

            // Validate name
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                errorProvider.SetError(txtName, "Le nom est obligatoire");
                isValid = false;
                txtName.Focus();
            }

            // Validate phone
            if (string.IsNullOrWhiteSpace(txtPhone.Text))
            {
                errorProvider.SetError(txtPhone, "Le téléphone est obligatoire");
                isValid = false;
                if (string.IsNullOrWhiteSpace(txtName.Text) == false)
                    txtPhone.Focus();
            }
            else
            {
                // Check if phone already exists
                bool phoneExists = await _customerCommands.PhoneExistsAsync(txtPhone.Text.Trim(), _isEditMode ? _customer.id : 0);
                if (phoneExists)
                {
                    errorProvider.SetError(txtPhone, "Ce numéro de téléphone est déjà utilisé par un autre client");
                    isValid = false;
                    txtPhone.Focus();
                }
            }

            // Validate email if provided
            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                if (!IsValidEmail(txtEmail.Text))
                {
                    errorProvider.SetError(txtEmail, "L'adresse email n'est pas valide");
                    isValid = false;
                    if (string.IsNullOrWhiteSpace(txtName.Text) == false &&
                        string.IsNullOrWhiteSpace(txtPhone.Text) == false)
                        txtEmail.Focus();
                }
                else
                {
                    // Check if email already exists
                    bool emailExists = await _customerCommands.EmailExistsAsync(txtEmail.Text.Trim(), _isEditMode ? _customer.id : 0);
                    if (emailExists)
                    {
                        errorProvider.SetError(txtEmail, "Cette adresse email est déjà utilisée par un autre client");
                        isValid = false;
                        txtEmail.Focus();
                    }
                }
            }

            return isValid;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void UpdateCustomerFromForm()
        {
            _customer.name = txtName.Text.Trim();
            _customer.phone = txtPhone.Text.Trim();
            _customer.email = txtEmail.Text.Trim();
            _customer.address = txtAddress.Text.Trim();
            _customer.notes = txtNotes.Text.Trim();
        }

        private async Task<bool> SaveCustomer()
        {
            try
            {
                if (!await ValidateForm())
                    return false;

                UpdateCustomerFromForm();

                if (_isEditMode)
                {
                    bool success = await _customerCommands.UpdateAsync(_customer);
                    if (success)
                    {
                        KryptonMessageBox.Show("Le client a été mis à jour avec succès", "Succès",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        HasChanges = true;
                    }
                    else
                    {
                        KryptonMessageBox.Show("Erreur lors de la mise à jour du client", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return false;
                    }
                }
                else
                {
                    int customerId = await _customerCommands.InsertAsync(_customer);
                    if (customerId > 0)
                    {
                        _customer.id = customerId;
                        _isEditMode = true;
                        KryptonMessageBox.Show("Le client a été ajouté avec succès", "Succès",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        HasChanges = true;
                    }
                    else
                    {
                        KryptonMessageBox.Show("Erreur lors de l'ajout du client", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return false;
                    }
                }

                return true;
            }
            catch (MySqlException ex) when (ex.Number == 1062)
            {
                string errorMessage = ex.Message.ToLower();
                if (errorMessage.Contains("email"))
                {
                    KryptonMessageBox.Show("Cette adresse email est déjà utilisée par un autre client", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    txtEmail.Focus();
                }
                else if (errorMessage.Contains("phone"))
                {
                    KryptonMessageBox.Show("Ce numéro de téléphone est déjà utilisé par un autre client", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    txtPhone.Focus();
                }
                else
                {
                    KryptonMessageBox.Show($"Erreur de duplication: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
                return false;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'enregistrement du client: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                return false;
            }
        }

        private void ResetForm()
        {
            txtName.Text = string.Empty;
            txtPhone.Text = string.Empty;
            txtEmail.Text = string.Empty;
            txtAddress.Text = string.Empty;
            txtNotes.Text = string.Empty;

            _customer = new Customer();
            _isEditMode = false;

            this.Text = "Ajouter un client";

            errorProvider.Clear();

            txtName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            await SaveCustomer();
        }

        private async void btnSaveAndClose_Click(object sender, EventArgs e)
        {
            if (await SaveCustomer())
            {
                this.Close();
            }
        }

        private async void btnSaveAndNew_Click(object sender, EventArgs e)
        {
            if (await SaveCustomer())
            {
                ResetForm();
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
