﻿﻿using IRepairIT.Data.Common;
using Krypton.Toolkit;
using System;
using System.IO;
using System.Windows.Forms;
using FastReport;

namespace IRepairIT.FRMS.FREPORTS
{
    public partial class FRM_REPORTS_LIST : KryptonForm
    {
        private readonly CustomerCommands _customerCmd;
        private readonly RepairOrderCommands _repairOrderCmd;
        private readonly DebtCommands _debtCmd;
        private readonly UserCommands _userCmd;
        private readonly ShopInfoCommands _shopInfoCmd;

        public FRM_REPORTS_LIST()
        {
            InitializeComponent();
            _customerCmd = new CustomerCommands();
            _repairOrderCmd = new RepairOrderCommands();
            _debtCmd = new DebtCommands();
            _userCmd = new UserCommands();
            _shopInfoCmd = new ShopInfoCommands();
        }

        private void FRM_REPORTS_LIST_Load(object sender, EventArgs e)
        {
            // Initialize date pickers
            dtpStartDate.Value = DateTime.Now.AddDays(-30);
            dtpEndDate.Value = DateTime.Now;
        }

        private async void BtnDebtsReport_Click(object sender, EventArgs e)
        {
            try
            {
                Cursor = Cursors.WaitCursor;

                // Get date range
                DateTime startDate = dtpStartDate.Value.Date;
                DateTime endDate = dtpEndDate.Value.Date.AddDays(1).AddSeconds(-1);

                // Get debts
                var debts = await _debtCmd.GetByDateRangeAsync(startDate, endDate);

                // Create report
                using (Report report = new Report())
                {
                    // Get report file path
                    string reportFile = Path.Combine(Application.StartupPath, "Reports", "DebtsReport.frx");

                    // Check if report file exists
                    if (!File.Exists(reportFile))
                    {
                        // Create directory if it doesn't exist
                        Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                        // Copy the report file from the project to the output directory
                        File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "DebtsReport.frx"), reportFile);

                        if (!File.Exists(reportFile))
                        {
                            KryptonMessageBox.Show("Le fichier de rapport n'existe pas: " + reportFile, "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                            return;
                        }
                    }

                    // Load report
                    report.Load(reportFile);

                    // Register data
                    report.RegisterData(debts, "Debts");

                    // Get shop info
                    var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                    if (shopInfo != null)
                    {
                        report.SetParameterValue("ShopName", shopInfo.name);
                        report.SetParameterValue("ShopAddress", shopInfo.address);
                        report.SetParameterValue("ShopPhone", shopInfo.phone);
                        report.SetParameterValue("ShopEmail", shopInfo.email);
                    }

                    // Set date range parameters
                    report.SetParameterValue("StartDate", startDate.ToString("dd/MM/yyyy"));
                    report.SetParameterValue("EndDate", endDate.ToString("dd/MM/yyyy"));

                    // Prepare report
                    report.Prepare();

                    // Show report
                    report.Show();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la génération du rapport: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private async void BtnRepairsReport_Click(object sender, EventArgs e)
        {
            try
            {
                Cursor = Cursors.WaitCursor;

                // Get date range
                DateTime startDate = dtpStartDate.Value.Date;
                DateTime endDate = dtpEndDate.Value.Date.AddDays(1).AddSeconds(-1);

                // Get repairs
                var repairs = await _repairOrderCmd.GetByDateRangeAsync(startDate, endDate);

                // Create report
                using (Report report = new Report())
                {
                    // Get report file path
                    string reportFile = Path.Combine(Application.StartupPath, "Reports", "RepairsReport.frx");

                    // Check if report file exists
                    if (!File.Exists(reportFile))
                    {
                        // Create directory if it doesn't exist
                        Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                        // Copy the report file from the project to the output directory
                        File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "RepairsReport.frx"), reportFile);

                        if (!File.Exists(reportFile))
                        {
                            KryptonMessageBox.Show("Le fichier de rapport n'existe pas: " + reportFile, "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                            return;
                        }
                    }

                    // Load report
                    report.Load(reportFile);

                    // Register data
                    report.RegisterData(repairs, "Repairs");

                    // Get shop info
                    var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                    if (shopInfo != null)
                    {
                        report.SetParameterValue("ShopName", shopInfo.name);
                        report.SetParameterValue("ShopAddress", shopInfo.address);
                        report.SetParameterValue("ShopPhone", shopInfo.phone);
                        report.SetParameterValue("ShopEmail", shopInfo.email);
                    }

                    // Set date range parameters
                    report.SetParameterValue("StartDate", startDate.ToString("dd/MM/yyyy"));
                    report.SetParameterValue("EndDate", endDate.ToString("dd/MM/yyyy"));

                    // Prepare report
                    report.Prepare();

                    // Show report
                    report.Show();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la génération du rapport: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private async void BtnMonthlyRepairsReport_Click(object sender, EventArgs e)
        {
            try
            {
                Cursor = Cursors.WaitCursor;

                // Get year
                int year = 2025; // Fixed to 2025 as requested

                // Get monthly repairs
                var monthlyRepairs = await _repairOrderCmd.GetMonthlyRepairsAsync(year);

                // Create report
                using (Report report = new Report())
                {
                    // Get report file path
                    string reportFile = Path.Combine(Application.StartupPath, "Reports", "MonthlyRepairsReport.frx");

                    // Check if report file exists
                    if (!File.Exists(reportFile))
                    {
                        // Create directory if it doesn't exist
                        Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                        // Copy the report file from the project to the output directory
                        File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "MonthlyRepairsReport.frx"), reportFile);

                        if (!File.Exists(reportFile))
                        {
                            KryptonMessageBox.Show("Le fichier de rapport n'existe pas: " + reportFile, "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                            return;
                        }
                    }

                    // Load report
                    report.Load(reportFile);

                    // Register data
                    report.RegisterData(monthlyRepairs, "MonthlyRepairs");

                    // Get shop info
                    var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                    if (shopInfo != null)
                    {
                        report.SetParameterValue("ShopName", shopInfo.name);
                        report.SetParameterValue("ShopAddress", shopInfo.address);
                        report.SetParameterValue("ShopPhone", shopInfo.phone);
                        report.SetParameterValue("ShopEmail", shopInfo.email);
                    }

                    // Set year parameter
                    report.SetParameterValue("Year", year.ToString());

                    // Prepare report
                    report.Prepare();

                    // Show report
                    report.Show();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la génération du rapport: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private async void BtnMonthlyRevenueReport_Click(object sender, EventArgs e)
        {
            try
            {
                Cursor = Cursors.WaitCursor;

                // Get year
                int year = 2025; // Fixed to 2025 as requested

                // Get monthly revenue
                var monthlyRevenue = await _repairOrderCmd.GetMonthlyRevenueAsync(year);

                // Create report
                using (Report report = new Report())
                {
                    // Get report file path
                    string reportFile = Path.Combine(Application.StartupPath, "Reports", "MonthlyRevenueReport.frx");

                    // Check if report file exists
                    if (!File.Exists(reportFile))
                    {
                        // Create directory if it doesn't exist
                        Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                        // Copy the report file from the project to the output directory
                        File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "MonthlyRevenueReport.frx"), reportFile);

                        if (!File.Exists(reportFile))
                        {
                            KryptonMessageBox.Show("Le fichier de rapport n'existe pas: " + reportFile, "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                            return;
                        }
                    }

                    // Load report
                    report.Load(reportFile);

                    // Register data
                    report.RegisterData(monthlyRevenue, "MonthlyRevenue");

                    // Get shop info
                    var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                    if (shopInfo != null)
                    {
                        report.SetParameterValue("ShopName", shopInfo.name);
                        report.SetParameterValue("ShopAddress", shopInfo.address);
                        report.SetParameterValue("ShopPhone", shopInfo.phone);
                        report.SetParameterValue("ShopEmail", shopInfo.email);
                    }

                    // Set year parameter
                    report.SetParameterValue("Year", year.ToString());

                    // Prepare report
                    report.Prepare();

                    // Show report
                    report.Show();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la génération du rapport: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private async void BtnPaymentsReport_Click(object sender, EventArgs e)
        {
            try
            {
                Cursor = Cursors.WaitCursor;

                // Get date range
                DateTime startDate = dtpStartDate.Value.Date;
                DateTime endDate = dtpEndDate.Value.Date.AddDays(1).AddSeconds(-1);

                // Get payments
                var payments = await _debtCmd.GetPaymentsByDateRangeAsync(startDate, endDate);

                // Create report
                using (Report report = new Report())
                {
                    // Get report file path
                    string reportFile = Path.Combine(Application.StartupPath, "Reports", "PaymentsReport.frx");

                    // Check if report file exists
                    if (!File.Exists(reportFile))
                    {
                        // Create directory if it doesn't exist
                        Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                        // Copy the report file from the project to the output directory
                        File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "PaymentsReport.frx"), reportFile);

                        if (!File.Exists(reportFile))
                        {
                            KryptonMessageBox.Show("Le fichier de rapport n'existe pas: " + reportFile, "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                            return;
                        }
                    }

                    // Load report
                    report.Load(reportFile);

                    // Register data
                    report.RegisterData(payments, "Payments");

                    // Get shop info
                    var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                    if (shopInfo != null)
                    {
                        report.SetParameterValue("ShopName", shopInfo.name);
                        report.SetParameterValue("ShopAddress", shopInfo.address);
                        report.SetParameterValue("ShopPhone", shopInfo.phone);
                        report.SetParameterValue("ShopEmail", shopInfo.email);
                    }

                    // Set date range parameters
                    report.SetParameterValue("StartDate", startDate.ToString("dd/MM/yyyy"));
                    report.SetParameterValue("EndDate", endDate.ToString("dd/MM/yyyy"));

                    // Prepare report
                    report.Prepare();

                    // Show report
                    report.Show();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la génération du rapport: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private async void BtnCustomerDebtsReport_Click(object sender, EventArgs e)
        {
            try
            {
                // Open customer selector
                var frm = new FCOMMON.FRM_SELECT_CUSTOMER();
                if (frm.ShowDialog() == DialogResult.OK && frm.SelectedCustomer != null)
                {
                    Cursor = Cursors.WaitCursor;

                    // Get customer debts
                    var debts = await _debtCmd.GetByCustomerIdAsync(frm.SelectedCustomer.id);

                    // Create report
                    using (Report report = new Report())
                    {
                        // Get report file path
                        string reportFile = Path.Combine(Application.StartupPath, "Reports", "CustomerDebtsReport.frx");

                        // Check if report file exists
                        if (!File.Exists(reportFile))
                        {
                            // Create directory if it doesn't exist
                            Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                            // Copy the report file from the project to the output directory
                            File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "CustomerDebtsReport.frx"), reportFile);

                            if (!File.Exists(reportFile))
                            {
                                KryptonMessageBox.Show("Le fichier de rapport n'existe pas: " + reportFile, "Erreur",
                                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                                return;
                            }
                        }

                        // Load report
                        report.Load(reportFile);

                        // Register data
                        report.RegisterData(debts, "Debts");

                        // Get shop info
                        var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                        if (shopInfo != null)
                        {
                            report.SetParameterValue("ShopName", shopInfo.name);
                            report.SetParameterValue("ShopAddress", shopInfo.address);
                            report.SetParameterValue("ShopPhone", shopInfo.phone);
                            report.SetParameterValue("ShopEmail", shopInfo.email);
                        }

                        // Set customer parameters
                        report.SetParameterValue("CustomerName", frm.SelectedCustomer.name);
                        report.SetParameterValue("CustomerPhone", frm.SelectedCustomer.phone);

                        // Prepare report
                        report.Prepare();

                        // Show report
                        report.Show();
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la génération du rapport: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private async void BtnOverdueDebtsReport_Click(object sender, EventArgs e)
        {
            try
            {
                Cursor = Cursors.WaitCursor;

                // Get overdue debts
                var debts = await _debtCmd.GetOverdueDebtsAsync();

                // Create report
                using (Report report = new Report())
                {
                    // Get report file path
                    string reportFile = Path.Combine(Application.StartupPath, "Reports", "OverdueDebtsReport.frx");

                    // Check if report file exists
                    if (!File.Exists(reportFile))
                    {
                        // Create directory if it doesn't exist
                        Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                        // Copy the report file from the project to the output directory
                        File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "OverdueDebtsReport.frx"), reportFile);

                        if (!File.Exists(reportFile))
                        {
                            KryptonMessageBox.Show("Le fichier de rapport n'existe pas: " + reportFile, "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                            return;
                        }
                    }

                    // Load report
                    report.Load(reportFile);

                    // Register data
                    report.RegisterData(debts, "Debts");

                    // Get shop info
                    var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                    if (shopInfo != null)
                    {
                        report.SetParameterValue("ShopName", shopInfo.name);
                        report.SetParameterValue("ShopAddress", shopInfo.address);
                        report.SetParameterValue("ShopPhone", shopInfo.phone);
                        report.SetParameterValue("ShopEmail", shopInfo.email);
                    }

                    // Set current date parameter
                    report.SetParameterValue("CurrentDate", DateTime.Now.ToString("dd/MM/yyyy"));

                    // Prepare report
                    report.Prepare();

                    // Show report
                    report.Show();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la génération du rapport: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }
    }
}
