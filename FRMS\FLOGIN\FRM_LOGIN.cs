﻿using IRepairIT.Data.Common;
using IRepairIT.FRMS.FDB;
using IRepairIT.FRMS.FMAIN;
using IRepairIT.Models;
using IRepairIT.Properties;
using Krypton.Navigator;
using Krypton.Toolkit;
using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FLOGIN
{
    public partial class FRM_LOGIN : KryptonForm
    {
        public FRM_LOGIN()
        {
            // Set DoubleBuffered to reduce flickering
            this.SetStyle(ControlStyles.OptimizedDoubleBuffer |
                         ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint, true);

            // Initialize components
            InitializeComponent();

            // Set initial properties before showing the form
            this.Opacity = 0;

            // Register event handlers
            btnNewDB.Click += BtnNewDB_Click;
            this.KeyPreview = true;
            this.KeyDown += FRM_LOGIN_KeyDown;

            // Handle form shown event to perform UI adjustments after form is loaded
            this.Shown += FRM_LOGIN_Shown;
        }

        private void FRM_LOGIN_Shown(object sender, EventArgs e)
        {
            // Perform the button click after form is shown
            this.SuspendLayout();
            buttonLeft.PerformClick();
            this.ResumeLayout();

            // Fade in the form
            Timer fadeTimer = new Timer();
            fadeTimer.Interval = 10;
            fadeTimer.Tick += (s, args) => {
                if (this.Opacity < 1)
                    this.Opacity += 0.05;
                else
                {
                    fadeTimer.Stop();
                    fadeTimer.Dispose();
                }
            };
            fadeTimer.Start();
        }

        private void BtnNewDB_Click(object sender, System.EventArgs e)
        {
            this.Hide();
            FRM_DATABASELIST frm = new FRM_DATABASELIST();
            frm.ShowDialog();
            this.Close();
        }

        private void FRM_LOGIN_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                BTNLOGIN.Focus();
            }
            if (e.Control && e.KeyCode == Keys.P)
            {
                ShowPasswordChangeDialog();
            }
        }

        private void ShowPasswordChangeDialog()
        {
            var passwordInput = new FRM_PASSWORDINPUT();
            if (passwordInput.ShowDialog() == DialogResult.OK)
            {
                if (passwordInput.EnteredPassword == "19951997")
                {
                    if (TXTUSER.SelectedItem != null)
                    {
                        var user = TXTUSER.SelectedItem as User;
                        if (user != null)
                        {
                            var changePasswordForm = new FRM_CHANGEPASSWORD(user.Id, user.Username);
                            changePasswordForm.ShowDialog();
                        }
                        else
                        {
                            KryptonMessageBox.Show("Veuillez sélectionner un utilisateur d'abord", "Avertissement", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                        }
                    }
                    else
                    {
                        KryptonMessageBox.Show("Veuillez sélectionner un utilisateur d'abord", "Avertissement", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    }
                }
                else
                {
                    KryptonMessageBox.Show("Mot de passe incorrect", "Erreur", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
            }
        }

        private async Task LOAD_USERS()
        {
            var userCommands = new UserCommands();
            var users = await userCommands.GetAllAsync();
            TXTUSER.ValueMember = "id";
            TXTUSER.DisplayMember = "username";
            TXTUSER.DataSource = users.ToList();
        }
        private async void FRM_LOGIN_Load(object sender, System.EventArgs e)
        {
            await LOAD_USERS();
        }

        private void btnShow_Click(object sender, System.EventArgs e)
        {
            TXTPASSWORD.PasswordChar = (TXTPASSWORD.Tag as string == "0") ? '\0' : '#';
            TXTPASSWORD.ButtonSpecs[0].Image = (TXTPASSWORD.Tag as string == "0") ? Resources.eye_no : Resources.eye;
            TXTPASSWORD.Tag = (TXTPASSWORD.Tag as string == "0") ? "1" : "0";
        }

        private void TXTPASSWORD_Enter(object sender, System.EventArgs e)
        {
            TXTPASSWORD.SelectAll();
        }

        private void buttonLeft_Click(object sender, System.EventArgs e)
        {
            // Determine current mode
            bool isHeaderBarCheckButtonGroup = kryptonNavigator1.NavigatorMode == Krypton.Navigator.NavigatorMode.HeaderBarCheckButtonGroup;

            // Set new size first to avoid flickering
            this.SuspendLayout();

            // Calculate new size
            Size newSize = isHeaderBarCheckButtonGroup ? new Size(380, 458) : new Size(555, 458);

            // Set form size
            this.Size = newSize;

            // Change navigator mode
            kryptonNavigator1.NavigatorMode = isHeaderBarCheckButtonGroup
                ? NavigatorMode.HeaderBarCheckButtonOnly
                : NavigatorMode.HeaderBarCheckButtonGroup;

            // Change button style
            buttonLeft.TypeRestricted = isHeaderBarCheckButtonGroup
                ? PaletteNavButtonSpecStyle.ArrowRight
                : PaletteNavButtonSpecStyle.ArrowLeft;

            this.ResumeLayout();

            // Force redraw to eliminate black artifacts
            this.Invalidate(true);
            kryptonNavigator1.Invalidate(true);
            kryptonPanel1.Invalidate(true);
        }

        private async void BTNLOGIN_Click(object sender, System.EventArgs e)
        {
            errorProvider1.Clear();

            if (string.IsNullOrWhiteSpace(TXTUSER.Text))
            {
                errorProvider1.SetError(TXTUSER, "Veuillez entrer un nom d'utilisateur");
                TXTUSER.Focus();
                return;
            }

            var userCommands = new UserCommands();

            var user = await userCommands.AuthenticateAsync(TXTUSER.Text, TXTPASSWORD.Text);

            if (user != null)
            {
                // Stocker l'ID de l'utilisateur dans les paramètres de l'application
                Settings.Default.CurrentUserId = user.Id;
                Settings.Default.Save();

                // Créer une session utilisateur dans la base de données
                try
                {
                    await userCommands.CreateUserSessionAsync(user.Id);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Erreur lors de la création de la session utilisateur: {ex.Message}");
                }

                var mainForm = new FRM_MAIN();
                this.Hide();
                mainForm.ShowDialog();
                this.Close();
            }
            else
            {
                errorProvider1.SetError(TXTPASSWORD, "Mot de passe incorrect");
                TXTPASSWORD.Focus();
                TXTPASSWORD.SelectAll();
            }
        }
    }
}
