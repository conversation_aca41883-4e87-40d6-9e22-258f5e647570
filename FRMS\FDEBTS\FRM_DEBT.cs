﻿﻿using IRepairIT.Data.Common;
using IRepairIT.Enums;
using IRepairIT.Models;
using Krypton.Toolkit;
using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FDEBTS
{
    public partial class FRM_DEBT : KryptonForm
    {
        private readonly DebtCommands _cmd;
        private readonly CustomerCommands _customerCmd;
        private readonly RepairOrderCommands _repairOrderCmd;
        private int _debtId;
        private Debt _debt;
        private bool _isNewDebt;

        // Constructor for new debt
        public FRM_DEBT()
        {
            InitializeComponent();
            _cmd = new DebtCommands();
            _customerCmd = new CustomerCommands();
            _repairOrderCmd = new RepairOrderCommands();
            _debtId = 0;
            _isNewDebt = true;
            _debt = new Debt
            {
                DebtDate = DateTime.Now,
                Status = PaymentStatus.unpaid
            };
        }

        // Constructor for existing debt
        public FRM_DEBT(int debtId)
        {
            InitializeComponent();
            _cmd = new DebtCommands();
            _customerCmd = new CustomerCommands();
            _repairOrderCmd = new RepairOrderCommands();
            _debtId = debtId;
            _isNewDebt = false;
            _debt = new Debt();
        }

        private async void FRM_DEBT_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize status combo box
                InitializeStatusComboBox();

                if (_isNewDebt)
                {
                    // New debt
                    this.Text = "Nouvelle dette";
                    dtpDebtDate.Value = DateTime.Now;
                    dtpDueDate.Value = DateTime.Now.AddDays(30);
                    cmbStatus.SelectedIndex = 0; // Unpaid
                }
                else
                {
                    // Load existing debt
                    await LoadDebt();
                    this.Text = $"Dette #{_debtId}";
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement de la dette: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void InitializeStatusComboBox()
        {
            cmbStatus.Items.Clear();
            cmbStatus.Items.Add("Non payé");
            cmbStatus.Items.Add("Partiellement payé");
            cmbStatus.Items.Add("Payé");
        }

        private async Task LoadDebt()
        {
            try
            {
                // Show loading indicator
                Cursor = Cursors.WaitCursor;

                // Get debt
                _debt = await _cmd.GetByIdAsync(_debtId);
                if (_debt == null)
                {
                    KryptonMessageBox.Show("Dette introuvable.", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    this.DialogResult = DialogResult.Cancel;
                    this.Close();
                    return;
                }

                // Get customer
                var customer = await _customerCmd.GetByIdAsync(_debt.CustomerId);
                if (customer != null)
                {
                    txtCustomer.Text = customer.name;
                }

                // Get repair order
                if (_debt.RepairOrderId.HasValue)
                {
                    var repairOrder = await _repairOrderCmd.GetByIdAsync(_debt.RepairOrderId.Value);
                    if (repairOrder != null)
                    {
                        txtRepairOrder.Text = repairOrder.OrderNumber;
                    }
                }

                // Set form values
                txtAmount.Text = _debt.Amount.ToString("F2");
                txtPaidAmount.Text = _debt.PaidAmount.ToString("F2");
                txtRemainingAmount.Text = (_debt.Amount - _debt.PaidAmount).ToString("F2");
                dtpDebtDate.Value = _debt.DebtDate;

                if (_debt.DueDate.HasValue)
                {
                    chkDueDate.Checked = true;
                    dtpDueDate.Value = _debt.DueDate.Value;
                }
                else
                {
                    chkDueDate.Checked = false;
                    dtpDueDate.Enabled = false;
                }

                // Set status
                switch (_debt.Status)
                {
                    case PaymentStatus.unpaid:
                        cmbStatus.SelectedIndex = 0;
                        break;
                    case PaymentStatus.partially_paid:
                        cmbStatus.SelectedIndex = 1;
                        break;
                    case PaymentStatus.paid:
                        cmbStatus.SelectedIndex = 2;
                        break;
                }

                txtNotes.Text = _debt.Notes;

                // Update UI based on payment status
                UpdateUIBasedOnStatus();
            }
            finally
            {
                // Hide loading indicator
                Cursor = Cursors.Default;
            }
        }

        private void UpdateUIBasedOnStatus()
        {
            // If debt is paid, disable editing
            bool isPaid = cmbStatus.SelectedIndex == 2; // Paid

            // These fields should always be readonly for existing debts
            txtCustomer.ReadOnly = true;
            txtRepairOrder.ReadOnly = true;

            // These fields can be edited if not paid
            txtAmount.ReadOnly = isPaid || !_isNewDebt;
            txtPaidAmount.ReadOnly = true; // Always readonly, updated through payments

            // Due date and notes can be edited regardless of status
            chkDueDate.Enabled = !isPaid;
            dtpDueDate.Enabled = chkDueDate.Checked && !isPaid;

            // Status can be changed manually
            cmbStatus.Enabled = !isPaid;

            // Notes can always be edited
            txtNotes.ReadOnly = isPaid;

            // Save button
            btnSave.Enabled = !isPaid;
        }

        private void ChkDueDate_CheckedChanged(object sender, EventArgs e)
        {
            dtpDueDate.Enabled = chkDueDate.Checked;
        }

        private void CmbStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateUIBasedOnStatus();
        }

        private void TxtAmount_TextChanged(object sender, EventArgs e)
        {
            UpdateRemainingAmount();
        }

        private void TxtPaidAmount_TextChanged(object sender, EventArgs e)
        {
            UpdateRemainingAmount();
        }

        private void UpdateRemainingAmount()
        {
            try
            {
                if (decimal.TryParse(txtAmount.Text, out decimal amount) &&
                    decimal.TryParse(txtPaidAmount.Text, out decimal paidAmount))
                {
                    decimal remainingAmount = amount - paidAmount;
                    txtRemainingAmount.Text = remainingAmount.ToString("F2");
                }
            }
            catch
            {
                txtRemainingAmount.Text = "0.00";
            }
        }

        private void BtnSelectCustomer_Click(object sender, EventArgs e)
        {
            try
            {
                // Open customer selector
                var frm = new FCOMMON.FRM_SELECT_CUSTOMER();
                if (frm.ShowDialog() == DialogResult.OK)
                {
                    // Get selected customer
                    if (frm.SelectedCustomer != null)
                    {
                        _debt.CustomerId = frm.SelectedCustomer.id;
                        txtCustomer.Text = frm.SelectedCustomer.name;
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la sélection du client: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void BtnSelectRepairOrder_Click(object sender, EventArgs e)
        {
            try
            {
                // Check if customer is selected
                if (_debt.CustomerId <= 0)
                {
                    KryptonMessageBox.Show("Veuillez d'abord sélectionner un client.", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                // Get repair orders for customer
                var repairOrders = await _repairOrderCmd.GetByCustomerIdAsync(_debt.CustomerId);
                if (repairOrders == null || repairOrders.Count() == 0)
                {
                    KryptonMessageBox.Show("Aucun ordre de réparation trouvé pour ce client.", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                // Show repair order selector
                using (var selectorForm = new KryptonForm())
                {
                    selectorForm.Text = "Sélectionner un ordre de réparation";
                    selectorForm.Size = new Size(500, 400);
                    selectorForm.StartPosition = FormStartPosition.CenterParent;
                    selectorForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                    selectorForm.MaximizeBox = false;
                    selectorForm.MinimizeBox = false;

                    var listBox = new KryptonListBox
                    {
                        Location = new Point(20, 20),
                        Size = new Size(440, 280),
                        DisplayMember = "DisplayText"
                    };

                    // Add repair orders to list box
                    foreach (var order in repairOrders)
                    {
                        listBox.Items.Add(new
                        {
                            Id = order.Id,
                            DisplayText = $"{order.OrderNumber} - {order.TotalCost:C} - {order.CreatedAt:dd/MM/yyyy}"
                        });
                    }

                    var okButton = new KryptonButton
                    {
                        Text = "OK",
                        Location = new Point(280, 320),
                        Size = new Size(80, 30),
                        DialogResult = DialogResult.OK
                    };

                    var cancelButton = new KryptonButton
                    {
                        Text = "Annuler",
                        Location = new Point(380, 320),
                        Size = new Size(80, 30),
                        DialogResult = DialogResult.Cancel
                    };

                    selectorForm.Controls.Add(listBox);
                    selectorForm.Controls.Add(okButton);
                    selectorForm.Controls.Add(cancelButton);
                    selectorForm.AcceptButton = okButton;
                    selectorForm.CancelButton = cancelButton;

                    if (selectorForm.ShowDialog() == DialogResult.OK && listBox.SelectedItem != null)
                    {
                        // Get selected repair order
                        dynamic selectedItem = listBox.SelectedItem;
                        int repairOrderId = selectedItem.Id;

                        var repairOrder = await _repairOrderCmd.GetByIdAsync(repairOrderId);
                        if (repairOrder != null)
                        {
                            _debt.RepairOrderId = repairOrder.Id;
                            txtRepairOrder.Text = repairOrder.OrderNumber;

                            // Set amount to remaining amount of repair order
                            decimal remainingAmount = repairOrder.TotalCost - repairOrder.PaidAmount;
                            if (remainingAmount > 0)
                            {
                                txtAmount.Text = remainingAmount.ToString("F2");
                                txtPaidAmount.Text = "0.00";
                                UpdateRemainingAmount();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la sélection de l'ordre de réparation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (_debt.CustomerId <= 0)
                {
                    KryptonMessageBox.Show("Veuillez sélectionner un client.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    return;
                }

                if (!decimal.TryParse(txtAmount.Text, out decimal amount) || amount <= 0)
                {
                    KryptonMessageBox.Show("Veuillez entrer un montant valide.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    return;
                }

                if (!decimal.TryParse(txtPaidAmount.Text, out decimal paidAmount) || paidAmount < 0)
                {
                    KryptonMessageBox.Show("Veuillez entrer un montant payé valide.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    return;
                }

                if (paidAmount > amount)
                {
                    KryptonMessageBox.Show("Le montant payé ne peut pas être supérieur au montant total.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    return;
                }

                // Update debt object
                _debt.Amount = amount;
                _debt.PaidAmount = paidAmount;
                _debt.DebtDate = dtpDebtDate.Value;
                _debt.DueDate = chkDueDate.Checked ? dtpDueDate.Value : (DateTime?)null;

                // Set status based on selection
                switch (cmbStatus.SelectedIndex)
                {
                    case 0:
                        _debt.Status = PaymentStatus.unpaid;
                        break;
                    case 1:
                        _debt.Status = PaymentStatus.partially_paid;
                        break;
                    case 2:
                        _debt.Status = PaymentStatus.paid;
                        break;
                }

                _debt.Notes = txtNotes.Text;

                // Save debt
                if (_isNewDebt)
                {
                    _debt.CreatedAt = DateTime.Now;
                    int debtId = await _cmd.InsertAsync(_debt);
                    if (debtId > 0)
                    {
                        KryptonMessageBox.Show("Dette créée avec succès.", "Succès",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        _debtId = debtId;
                        _isNewDebt = false;
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        KryptonMessageBox.Show("Erreur lors de la création de la dette.", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }
                }
                else
                {
                    _debt.UpdatedAt = DateTime.Now;
                    bool success = await _cmd.UpdateAsync(_debt);
                    if (success)
                    {
                        KryptonMessageBox.Show("Dette mise à jour avec succès.", "Succès",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        KryptonMessageBox.Show("Erreur lors de la mise à jour de la dette.", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'enregistrement de la dette: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private async void BtnMakePayment_Click(object sender, EventArgs e)
        {
            try
            {
                if (_debtId <= 0)
                {
                    KryptonMessageBox.Show("Veuillez d'abord enregistrer la dette avant d'effectuer un paiement.", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                decimal remainingAmount = _debt.Amount - _debt.PaidAmount;
                if (remainingAmount <= 0)
                {
                    KryptonMessageBox.Show("Cette dette est déjà entièrement payée.", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                // Show payment form
                using (var paymentForm = new KryptonForm())
                {
                    paymentForm.Text = "Effectuer un paiement";
                    paymentForm.Size = new Size(400, 250);
                    paymentForm.StartPosition = FormStartPosition.CenterParent;
                    paymentForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                    paymentForm.MaximizeBox = false;
                    paymentForm.MinimizeBox = false;

                    var amountLabel = new KryptonLabel
                    {
                        Text = "Montant à payer:",
                        Location = new Point(20, 20),
                        Size = new Size(150, 20)
                    };

                    var amountTextBox = new KryptonTextBox
                    {
                        Text = remainingAmount.ToString("F2"),
                        Location = new Point(180, 20),
                        Size = new Size(180, 25)
                    };

                    var methodLabel = new KryptonLabel
                    {
                        Text = "Méthode de paiement:",
                        Location = new Point(20, 60),
                        Size = new Size(150, 20)
                    };

                    var methodComboBox = new KryptonComboBox
                    {
                        Location = new Point(180, 60),
                        Size = new Size(180, 25),
                        DropDownWidth = 180
                    };
                    methodComboBox.Items.AddRange(new object[] { "Espèces", "Carte bancaire", "Chèque", "Virement" });
                    methodComboBox.SelectedIndex = 0;

                    var notesLabel = new KryptonLabel
                    {
                        Text = "Notes:",
                        Location = new Point(20, 100),
                        Size = new Size(150, 20)
                    };

                    var notesTextBox = new KryptonTextBox
                    {
                        Location = new Point(180, 100),
                        Size = new Size(180, 25),
                        Multiline = true,
                        Height = 60
                    };

                    var okButton = new KryptonButton
                    {
                        Text = "OK",
                        Location = new Point(180, 180),
                        Size = new Size(80, 30),
                        DialogResult = DialogResult.OK
                    };

                    var cancelButton = new KryptonButton
                    {
                        Text = "Annuler",
                        Location = new Point(280, 180),
                        Size = new Size(80, 30),
                        DialogResult = DialogResult.Cancel
                    };

                    paymentForm.Controls.Add(amountLabel);
                    paymentForm.Controls.Add(amountTextBox);
                    paymentForm.Controls.Add(methodLabel);
                    paymentForm.Controls.Add(methodComboBox);
                    paymentForm.Controls.Add(notesLabel);
                    paymentForm.Controls.Add(notesTextBox);
                    paymentForm.Controls.Add(okButton);
                    paymentForm.Controls.Add(cancelButton);
                    paymentForm.AcceptButton = okButton;
                    paymentForm.CancelButton = cancelButton;

                    if (paymentForm.ShowDialog() == DialogResult.OK)
                    {
                        // Get payment amount
                        if (!decimal.TryParse(amountTextBox.Text, out decimal paymentAmount))
                        {
                            KryptonMessageBox.Show("Veuillez entrer un montant valide.", "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                            return;
                        }

                        if (paymentAmount <= 0 || paymentAmount > remainingAmount)
                        {
                            KryptonMessageBox.Show($"Veuillez entrer un montant entre 0 et {remainingAmount:C}.", "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                            return;
                        }

                        // Get payment method
                        string paymentMethod = methodComboBox.SelectedItem.ToString();

                        // Get notes
                        string notes = notesTextBox.Text;

                        // Update debt
                        bool success = await _cmd.UpdatePaymentAsync(_debtId, paymentAmount);
                        if (success)
                        {
                            KryptonMessageBox.Show($"Paiement de {paymentAmount:C} effectué avec succès.", "Succès",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                            // Reload debt
                            await LoadDebt();
                        }
                        else
                        {
                            KryptonMessageBox.Show("Erreur lors de l'enregistrement du paiement.", "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du paiement: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
    }
}
