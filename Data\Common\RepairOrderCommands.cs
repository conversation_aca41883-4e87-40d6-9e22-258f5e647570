﻿﻿using Dapper;
using IRepairIT.Enums;
using IRepairIT.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace IRepairIT.Data.Common
{
    public class RepairOrderCommands
    {
        private readonly DataAccess _db;

        public RepairOrderCommands()
        {
            _db = new DataAccess();
        }

        public async Task<int> GetCount(string searchTerm)
        {
            const string sql = @"repair_orders ro
                JOIN customers c ON ro.customer_id = c.id
                JOIN devices d ON ro.device_id = d.id
                LEFT JOIN users u ON ro.technician_id = u.id
                WHERE (ro.order_number LIKE CONCAT('%', @searchTerm, '%')
                OR c.name LIKE CONCAT('%', @searchTerm, '%')
                OR d.brand LIKE CONCAT('%', @searchTerm, '%')
                OR d.model LIKE CONCAT('%', @searchTerm, '%')
                OR u.full_name LIKE CONCAT('%', @searchTerm, '%'))";

            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            return await _db.CountQuery(sql, parameters);
        }

        public async Task<IEnumerable<dynamic>> GetAll(string searchTerm, int offset, int pageSize)
        {
            const string sql = @"
                SELECT
                    ro.id,
                    ro.order_number,
                    c.id as customer_id,
                    c.name as client,
                    d.id as device_id,
                    CONCAT(d.brand, ' ', d.model) as device,
                    ro.status,
                    ro.total_cost,
                    ro.payment_status,
                    ro.created_at as date_creation,
                    u.full_name as technician
                FROM repair_orders ro
                JOIN customers c ON ro.customer_id = c.id
                JOIN devices d ON ro.device_id = d.id
                LEFT JOIN users u ON ro.technician_id = u.id
                WHERE (ro.order_number LIKE CONCAT('%', @searchTerm, '%')
                    OR c.name LIKE CONCAT('%', @searchTerm, '%')
                    OR d.brand LIKE CONCAT('%', @searchTerm, '%')
                    OR d.model LIKE CONCAT('%', @searchTerm, '%')
                    OR u.full_name LIKE CONCAT('%', @searchTerm, '%'))
                ORDER BY ro.created_at DESC
                LIMIT @pageSize OFFSET @offset";

            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            parameters.Add("offset", offset);
            parameters.Add("pageSize", pageSize);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        public async Task<RepairOrder> GetByIdAsync(int id)
        {
            try
            {
                Console.WriteLine($"RepairOrderCommands.GetByIdAsync called with id={id}");

                const string sql = @"
                    SELECT
                        ro.id as Id,
                        ro.order_number as OrderNumber,
                        ro.device_id as DeviceId,
                        ro.customer_id as CustomerId,
                        ro.status as Status,
                        ro.total_cost as TotalCost,
                        ro.service_cost as ServiceCost,
                        ro.part_cost as PartCost,
                        ro.paid_amount as PaidAmount,
                        ro.payment_status as PaymentStatus,
                        ro.payment_method as PaymentMethod,
                        ro.technician_id as TechnicianId,
                        ro.warranty_period as WarrantyPeriod,
                        ro.repair_notes as RepairNotes,
                        ro.technical_notes as TechnicalNotes,
                        ro.created_at as CreatedAt,
                        ro.updated_at as UpdatedAt,
                        c.name as CustomerName,
                        CONCAT(d.brand, ' ', d.model) as DeviceName,
                        u.full_name as TechnicianName
                    FROM repair_orders ro
                    LEFT JOIN customers c ON ro.customer_id = c.id
                    LEFT JOIN devices d ON ro.device_id = d.id
                    LEFT JOIN users u ON ro.technician_id = u.id
                    WHERE ro.id = @Id";

                Console.WriteLine("Executing SQL query");
                var result = await _db.QuerySingleOrDefaultQuery<RepairOrder>(sql, new { Id = id });

                Console.WriteLine($"Query result: {(result != null ? "object returned" : "null")}");

                if (result != null)
                {
                    Console.WriteLine($"RepairOrder details: ID={result.Id}, OrderNumber={result.OrderNumber}, Status={result.Status}");
                    Console.WriteLine($"CustomerId={result.CustomerId}, DeviceId={result.DeviceId}, TechnicianId={result.TechnicianId}");
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in RepairOrderCommands.GetByIdAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<string> GenerateOrderNumber()
        {
            // Format: RO-YYYYMMDD-XXXX (RO-20240421-0001)
            string datePrefix = DateTime.Now.ToString("yyyyMMdd");

            const string sql = @"
                SELECT MAX(SUBSTRING_INDEX(order_number, '-', -1))
                FROM repair_orders
                WHERE order_number LIKE CONCAT('RO-', @datePrefix, '-%')";

            var parameters = new DynamicParameters();
            parameters.Add("datePrefix", datePrefix);

            var maxNumber = await _db.ExecuteScalerQuery<string>(sql, parameters);

            int nextNumber = 1;
            if (!string.IsNullOrEmpty(maxNumber) && int.TryParse(maxNumber, out int currentNumber))
            {
                nextNumber = currentNumber + 1;
            }

            return $"RO-{datePrefix}-{nextNumber:D4}";
        }

        public async Task<int> InsertAsync(RepairOrder repairOrder)
        {
            try
            {
                Console.WriteLine("RepairOrderCommands.InsertAsync called");
                Console.WriteLine($"RepairOrder details: OrderNumber={repairOrder.OrderNumber}, Status={repairOrder.Status}");
                Console.WriteLine($"CustomerId={repairOrder.CustomerId}, DeviceId={repairOrder.DeviceId}, TechnicianId={repairOrder.TechnicianId}");

                const string sql = @"
                    INSERT INTO repair_orders (
                        order_number, device_id, customer_id, status,
                        total_cost, service_cost, part_cost, paid_amount,
                        payment_status, payment_method, technician_id,
                        warranty_period, repair_notes, technical_notes, created_at
                    ) VALUES (
                        @OrderNumber, @DeviceId, @CustomerId, @Status,
                        @TotalCost, @ServiceCost, @PartCost, @PaidAmount,
                        @PaymentStatus, @PaymentMethod, @TechnicianId,
                        @WarrantyPeriod, @RepairNotes, @TechnicalNotes, @CreatedAt
                    )";

                if (string.IsNullOrEmpty(repairOrder.OrderNumber))
                {
                    repairOrder.OrderNumber = await GenerateOrderNumber();
                    Console.WriteLine($"Generated new order number: {repairOrder.OrderNumber}");
                }

                repairOrder.CreatedAt = DateTime.Now;
                Console.WriteLine($"Set repairOrder.CreatedAt = {repairOrder.CreatedAt}");

                Console.WriteLine("Executing SQL query");
                int newId = await _db.InsertAndGetIdAsync(sql, repairOrder);
                Console.WriteLine($"InsertAndGetIdAsync result: newId={newId}");

                return newId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in RepairOrderCommands.InsertAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<bool> UpdateAsync(RepairOrder repairOrder)
        {
            try
            {
                Console.WriteLine($"RepairOrderCommands.UpdateAsync called with repairOrder.Id={repairOrder.Id}");
                Console.WriteLine($"RepairOrder details: OrderNumber={repairOrder.OrderNumber}, Status={repairOrder.Status}");
                Console.WriteLine($"CustomerId={repairOrder.CustomerId}, DeviceId={repairOrder.DeviceId}, TechnicianId={repairOrder.TechnicianId}");

                const string sql = @"
                    UPDATE repair_orders
                    SET device_id = @DeviceId,
                        customer_id = @CustomerId,
                        status = @Status,
                        total_cost = @TotalCost,
                        service_cost = @ServiceCost,
                        part_cost = @PartCost,
                        paid_amount = @PaidAmount,
                        payment_status = @PaymentStatus,
                        payment_method = @PaymentMethod,
                        technician_id = @TechnicianId,
                        warranty_period = @WarrantyPeriod,
                        repair_notes = @RepairNotes,
                        technical_notes = @TechnicalNotes,
                        updated_at = @UpdatedAt
                    WHERE id = @Id";

                repairOrder.UpdatedAt = DateTime.Now;
                Console.WriteLine($"Set repairOrder.UpdatedAt = {repairOrder.UpdatedAt}");

                Console.WriteLine("Executing SQL query");
                int rowsAffected = await _db.ExecuteQuery(sql, repairOrder);
                Console.WriteLine($"ExecuteQuery result: rowsAffected={rowsAffected}");

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in RepairOrderCommands.UpdateAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                // Check if the repair order has services
                const string checkServicesSql = @"SELECT COUNT(*) FROM repair_order_services WHERE repair_order_id = @Id";
                int servicesCount = await _db.ExecuteScalerQuery<int>(checkServicesSql, new { Id = id });

                if (servicesCount > 0)
                {
                    throw new Exception($"Impossible de supprimer cet ordre de réparation car il contient {servicesCount} service(s). Veuillez d'abord supprimer les services associés.");
                }

                // Check if the repair order has parts
                const string checkPartsSql = @"SELECT COUNT(*) FROM repair_order_parts WHERE repair_order_id = @Id";
                int partsCount = await _db.ExecuteScalerQuery<int>(checkPartsSql, new { Id = id });

                if (partsCount > 0)
                {
                    throw new Exception($"Impossible de supprimer cet ordre de réparation car il contient {partsCount} pièce(s). Veuillez d'abord supprimer les pièces associées.");
                }

                // Check if the repair order has payments
                const string checkPaymentsSql = @"SELECT COUNT(*) FROM payments WHERE repair_order_id = @Id";
                int paymentsCount = await _db.ExecuteScalerQuery<int>(checkPaymentsSql, new { Id = id });

                if (paymentsCount > 0)
                {
                    throw new Exception($"Impossible de supprimer cet ordre de réparation car il contient {paymentsCount} paiement(s). Veuillez d'abord supprimer les paiements associés.");
                }

                // Check if the repair order has status history
                const string checkHistorySql = @"SELECT COUNT(*) FROM status_history WHERE repair_order_id = @Id";
                int historyCount = await _db.ExecuteScalerQuery<int>(checkHistorySql, new { Id = id });

                // Delete status history if exists (this is safe to delete)
                if (historyCount > 0)
                {
                    const string deleteHistorySql = @"DELETE FROM status_history WHERE repair_order_id = @Id";
                    await _db.ExecuteQuery(deleteHistorySql, new { Id = id });
                }

                // Delete the repair order
                const string sql = @"DELETE FROM repair_orders WHERE id = @Id";
                int rowsAffected = await _db.ExecuteQuery(sql, new { Id = id });
                return rowsAffected > 0;
            }
            catch
            {
                throw;
            }
        }

        public async Task<bool> DeleteWithDependenciesAsync(int id)
        {
            try
            {
                using (var connection = new MySql.Data.MySqlClient.MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
                {
                    await connection.OpenAsync();
                    using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Delete services
                        const string deleteServicesSql = @"DELETE FROM repair_order_services WHERE repair_order_id = @Id";
                        await _db.ExecuteQuery(deleteServicesSql, new { Id = id }, transaction);

                        // Delete parts
                        const string deletePartsSql = @"DELETE FROM repair_order_parts WHERE repair_order_id = @Id";
                        await _db.ExecuteQuery(deletePartsSql, new { Id = id }, transaction);

                        // Delete payments
                        const string deletePaymentsSql = @"DELETE FROM payments WHERE repair_order_id = @Id";
                        await _db.ExecuteQuery(deletePaymentsSql, new { Id = id }, transaction);

                        // Delete status history
                        const string deleteHistorySql = @"DELETE FROM status_history WHERE repair_order_id = @Id";
                        await _db.ExecuteQuery(deleteHistorySql, new { Id = id }, transaction);

                        // Delete the repair order
                        const string deleteOrderSql = @"DELETE FROM repair_orders WHERE id = @Id";
                        int rowsAffected = await _db.ExecuteQuery(deleteOrderSql, new { Id = id }, transaction);

                        transaction.Commit();
                        return rowsAffected > 0;
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
                }
            }
            catch
            {
                throw;
            }
        }

        public async Task<bool> UpdateStatusAsync(int id, RepairOrderStatus status, string notes = null, int? userId = null)
        {
            const string updateSql = @"
                UPDATE repair_orders
                SET status = @Status,
                    updated_at = @UpdatedAt
                WHERE id = @Id";

            const string historySql = @"
                INSERT INTO status_history (
                    repair_order_id, status, notes, user_id, created_at
                ) VALUES (
                    @RepairOrderId, @Status, @Notes, @UserId, @CreatedAt
                )";

            using (var connection = new MySql.Data.MySqlClient.MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
            {
                try
                {
                    // Update repair order status
                    await _db.ExecuteQuery(updateSql, new
                    {
                        Id = id,
                        Status = status,
                        UpdatedAt = DateTime.Now
                    }, transaction);

                    // Add status history entry
                    await _db.ExecuteQuery(historySql, new
                    {
                        RepairOrderId = id,
                        Status = status.ToString(),
                        Notes = notes,
                        UserId = userId,
                        CreatedAt = DateTime.Now
                    }, transaction);

                    transaction.Commit();
                    return true;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            }
        }

        public async Task<IEnumerable<RepairOrderService>> GetServicesAsync(int repairOrderId)
        {
            try
            {
                Console.WriteLine($"GetServicesAsync called with repairOrderId={repairOrderId}");

                const string sql = @"
                    SELECT
                        ros.id,
                        ros.repair_order_id as RepairOrderId,
                        ros.service_id as ServiceId,
                        ros.price as Price,
                        ros.created_at as CreatedAt,
                        s.id as 'Service.id',
                        s.name as 'Service.name',
                        s.description as 'Service.description',
                        s.price as 'Service.price',
                        s.duration as 'Service.duration',
                        s.created_at as 'Service.created_at',
                        s.updated_at as 'Service.updated_at'
                    FROM repair_order_services ros
                    JOIN services s ON ros.service_id = s.id
                    WHERE ros.repair_order_id = @RepairOrderId";

                var result = await _db.QueryQuery<RepairOrderService>(sql, new { RepairOrderId = repairOrderId });

                // Ensure Service property is initialized for each item
                var servicesList = result;
                foreach (var service in servicesList)
                {
                    if (service.Service == null)
                    {
                        Console.WriteLine($"Service property is null for service ID {service.Id}, initializing it");

                        // Get the service by ID
                        var serviceCmd = new ServiceCommands();
                        service.Service = await serviceCmd.GetByIdAsync(service.ServiceId);

                        if (service.Service == null)
                        {
                            Console.WriteLine($"Failed to get service with ID {service.ServiceId}");
                        }
                    }
                }

                return servicesList;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in GetServicesAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<int> AddServiceAsync(RepairOrderService service)
        {
            const string sql = @"
                INSERT INTO repair_order_services (
                    repair_order_id, service_id, price, created_at
                ) VALUES (
                    @RepairOrderId, @ServiceId, @Price, @CreatedAt
                )";

            service.CreatedAt = DateTime.Now;

            return await _db.InsertAndGetIdAsync(sql, service);
        }

        public async Task<bool> RemoveServiceAsync(int serviceId)
        {
            const string sql = @"DELETE FROM repair_order_services WHERE id = @Id";
            int rowsAffected = await _db.ExecuteQuery(sql, new { Id = serviceId });
            return rowsAffected > 0;
        }

        public async Task<bool> UpdateServiceAsync(int serviceId, decimal price)
        {
            try
            {
                Console.WriteLine($"UpdateServiceAsync called with serviceId={serviceId}, price={price}");

                const string sql = @"
                    UPDATE repair_order_services
                    SET price = @Price,
                        updated_at = @UpdatedAt
                    WHERE id = @Id";

                int rowsAffected = await _db.ExecuteQuery(sql, new
                {
                    Id = serviceId,
                    Price = price,
                    UpdatedAt = DateTime.Now
                });

                Console.WriteLine($"UpdateServiceAsync affected {rowsAffected} rows");
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in UpdateServiceAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<IEnumerable<RepairOrderPart>> GetPartsAsync(int repairOrderId)
        {
            try
            {
                Console.WriteLine($"GetPartsAsync called with repairOrderId={repairOrderId}");

                const string sql = @"
                    SELECT
                        rop.id,
                        rop.repair_order_id as RepairOrderId,
                        rop.part_id as PartId,
                        rop.quantity as Quantity,
                        rop.price as Price,
                        rop.created_at as CreatedAt,
                        p.id as 'Part.Id',
                        p.name as 'Part.Name',
                        p.code as 'Part.Code',
                        p.description as 'Part.Description',
                        p.purchase_price as 'Part.Purchase_Price',
                        p.selling_price as 'Part.Selling_Price',
                        p.quantity as 'Part.Quantity',
                        p.category as 'Part.Category',
                        p.created_at as 'Part.CreatedAt',
                        p.updated_at as 'Part.UpdatedAt'
                    FROM repair_order_parts rop
                    JOIN parts p ON rop.part_id = p.id
                    WHERE rop.repair_order_id = @RepairOrderId";

                var result = await _db.QueryQuery<RepairOrderPart>(sql, new { RepairOrderId = repairOrderId });

                // Ensure Part property is initialized for each item
                var partsList = result;
                foreach (var part in partsList)
                {
                    if (part.Part == null)
                    {
                        Console.WriteLine($"Part property is null for part ID {part.Id}, initializing it");

                        // Get the part by ID
                        var partCmd = new PartCommands();
                        part.Part = await partCmd.GetByIdAsync(part.PartId);

                        if (part.Part == null)
                        {
                            Console.WriteLine($"Failed to get part with ID {part.PartId}");
                        }
                    }
                }

                Console.WriteLine($"GetPartsAsync returning {partsList.Count()} parts");
                return partsList;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in GetPartsAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<int> AddPartAsync(RepairOrderPart part)
        {
            const string sql = @"
                INSERT INTO repair_order_parts (
                    repair_order_id, part_id, quantity, price, created_at
                ) VALUES (
                    @RepairOrderId, @PartId, @Quantity, @Price, @CreatedAt
                )";

            part.CreatedAt = DateTime.Now;

            return await _db.InsertAndGetIdAsync(sql, part);
        }

        public async Task<bool> RemovePartAsync(int partId)
        {
            const string sql = @"DELETE FROM repair_order_parts WHERE id = @Id";
            int rowsAffected = await _db.ExecuteQuery(sql, new { Id = partId });
            return rowsAffected > 0;
        }

        public async Task<bool> UpdatePartAsync(int partId, int quantity, decimal price)
        {
            try
            {
                Console.WriteLine($"UpdatePartAsync called with partId={partId}, quantity={quantity}, price={price}");

                const string sql = @"
                    UPDATE repair_order_parts
                    SET quantity = @Quantity,
                        price = @Price,
                        updated_at = @UpdatedAt
                    WHERE id = @Id";

                int rowsAffected = await _db.ExecuteQuery(sql, new
                {
                    Id = partId,
                    Quantity = quantity,
                    Price = price,
                    UpdatedAt = DateTime.Now
                });

                Console.WriteLine($"UpdatePartAsync affected {rowsAffected} rows");
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in UpdatePartAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<IEnumerable<Payment>> GetPaymentsAsync(int repairOrderId)
        {
            const string sql = @"
                SELECT p.*, u.full_name as UserName
                FROM payments p
                LEFT JOIN users u ON p.user_id = u.id
                WHERE p.repair_order_id = @RepairOrderId
                ORDER BY p.created_at DESC";

            return await _db.QueryQuery<Payment>(sql, new { RepairOrderId = repairOrderId });
        }

        public async Task<int> AddPaymentAsync(Payment payment)
        {
            const string sql = @"
                INSERT INTO payments (
                    repair_order_id, amount, payment_method, notes, user_id, created_at
                ) VALUES (
                    @RepairOrderId, @Amount, @PaymentMethod, @Notes, @UserId, @CreatedAt
                )";

            payment.CreatedAt = DateTime.Now;

            // Update repair order paid amount and payment status
            const string updateSql = @"
                UPDATE repair_orders
                SET paid_amount = paid_amount + @Amount,
                    payment_status = CASE
                        WHEN (paid_amount + @Amount) >= total_cost THEN 'paid'
                        WHEN (paid_amount + @Amount) > 0 THEN 'partially_paid'
                        ELSE 'unpaid'
                    END,
                    updated_at = @UpdatedAt
                WHERE id = @RepairOrderId";

            using (var connection = new MySql.Data.MySqlClient.MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
            {
                try
                {
                    // Add payment
                    int paymentId = await _db.InsertAndGetIdAsync(sql, payment, transaction);

                    // Update repair order
                    await _db.ExecuteQuery(updateSql, new
                    {
                        RepairOrderId = payment.RepairOrderId,
                        Amount = payment.Amount,
                        UpdatedAt = DateTime.Now
                    }, transaction);

                    transaction.Commit();
                    return paymentId;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            }
        }

        public async Task<IEnumerable<StatusHistory>> GetStatusHistoryAsync(int repairOrderId)
        {
            const string sql = @"
                SELECT sh.*, u.full_name as UserName
                FROM status_history sh
                LEFT JOIN users u ON sh.user_id = u.id
                WHERE sh.repair_order_id = @RepairOrderId
                ORDER BY sh.created_at DESC";

            return await _db.QueryQuery<StatusHistory>(sql, new { RepairOrderId = repairOrderId });
        }

        public async Task<IEnumerable<RepairOrder>> GetByCustomerIdAsync(int customerId)
        {
            const string sql = @"
                SELECT
                    ro.id as Id,
                    ro.order_number as OrderNumber,
                    ro.device_id as DeviceId,
                    ro.customer_id as CustomerId,
                    ro.status as Status,
                    ro.total_cost as TotalCost,
                    ro.service_cost as ServiceCost,
                    ro.part_cost as PartCost,
                    ro.paid_amount as PaidAmount,
                    ro.payment_status as PaymentStatus,
                    ro.payment_method as PaymentMethod,
                    ro.technician_id as TechnicianId,
                    ro.warranty_period as WarrantyPeriod,
                    ro.repair_notes as RepairNotes,
                    ro.technical_notes as TechnicalNotes,
                    ro.created_at as CreatedAt,
                    ro.updated_at as UpdatedAt,
                    c.name as CustomerName,
                    CONCAT(d.brand, ' ', d.model) as DeviceName,
                    u.full_name as TechnicianName
                FROM repair_orders ro
                LEFT JOIN customers c ON ro.customer_id = c.id
                LEFT JOIN devices d ON ro.device_id = d.id
                LEFT JOIN users u ON ro.technician_id = u.id
                WHERE ro.customer_id = @CustomerId
                ORDER BY ro.created_at DESC";

            return await _db.QueryQuery<RepairOrder>(sql, new { CustomerId = customerId });
        }

        public async Task<IEnumerable<dynamic>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT
                    ro.id,
                    ro.order_number,
                    c.name as customer_name,
                    CONCAT(d.brand, ' ', d.model) as device_name,
                    ro.status,
                    ro.total_cost,
                    ro.paid_amount,
                    ro.payment_status,
                    ro.created_at,
                    ro.updated_at,
                    u.full_name as technician_name
                FROM repair_orders ro
                LEFT JOIN customers c ON ro.customer_id = c.id
                LEFT JOIN devices d ON ro.device_id = d.id
                LEFT JOIN users u ON ro.technician_id = u.id
                WHERE ro.created_at BETWEEN @startDate AND @endDate
                ORDER BY ro.created_at DESC";

            var parameters = new DynamicParameters();
            parameters.Add("startDate", startDate);
            parameters.Add("endDate", endDate);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        public async Task<IEnumerable<dynamic>> GetMonthlyRepairsAsync(int year)
        {
            const string sql = @"
                SELECT
                    MONTH(ro.created_at) as month,
                    CASE
                        WHEN MONTH(ro.created_at) = 1 THEN 'Janvier'
                        WHEN MONTH(ro.created_at) = 2 THEN 'Février'
                        WHEN MONTH(ro.created_at) = 3 THEN 'Mars'
                        WHEN MONTH(ro.created_at) = 4 THEN 'Avril'
                        WHEN MONTH(ro.created_at) = 5 THEN 'Mai'
                        WHEN MONTH(ro.created_at) = 6 THEN 'Juin'
                        WHEN MONTH(ro.created_at) = 7 THEN 'Juillet'
                        WHEN MONTH(ro.created_at) = 8 THEN 'Août'
                        WHEN MONTH(ro.created_at) = 9 THEN 'Septembre'
                        WHEN MONTH(ro.created_at) = 10 THEN 'Octobre'
                        WHEN MONTH(ro.created_at) = 11 THEN 'Novembre'
                        WHEN MONTH(ro.created_at) = 12 THEN 'Décembre'
                    END as month_name,
                    COUNT(*) as repair_count,
                    SUM(ro.total_cost) as total_amount,
                    SUM(ro.paid_amount) as paid_amount,
                    SUM(ro.total_cost - ro.paid_amount) as unpaid_amount
                FROM repair_orders ro
                WHERE YEAR(ro.created_at) = @year
                GROUP BY MONTH(ro.created_at)
                ORDER BY MONTH(ro.created_at)";

            var parameters = new DynamicParameters();
            parameters.Add("year", year);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        public async Task<IEnumerable<dynamic>> GetMonthlyRevenueAsync(int year)
        {
            const string sql = @"
                SELECT
                    MONTH(ro.created_at) as month,
                    CASE
                        WHEN MONTH(ro.created_at) = 1 THEN 'Janvier'
                        WHEN MONTH(ro.created_at) = 2 THEN 'Février'
                        WHEN MONTH(ro.created_at) = 3 THEN 'Mars'
                        WHEN MONTH(ro.created_at) = 4 THEN 'Avril'
                        WHEN MONTH(ro.created_at) = 5 THEN 'Mai'
                        WHEN MONTH(ro.created_at) = 6 THEN 'Juin'
                        WHEN MONTH(ro.created_at) = 7 THEN 'Juillet'
                        WHEN MONTH(ro.created_at) = 8 THEN 'Août'
                        WHEN MONTH(ro.created_at) = 9 THEN 'Septembre'
                        WHEN MONTH(ro.created_at) = 10 THEN 'Octobre'
                        WHEN MONTH(ro.created_at) = 11 THEN 'Novembre'
                        WHEN MONTH(ro.created_at) = 12 THEN 'Décembre'
                    END as month_name,
                    SUM(ro.total_cost) as repair_revenue,
                    SUM(ro.part_cost) as parts_revenue,
                    SUM(ro.service_cost) as service_revenue,
                    SUM(ro.total_cost) as total_revenue,
                    0 as expenses,
                    SUM(ro.total_cost) as profit
                FROM repair_orders ro
                WHERE YEAR(ro.created_at) = @year
                GROUP BY MONTH(ro.created_at)
                ORDER BY MONTH(ro.created_at)";

            var parameters = new DynamicParameters();
            parameters.Add("year", year);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        public async Task<IEnumerable<dynamic>> GetTotalOrdersAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT
                    DATE(ro.created_at) as order_date,
                    COUNT(*) as order_count,
                    SUM(ro.total_cost) as total_amount,
                    SUM(ro.paid_amount) as paid_amount,
                    SUM(ro.total_cost - ro.paid_amount) as unpaid_amount,
                    SUM(ro.service_cost) as service_amount,
                    SUM(ro.part_cost) as part_amount
                FROM repair_orders ro
                WHERE ro.created_at BETWEEN @startDate AND @endDate
                GROUP BY DATE(ro.created_at)
                ORDER BY DATE(ro.created_at)";

            var parameters = new DynamicParameters();
            parameters.Add("startDate", startDate);
            parameters.Add("endDate", endDate);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        public async Task<IEnumerable<dynamic>> GetSalesDataAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT
                    ro.id,
                    ro.order_number,
                    ro.created_at as order_date,
                    c.name as customer_name,
                    CONCAT(d.brand, ' ', d.model) as device_name,
                    ro.status,
                    ro.total_cost,
                    ro.service_cost,
                    ro.part_cost,
                    ro.paid_amount,
                    ro.payment_status,
                    ro.payment_method,
                    u.full_name as technician_name
                FROM repair_orders ro
                LEFT JOIN customers c ON ro.customer_id = c.id
                LEFT JOIN devices d ON ro.device_id = d.id
                LEFT JOIN users u ON ro.technician_id = u.id
                WHERE ro.created_at BETWEEN @startDate AND @endDate
                ORDER BY ro.created_at DESC";

            var parameters = new DynamicParameters();
            parameters.Add("startDate", startDate);
            parameters.Add("endDate", endDate);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        public async Task<decimal> CalculateTotalCostAsync(int repairOrderId)
        {
            // Calculate service cost
            const string serviceSql = @"
                SELECT COALESCE(SUM(price), 0)
                FROM repair_order_services
                WHERE repair_order_id = @RepairOrderId";

            // Calculate part cost
            const string partSql = @"
                SELECT COALESCE(SUM(price * quantity), 0)
                FROM repair_order_parts
                WHERE repair_order_id = @RepairOrderId";

            var serviceCost = await _db.ExecuteScalerQuery<decimal>(serviceSql, new { RepairOrderId = repairOrderId });
            var partCost = await _db.ExecuteScalerQuery<decimal>(partSql, new { RepairOrderId = repairOrderId });
            var totalCost = serviceCost + partCost;

            // Update repair order costs
            const string updateSql = @"
                UPDATE repair_orders
                SET service_cost = @ServiceCost,
                    part_cost = @PartCost,
                    total_cost = @TotalCost,
                    payment_status = CASE
                        WHEN paid_amount >= @TotalCost THEN 'paid'
                        WHEN paid_amount > 0 THEN 'partially_paid'
                        ELSE 'unpaid'
                    END,
                    updated_at = @UpdatedAt
                WHERE id = @RepairOrderId";

            await _db.ExecuteQuery(updateSql, new
            {
                RepairOrderId = repairOrderId,
                ServiceCost = serviceCost,
                PartCost = partCost,
                TotalCost = totalCost,
                UpdatedAt = DateTime.Now
            });

            return totalCost;
        }
    }
}
