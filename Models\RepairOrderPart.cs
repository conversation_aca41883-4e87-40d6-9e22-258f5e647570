﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class RepairOrderPart
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Identifiant de la commande de réparation")]
        public int RepairOrderId { get; set; }

        [Display(Name = "Identifiant de la pièce")]
        public int PartId { get; set; }

        [Display(Name = "Quantité")]
        public int Quantity { get; set; }

        [Display(Name = "Prix")]
        public decimal Price { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        // Navigation properties
        [Display(Name = "Commande de réparation")]
        public RepairOrder RepairOrder { get; set; }

        [Display(Name = "Pièce")]
        public Part Part { get; set; }
    }
}
