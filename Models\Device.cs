﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class Device
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Identifiant du client")]
        public int CustomerId { get; set; }

        [Display(Name = "Type")]
        public string Type { get; set; }

        [Display(Name = "Marque")]
        public string Brand { get; set; }

        [Display(Name = "Modèle")]
        public string Model { get; set; }

        [Display(Name = "Numéro de série")]
        public string SerialNumber { get; set; }

        [Display(Name = "IMEI")]
        public string IMEI { get; set; }

        [Display(Name = "Problème")]
        public string Problem { get; set; }

        [Display(Name = "État")]
        public string Condition { get; set; }

        [Display(Name = "Mot de passe")]
        public string Password { get; set; }

        [Display(Name = "Accessoires")]
        public string Accessories { get; set; }

        [Display(Name = "Date de réception")]
        public DateTime ReceiptDate { get; set; }

        [Display(Name = "Date de livraison prévue")]
        public DateTime? ExpectedDeliveryDate { get; set; }

        [Display(Name = "Date de livraison")]
        public DateTime? DeliveryDate { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Date de mise à jour")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation property
        [Display(Name = "Client")]
        public Customer Customer { get; set; }
    }
}
