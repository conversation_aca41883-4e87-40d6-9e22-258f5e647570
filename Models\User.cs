﻿using IRepairIT.Enums;
using IRepairIT.utilities;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
namespace IRepairIT.Models
{
    public class User : EntityValidator
    {
        [DisplayName("#")]
        public int Id { get; set; }

        [DisplayName("Nom d'utilisateur"),Required(ErrorMessage = "Le champ [ Nom d'utilisateur ] doit être renseigné")]
        public string Username { get; set; }

        [DisplayName("Mot de passe"), Required(ErrorMessage = "Le champ [ Mot de passe ] doit être renseigné")]
        public string Password { get; set; }

        [DisplayName("Nom complet"), Required(ErrorMessage = "Le champ [ Nom complet ] doit être renseigné")]
        public string Full_Name { get; set; }

        [DisplayName("Email"),EmailAddress, Required(ErrorMessage = "Le champ [ Email ] doit être renseigné")]
        public string Email { get; set; }

        [DisplayName("Téléphone"),Phone]
        public string Phone { get; set; }

        [DisplayName("Rôle")]
        public UserRoles Role { get; set; }

        [DisplayName("Statut")]
        public UserStatus Status { get; set; }

        [DisplayName("Date de création")]
        public DateTime CreatedAt { get; set; }

        [DisplayName("Date de mise à jour")]
        public DateTime? UpdatedAt { get; set; }
    }
}
