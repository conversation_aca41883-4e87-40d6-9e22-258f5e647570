using IRepairIT.Data.Common;
using IRepairIT.Models;
using Krypton.Toolkit;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FSERVICES
{
    public partial class FRM_SERVICE_SELECTOR : KryptonForm
    {
        private readonly ServiceCommands _serviceCmd;
        private string _searchTerm = "";
        private int _pageSize = 10;
        private int _currentPage = 1;
        private int _totalRows = 0;

        public int SelectedServiceId { get; private set; }
        public Service SelectedService { get; private set; }
        public string SearchTerm { get; set; }

        public FRM_SERVICE_SELECTOR()
        {
            InitializeComponent();
            _serviceCmd = new ServiceCommands();
            SelectedServiceId = 0;
            SelectedService = null;
        }

        private async void FRM_SERVICE_SELECTOR_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize the grid
                InitializeGrid();

                // Set search term if provided
                if (!string.IsNullOrEmpty(SearchTerm))
                {
                    txtSearch.Text = SearchTerm;
                    _searchTerm = SearchTerm;
                }

                // Load services
                await LoadServices();

                // Set up autocomplete for the search box
                SetupAutocomplete();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void InitializeGrid()
        {
            // Configure the DataGridView
            dgvServices.AutoGenerateColumns = false;
            dgvServices.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvServices.MultiSelect = false;
            dgvServices.ReadOnly = true;
            dgvServices.AllowUserToAddRows = false;
            dgvServices.AllowUserToDeleteRows = false;
            dgvServices.AllowUserToResizeRows = false;
            dgvServices.RowHeadersVisible = false;
            dgvServices.AlternatingRowsDefaultCellStyle.BackColor = System.Drawing.Color.FromArgb(240, 240, 240);

            // Create columns
            DataGridViewTextBoxColumn idColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "id",
                HeaderText = "ID",
                Name = "id",
                Visible = false
            };

            DataGridViewTextBoxColumn nameColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "name",
                HeaderText = "Nom",
                Name = "name",
                Width = 200
            };

            DataGridViewTextBoxColumn descriptionColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "description",
                HeaderText = "Description",
                Name = "description",
                Width = 250
            };

            DataGridViewTextBoxColumn priceColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "price",
                HeaderText = "Prix",
                Name = "price",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
            };

            // Add columns to the grid
            dgvServices.Columns.AddRange(new DataGridViewColumn[] { idColumn, nameColumn, descriptionColumn, priceColumn });

            // Set double-click event
            dgvServices.CellDoubleClick += DgvServices_CellDoubleClick;
        }

        private async Task LoadServices()
        {
            try
            {
                // Calculate offset based on current page and page size
                int offset = (_currentPage - 1) * _pageSize;

                // Get services with pagination
                var services = await _serviceCmd.GetALL(_searchTerm, offset, _pageSize);
                _totalRows = await _serviceCmd.GetCount(_searchTerm);

                // Set data source
                dgvServices.DataSource = services.ToList();

                // Update pagination info
                UpdatePaginationInfo();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des services: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void UpdatePaginationInfo()
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            lblPagination.Text = $"Page {_currentPage} sur {totalPages} (Total: {_totalRows})";

            // Enable/disable pagination buttons
            btnFirst.Enabled = _currentPage > 1;
            btnPrev.Enabled = _currentPage > 1;
            btnNext.Enabled = _currentPage < totalPages;
            btnLast.Enabled = _currentPage < totalPages;
        }

        private async void SetupAutocomplete()
        {
            try
            {
                // Get all services for autocomplete
                var allServices = await _serviceCmd.GetAllForAutocomplete();

                // Create autocomplete source
                AutoCompleteStringCollection autoCompleteSource = new AutoCompleteStringCollection();
                foreach (var service in allServices)
                {
                    autoCompleteSource.Add(service.name);
                }

                // Set up autocomplete for the search box
                txtSearch.AutoCompleteMode = AutoCompleteMode.SuggestAppend;
                txtSearch.AutoCompleteSource = AutoCompleteSource.CustomSource;
                txtSearch.AutoCompleteCustomSource = autoCompleteSource;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting up autocomplete: {ex.Message}");
            }
        }

        private void DgvServices_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                SelectService();
            }
        }

        private void btnSelect_Click(object sender, EventArgs e)
        {
            SelectService();
        }

        private void SelectService()
        {
            if (dgvServices.SelectedRows.Count > 0)
            {
                try
                {
                    // Get the selected service
                    var selectedRow = dgvServices.SelectedRows[0];
                    SelectedService = selectedRow.DataBoundItem as Service;

                    if (SelectedService != null)
                    {
                        SelectedServiceId = SelectedService.id;
                        DialogResult = DialogResult.OK;
                        Close();
                        return;
                    }
                }
                catch (Exception ex)
                {
                    KryptonMessageBox.Show($"Erreur lors de la sélection du service: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    return;
                }
            }

            KryptonMessageBox.Show("Veuillez sélectionner un service", "Sélection requise",
                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private async void btnSearch_Click(object sender, EventArgs e)
        {
            _searchTerm = txtSearch.Text.Trim();
            _currentPage = 1;
            await LoadServices();
        }

        private async void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.SuppressKeyPress = true;
                _searchTerm = txtSearch.Text.Trim();
                _currentPage = 1;
                await LoadServices();
            }
        }

        private async void btnClearSearch_Click(object sender, EventArgs e)
        {
            txtSearch.Text = "";
            _searchTerm = "";
            _currentPage = 1;
            await LoadServices();
        }

        private async void btnFirst_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage = 1;
                await LoadServices();
            }
        }

        private async void btnPrev_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadServices();
            }
        }

        private async void btnNext_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage++;
                await LoadServices();
            }
        }

        private async void btnLast_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage = totalPages;
                await LoadServices();
            }
        }

        private async void btnAddNew_Click(object sender, EventArgs e)
        {
            // Check if there's text in the search box that doesn't match any existing service
            string searchText = txtSearch.Text.Trim();
            if (!string.IsNullOrEmpty(searchText))
            {
                // Ask if the user wants to create a new service with this name
                var result = KryptonMessageBox.Show(
                    $"Voulez-vous créer un nouveau service nommé '{searchText}'?",
                    "Créer un nouveau service",
                    KryptonMessageBoxButtons.YesNo,
                    KryptonMessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Open the service creation form with the name pre-filled
                    var serviceForm = new FRM_SERVICES();
                    serviceForm.ServiceName = searchText;

                    if (serviceForm.ShowDialog() == DialogResult.OK)
                    {
                        // Reload the services list
                        await LoadServices();

                        // Set up autocomplete again with the new service
                        SetupAutocomplete();
                    }
                }
            }
            else
            {
                // Just open the service creation form
                var serviceForm = new FRM_SERVICES();
                if (serviceForm.ShowDialog() == DialogResult.OK)
                {
                    // Reload the services list
                    await LoadServices();

                    // Set up autocomplete again with the new service
                    SetupAutocomplete();
                }
            }
        }
    }
}
