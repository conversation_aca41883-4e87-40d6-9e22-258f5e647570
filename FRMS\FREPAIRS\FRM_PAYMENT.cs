using IRepairIT.Data.Common;
using IRepairIT.Models;
using Krypton.Toolkit;
using System;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FREPAIRS
{
    public partial class FRM_PAYMENT : KryptonForm
    {
        private readonly RepairOrderCommands _cmd;
        private readonly int _repairOrderId;
        private decimal _totalCost;
        private decimal _paidAmount;
        private decimal _remainingAmount;

        public bool PaymentAdded { get; private set; }

        public FRM_PAYMENT(int repairOrderId, decimal totalCost, decimal paidAmount)
        {
            InitializeComponent();

            _cmd = new RepairOrderCommands();
            _repairOrderId = repairOrderId;
            _totalCost = totalCost;
            _paidAmount = paidAmount;
            _remainingAmount = _totalCost - _paidAmount;

            PaymentAdded = false;
        }

        private void FRM_PAYMENT_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize payment method combo box
                InitializePaymentMethodComboBox();

                // Set remaining amount
                lblTotalAmount.Text = $"Montant total: {_totalCost:C2}";
                lblPaidAmount.Text = $"Montant payé: {_paidAmount:C2}";
                lblRemainingAmount.Text = $"Montant restant: {_remainingAmount:C2}";

                // Set default amount to remaining amount
                txtAmount.Text = _remainingAmount.ToString("F2");
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du formulaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void InitializePaymentMethodComboBox()
        {
            // Add payment methods
            cmbPaymentMethod.Items.Clear();
            cmbPaymentMethod.Items.Add("Espèces");
            cmbPaymentMethod.Items.Add("Carte bancaire");
            cmbPaymentMethod.Items.Add("Chèque");
            cmbPaymentMethod.Items.Add("Virement");
            cmbPaymentMethod.Items.Add("Autre");

            // Select first item
            if (cmbPaymentMethod.Items.Count > 0)
            {
                cmbPaymentMethod.SelectedIndex = 0;
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate form
                if (!ValidateForm())
                {
                    return;
                }

                // Create payment object
                var payment = new Payment
                {
                    RepairOrderId = _repairOrderId,
                    Amount = decimal.Parse(txtAmount.Text),
                    PaymentMethod = cmbPaymentMethod.Text,
                    Notes = txtNotes.Text,
                    UserId = Properties.Settings.Default.CurrentUserId > 0 ? Properties.Settings.Default.CurrentUserId : (int?)null
                };

                // Save payment
                int paymentId = await _cmd.AddPaymentAsync(payment);
                if (paymentId > 0)
                {
                    KryptonMessageBox.Show("Le paiement a été ajouté avec succès", "Succès",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    PaymentAdded = true;
                    this.Close();
                }
                else
                {
                    KryptonMessageBox.Show("Erreur lors de l'ajout du paiement", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'enregistrement du paiement: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private bool ValidateForm()
        {
            // Check amount
            if (string.IsNullOrEmpty(txtAmount.Text))
            {
                KryptonMessageBox.Show("Veuillez saisir un montant", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                return false;
            }

            if (!decimal.TryParse(txtAmount.Text, out decimal amount) || amount <= 0)
            {
                KryptonMessageBox.Show("Le montant doit être un nombre positif", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                return false;
            }

            // Check payment method
            if (string.IsNullOrEmpty(cmbPaymentMethod.Text))
            {
                KryptonMessageBox.Show("Veuillez sélectionner une méthode de paiement", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
