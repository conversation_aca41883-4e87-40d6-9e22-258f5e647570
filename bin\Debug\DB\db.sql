﻿-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL,
    `password` varchar(255) NOT NULL,
    `full_name` varchar(100) NOT NULL,
    `email` varchar(100) DEFAULT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `role` enum('admin','technician','cashier','receptionist') NOT NULL DEFAULT 'receptionist',
    `status` enum('active','inactive') NOT NULL DEFAULT 'active',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`),
    UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول العملاء
CREATE TABLE IF NOT EXISTS `customers` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `email` varchar(100) DEFAULT NULL,
    `phone` varchar(20) NOT NULL,
    `address` text DEFAULT NULL,
    `notes` text DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الأجهزة
CREATE TABLE IF NOT EXISTS `devices` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `customer_id` int(11) NOT NULL,
    `type` varchar(50) NOT NULL,
    `brand` varchar(50) NOT NULL,
    `model` varchar(100) NOT NULL,
    `serial_number` varchar(100) DEFAULT NULL,
    `imei` varchar(50) DEFAULT NULL,
    `problem` text NOT NULL,
    `condition` text DEFAULT NULL,
    `password` varchar(100) DEFAULT NULL,
    `accessories` text DEFAULT NULL,
    `receipt_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `expected_delivery_date` datetime DEFAULT NULL,
    `delivery_date` datetime DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `customer_id` (`customer_id`),
    CONSTRAINT `devices_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول صور الأجهزة
CREATE TABLE IF NOT EXISTS `device_images` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `device_id` int(11) NOT NULL,
    `image_path` varchar(255) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `device_id` (`device_id`),
    CONSTRAINT `device_images_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قطع الغيار
CREATE TABLE IF NOT EXISTS `parts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `code` varchar(50) DEFAULT NULL,
    `description` text DEFAULT NULL,
    `purchase_price` decimal(10,2) NOT NULL,
    `selling_price` decimal(10,2) NOT NULL,
    `quantity` int(11) NOT NULL DEFAULT 0,
    `min_quantity` int(11) NOT NULL DEFAULT 1,
    `category` varchar(50) DEFAULT NULL,
    `supplier` varchar(100) DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الخدمات
CREATE TABLE IF NOT EXISTS `services` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text DEFAULT NULL,
    `price` decimal(10,2) NOT NULL,
    `duration` int(11) DEFAULT NULL COMMENT 'المدة بالدقائق',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإصلاحات
CREATE TABLE IF NOT EXISTS `repairs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `customer_id` int(11) NOT NULL,
    `reference_number` varchar(20) NOT NULL,
    `device_type` varchar(50) NOT NULL,
    `brand` varchar(50) NOT NULL,
    `model` varchar(100) NOT NULL,
    `serial_number` varchar(100) DEFAULT NULL,
    `problem_description` text NOT NULL,
    `diagnosis` text DEFAULT NULL,
    `solution` text DEFAULT NULL,
    `status` int(11) NOT NULL DEFAULT 0,
    `estimated_cost` decimal(10,2) DEFAULT NULL,
    `final_cost` decimal(10,2) DEFAULT NULL,
    `received_date` datetime NOT NULL,
    `estimated_completion_date` datetime DEFAULT NULL,
    `completion_date` datetime DEFAULT NULL,
    `returned_date` datetime DEFAULT NULL,
    `notes` text DEFAULT NULL,
    `assigned_technician` varchar(100) DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `reference_number` (`reference_number`),
    KEY `customer_id` (`customer_id`),
    CONSTRAINT `repairs_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول طلبات الصيانة
CREATE TABLE IF NOT EXISTS `repair_orders` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `order_number` varchar(20) NOT NULL,
    `device_id` int(11) NOT NULL,
    `customer_id` int(11) NOT NULL,
    `status` enum('received','diagnosed','waiting_parts','in_progress','repaired','ready','delivered','cancelled','unrepairable') NOT NULL DEFAULT 'received',
    `total_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
    `service_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
    `part_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
    `paid_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `payment_status` enum('unpaid','partially_paid','paid') NOT NULL DEFAULT 'unpaid',
    `payment_method` varchar(50) DEFAULT NULL,
    `technician_id` int(11) DEFAULT NULL,
    `warranty_period` int(11) DEFAULT NULL COMMENT 'فترة الضمان بالأيام',
    `repair_notes` text DEFAULT NULL,
    `technical_notes` text DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `order_number` (`order_number`),
    KEY `device_id` (`device_id`),
    KEY `customer_id` (`customer_id`),
    KEY `technician_id` (`technician_id`),
    CONSTRAINT `repair_orders_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`id`) ON DELETE CASCADE,
    CONSTRAINT `repair_orders_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
    CONSTRAINT `repair_orders_ibfk_3` FOREIGN KEY (`technician_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول عناصر طلبات الصيانة
CREATE TABLE IF NOT EXISTS `repair_order_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `repair_order_id` int(11) NOT NULL,
    `item_type` enum('part','service') NOT NULL,
    `item_id` int(11) NOT NULL,
    `quantity` int(11) NOT NULL DEFAULT 1,
    `price` decimal(10,2) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `repair_order_id` (`repair_order_id`),
    CONSTRAINT `repair_order_items_ibfk_1` FOREIGN KEY (`repair_order_id`) REFERENCES `repair_orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول خدمات طلب الإصلاح
CREATE TABLE IF NOT EXISTS `repair_order_services` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `repair_order_id` int(11) NOT NULL,
    `service_id` int(11) NOT NULL,
    `price` decimal(10,2) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `repair_order_id` (`repair_order_id`),
    KEY `service_id` (`service_id`),
    CONSTRAINT `repair_order_services_ibfk_1` FOREIGN KEY (`repair_order_id`) REFERENCES `repair_orders` (`id`) ON DELETE CASCADE,
    CONSTRAINT `repair_order_services_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قطع غيار طلب الإصلاح
CREATE TABLE IF NOT EXISTS `repair_order_parts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `repair_order_id` int(11) NOT NULL,
    `part_id` int(11) NOT NULL,
    `quantity` int(11) NOT NULL DEFAULT 1,
    `price` decimal(10,2) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `repair_order_id` (`repair_order_id`),
    KEY `part_id` (`part_id`),
    CONSTRAINT `repair_order_parts_ibfk_1` FOREIGN KEY (`repair_order_id`) REFERENCES `repair_orders` (`id`) ON DELETE CASCADE,
    CONSTRAINT `repair_order_parts_ibfk_2` FOREIGN KEY (`part_id`) REFERENCES `parts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المدفوعات
CREATE TABLE IF NOT EXISTS `payments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `repair_order_id` int(11) NOT NULL,
    `amount` decimal(10,2) NOT NULL,
    `payment_method` varchar(50) NOT NULL,
    `notes` text DEFAULT NULL,
    `user_id` int(11) DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `repair_order_id` (`repair_order_id`),
    KEY `user_id` (`user_id`),
    CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`repair_order_id`) REFERENCES `repair_orders` (`id`) ON DELETE CASCADE,
    CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجل الحالة
CREATE TABLE IF NOT EXISTS `status_history` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `repair_order_id` int(11) NOT NULL,
    `status` varchar(50) NOT NULL,
    `notes` text DEFAULT NULL,
    `user_id` int(11) DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `repair_order_id` (`repair_order_id`),
    KEY `user_id` (`user_id`),
    CONSTRAINT `status_history_ibfk_1` FOREIGN KEY (`repair_order_id`) REFERENCES `repair_orders` (`id`) ON DELETE CASCADE,
    CONSTRAINT `status_history_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول حركات المخزون
CREATE TABLE IF NOT EXISTS `inventory_transactions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `part_id` int(11) NOT NULL,
    `transaction_type` enum('in','out','adjustment') NOT NULL,
    `quantity` int(11) NOT NULL,
    `reference` varchar(100) DEFAULT NULL,
    `notes` text DEFAULT NULL,
    `user_id` int(11) DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `part_id` (`part_id`),
    KEY `user_id` (`user_id`),
    CONSTRAINT `inventory_transactions_ibfk_1` FOREIGN KEY (`part_id`) REFERENCES `parts` (`id`) ON DELETE CASCADE,
    CONSTRAINT `inventory_transactions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الديون
CREATE TABLE IF NOT EXISTS `debts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `customer_id` int(11) NOT NULL,
    `repair_order_id` int(11) DEFAULT NULL,
    `amount` decimal(10,2) NOT NULL,
    `paid_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `debt_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `due_date` datetime DEFAULT NULL,
    `status` enum('unpaid','partially_paid','paid') NOT NULL DEFAULT 'unpaid',
    `notes` text DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `customer_id` (`customer_id`),
    KEY `repair_order_id` (`repair_order_id`),
    CONSTRAINT `debts_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
    CONSTRAINT `debts_ibfk_2` FOREIGN KEY (`repair_order_id`) REFERENCES `repair_orders` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المصروفات
CREATE TABLE IF NOT EXISTS `expenses` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL,
    `amount` decimal(10,2) NOT NULL,
    `category` varchar(100) DEFAULT NULL,
    `expense_date` date NOT NULL,
    `description` text DEFAULT NULL,
    `user_id` int(11) DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    KEY `expense_date` (`expense_date`),
    KEY `category` (`category`),
    CONSTRAINT `expenses_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإعدادات
CREATE TABLE IF NOT EXISTS `settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `setting_key` varchar(50) NOT NULL,
    `setting_value` text DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المستخدمين المتصلين
CREATE TABLE IF NOT EXISTS `user_sessions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `session_id` varchar(100) NOT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `hostname` varchar(100) DEFAULT NULL,
    `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_activity` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `logout_time` datetime DEFAULT NULL,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    KEY `session_id` (`session_id`),
    CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول معلومات المتجر
CREATE TABLE IF NOT EXISTS `shop_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `activity` varchar(200) DEFAULT NULL,
    `address` text DEFAULT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `email` varchar(100) DEFAULT NULL,
    `website` varchar(100) DEFAULT NULL,
    `logo_path` varchar(255) DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج المستخدم الافتراضي (admin/"")
INSERT INTO `users` (`username`, `password`, `full_name`, `email`, `role`, `status`) VALUES
('admin', '', 'Administrateur', '<EMAIL>', 'admin', 'active');

-- إدراج خدمات افتراضية
INSERT INTO `services` (`name`, `description`, `price`) VALUES
('فحص الجهاز', 'فحص شامل للجهاز وتحديد المشكلة', 500.00),
('تنظيف الجهاز', 'تنظيف الجهاز من الداخل والخارج', 1000.00),
('تغيير شاشة', 'استبدال شاشة الجهاز', 1500.00),
('تغيير بطارية', 'استبدال بطارية الجهاز', 1200.00),
('إصلاح لوحة المفاتيح', 'إصلاح أو استبدال لوحة المفاتيح', 2000.00),
('تثبيت نظام التشغيل', 'تثبيت أو إعادة تثبيت نظام التشغيل', 1500.00),
('استعادة البيانات', 'استعادة البيانات المفقودة', 3000.00);


