﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class Expense
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Titre")]
        public string Title { get; set; }

        [Display(Name = "Montant")]
        public decimal Amount { get; set; }

        [Display(Name = "Catégorie")]
        public string Category { get; set; }

        [Display(Name = "Date de dépense")]
        public DateTime ExpenseDate { get; set; }

        [Display(Name = "Description")]
        public string Description { get; set; }

        [Display(Name = "Identifiant de l'utilisateur")]
        public int? UserId { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Date de mise à jour")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation property
        [Display(Name = "Utilisateur")]
        public User User { get; set; }
    }
}
