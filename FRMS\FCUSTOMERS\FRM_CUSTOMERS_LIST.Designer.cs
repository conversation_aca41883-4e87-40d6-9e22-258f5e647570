﻿namespace IRepairIT.FRMS.FCUSTOMERS
{
    partial class FRM_CUSTOMERS_LIST
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.kryptonHeaderGroup1 = new Krypton.Toolkit.KryptonHeaderGroup();
            this.btnAdd = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnEdit = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnDelete = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnSelect = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnFirst = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnNext = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnPrev = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnLast = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.kryptonDataGridView1 = new Krypton.Toolkit.KryptonDataGridView();
            this.kryptonPanel2 = new Krypton.Toolkit.KryptonPanel();
            this.kryptonLabel1 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonComboBox1 = new Krypton.Toolkit.KryptonComboBox();
            this.TXTSearch = new Krypton.Toolkit.KryptonTextBox();
            this.btnSearch = new Krypton.Toolkit.ButtonSpecAny();
            this.btnClearSearch = new Krypton.Toolkit.ButtonSpecAny();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1.Panel)).BeginInit();
            this.kryptonHeaderGroup1.Panel.SuspendLayout();
            this.kryptonHeaderGroup1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel2)).BeginInit();
            this.kryptonPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonComboBox1)).BeginInit();
            this.SuspendLayout();
            //
            // kryptonHeaderGroup1
            //
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnAdd);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnEdit);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnDelete);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnSelect);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnFirst);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnNext);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnPrev);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnLast);
            this.kryptonHeaderGroup1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonHeaderGroup1.Location = new System.Drawing.Point(0, 0);
            this.kryptonHeaderGroup1.Name = "kryptonHeaderGroup1";
            //
            // kryptonHeaderGroup1.Panel
            //
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.kryptonDataGridView1);
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.kryptonPanel2);
            this.kryptonHeaderGroup1.Size = new System.Drawing.Size(838, 450);
            this.kryptonHeaderGroup1.TabIndex = 5;
            this.kryptonHeaderGroup1.ValuesPrimary.Heading = "Clients";
            this.kryptonHeaderGroup1.ValuesPrimary.Image = null;
            this.kryptonHeaderGroup1.ValuesSecondary.Heading = "N° : ";
            //
            // btnAdd
            //
            this.btnAdd.Image = global::IRepairIT.Properties.Resources.button_circle_add;
            this.btnAdd.Style = Krypton.Toolkit.PaletteButtonStyle.Standalone;
            this.btnAdd.Text = "Ajouter un utilisateur";
            this.btnAdd.UniqueName = "8833e85e72444aeb812cb717fdc0b39c";
            //
            // btnEdit
            //
            this.btnEdit.Image = global::IRepairIT.Properties.Resources.tool_pencil;
            this.btnEdit.Style = Krypton.Toolkit.PaletteButtonStyle.Standalone;
            this.btnEdit.Text = "Modifier";
            this.btnEdit.UniqueName = "bb4bc62dda7046dabd9cdcdd316134ff";
            //
            // btnDelete
            //
            this.btnDelete.Image = global::IRepairIT.Properties.Resources.trash;
            this.btnDelete.Style = Krypton.Toolkit.PaletteButtonStyle.Standalone;
            this.btnDelete.Text = "Supprimer";
            this.btnDelete.UniqueName = "7185fa9cee2e479d9ecc6064ad018e0f";
            //
            // btnSelect
            //
            this.btnSelect.Style = Krypton.Toolkit.PaletteButtonStyle.Standalone;
            this.btnSelect.Text = "Sélectionner";
            this.btnSelect.UniqueName = "selectCustomerButton";
            //
            // btnFirst
            //
            this.btnFirst.HeaderLocation = Krypton.Toolkit.HeaderLocation.SecondaryHeader;
            this.btnFirst.UniqueName = "ed6d29f8c18d452f98335f894afc72ac";
            //
            // btnNext
            //
            this.btnNext.HeaderLocation = Krypton.Toolkit.HeaderLocation.SecondaryHeader;
            this.btnNext.UniqueName = "d0f86ac8b82d404eb2b76dd3dad4b2c0";
            //
            // btnPrev
            //
            this.btnPrev.HeaderLocation = Krypton.Toolkit.HeaderLocation.SecondaryHeader;
            this.btnPrev.UniqueName = "a49b80c9541a408899d0e2102c0b53c2";
            //
            // btnLast
            //
            this.btnLast.HeaderLocation = Krypton.Toolkit.HeaderLocation.SecondaryHeader;
            this.btnLast.UniqueName = "76de9b7973ab42388a0996e56f79d9c9";
            //
            // kryptonDataGridView1
            //
            this.kryptonDataGridView1.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.kryptonDataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonDataGridView1.Location = new System.Drawing.Point(0, 36);
            this.kryptonDataGridView1.Name = "kryptonDataGridView1";
            this.kryptonDataGridView1.Size = new System.Drawing.Size(836, 358);
            this.kryptonDataGridView1.TabIndex = 2;
            //
            // kryptonPanel2
            //
            this.kryptonPanel2.Controls.Add(this.kryptonLabel1);
            this.kryptonPanel2.Controls.Add(this.kryptonComboBox1);
            this.kryptonPanel2.Controls.Add(this.TXTSearch);
            this.kryptonPanel2.Dock = System.Windows.Forms.DockStyle.Top;
            this.kryptonPanel2.Location = new System.Drawing.Point(0, 0);
            this.kryptonPanel2.Name = "kryptonPanel2";
            this.kryptonPanel2.Size = new System.Drawing.Size(836, 36);
            this.kryptonPanel2.TabIndex = 1;
            //
            // kryptonLabel1
            //
            this.kryptonLabel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonLabel1.Location = new System.Drawing.Point(652, 8);
            this.kryptonLabel1.Name = "kryptonLabel1";
            this.kryptonLabel1.Size = new System.Drawing.Size(78, 20);
            this.kryptonLabel1.TabIndex = 4;
            this.kryptonLabel1.Values.Text = "Page count :";
            //
            // kryptonComboBox1
            //
            this.kryptonComboBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonComboBox1.DropDownWidth = 121;
            this.kryptonComboBox1.IntegralHeight = false;
            this.kryptonComboBox1.Items.AddRange(new object[] {
            "10",
            "50",
            "100",
            "200",
            "500"});
            this.kryptonComboBox1.Location = new System.Drawing.Point(736, 6);
            this.kryptonComboBox1.Name = "kryptonComboBox1";
            this.kryptonComboBox1.Size = new System.Drawing.Size(89, 22);
            this.kryptonComboBox1.StateCommon.ComboBox.Content.TextH = Krypton.Toolkit.PaletteRelativeAlign.Near;
            this.kryptonComboBox1.TabIndex = 3;
            //
            // TXTSearch
            //
            this.TXTSearch.ButtonSpecs.Add(this.btnSearch);
            this.TXTSearch.ButtonSpecs.Add(this.btnClearSearch);
            this.TXTSearch.CueHint.Color1 = System.Drawing.Color.Silver;
            this.TXTSearch.CueHint.CueHintText = "Rechercher ...";
            this.TXTSearch.Location = new System.Drawing.Point(3, 3);
            this.TXTSearch.Name = "TXTSearch";
            this.TXTSearch.Size = new System.Drawing.Size(390, 27);
            this.TXTSearch.StateCommon.Border.Rounding = 5F;
            this.TXTSearch.TabIndex = 0;
            //
            // btnSearch
            //
            this.btnSearch.ToolTipTitle = "Rechercher";
            this.btnSearch.UniqueName = "58f205b2a5e84fd6af6c035b128ba21a";
            //
            // btnClearSearch
            //
            this.btnClearSearch.ToolTipTitle = "Effacer la recherche";
            this.btnClearSearch.UniqueName = "ClearSearch";
            //
            // FRM_CUSTOMERS_LIST
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(838, 450);
            this.Controls.Add(this.kryptonHeaderGroup1);
            this.Name = "FRM_CUSTOMERS_LIST";
            this.Text = "Gestion des Clients";
            this.Load += new System.EventHandler(this.FRM_CUSTOMERS_LIST_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1.Panel)).EndInit();
            this.kryptonHeaderGroup1.Panel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1)).EndInit();
            this.kryptonHeaderGroup1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel2)).EndInit();
            this.kryptonPanel2.ResumeLayout(false);
            this.kryptonPanel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonComboBox1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonHeaderGroup kryptonHeaderGroup1;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnAdd;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnEdit;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnDelete;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnSelect;
        private Krypton.Toolkit.KryptonDataGridView kryptonDataGridView1;
        private Krypton.Toolkit.KryptonPanel kryptonPanel2;
        private Krypton.Toolkit.KryptonTextBox TXTSearch;
        private Krypton.Toolkit.ButtonSpecAny btnSearch;
        private Krypton.Toolkit.ButtonSpecAny btnClearSearch;
        private Krypton.Toolkit.KryptonComboBox kryptonComboBox1;
        private Krypton.Toolkit.KryptonLabel kryptonLabel1;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnFirst;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnNext;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnPrev;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnLast;
    }
}