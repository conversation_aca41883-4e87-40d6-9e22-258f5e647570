﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class RepairOrderService
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Identifiant de la commande de réparation")]
        public int RepairOrderId { get; set; }

        [Display(Name = "Identifiant du service")]
        public int ServiceId { get; set; }

        [Display(Name = "Prix")]
        public decimal Price { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        // Navigation properties
        [Display(Name = "Commande de réparation")]
        public RepairOrder RepairOrder { get; set; }

        [Display(Name = "Service")]
        public Service Service { get; set; }
    }
}
