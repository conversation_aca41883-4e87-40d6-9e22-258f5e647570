﻿using Dapper;
using IRepairIT.DB;
using Krypton.Toolkit;
using MySql.Data.MySqlClient;
using System;
using System.IO;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FDB
{
    public partial class FRM_DBCONNECTIONSTRING : KryptonForm
    {
        private ConnectionSettings _connectionSettings;
        private readonly string _settingsFilePath;
        public FRM_DBCONNECTIONSTRING()
        {
            InitializeComponent();
            _settingsFilePath = Path.Combine(Application.StartupPath, "connection.json");
            _connectionSettings = ConnectionSettings.LoadFromJson(_settingsFilePath);

        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void FRM_DBCONNECTIONSTRING_Load(object sender, EventArgs e)
        {
            txtServer.Text = _connectionSettings.Server;
            txtPort.Text = _connectionSettings.Port.ToString();
            txtUsername.Text = _connectionSettings.Username;
            txtPassword.Text = _connectionSettings.Password;
        }

        private void btnTest_Click(object sender, EventArgs e)
        {
            var tempSettings = new ConnectionSettings
            {
                Server = txtServer.Text.Trim(),
                Port = int.TryParse(txtPort.Text.Trim(), out int port) ? port : 3306,
                Username = txtUsername.Text.Trim(),
                Password = txtPassword.Text
            };

            try
            {
                using (var connection = new MySqlConnection(tempSettings.GetMasterConnectionString()))
                {
                    connection.Open();

                    var version = connection.ExecuteScalar<string>("SELECT VERSION()");
                    KryptonMessageBox.Show($"Connexion réussie!\nVersion MySQL: {version}", "Succès", buttons: KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Connexion échouée: {ex.Message}", "Erreur", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            _connectionSettings.Server = txtServer.Text.Trim();
            _connectionSettings.Port = int.TryParse(txtPort.Text.Trim(), out int port) ? port : 3306;
            _connectionSettings.Username = txtUsername.Text.Trim();
            _connectionSettings.Password = txtPassword.Text;

            _connectionSettings.SaveToJson(_settingsFilePath);

            KryptonMessageBox.Show("Paramètres de connexion enregistrés avec succès!", "Succès", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            DialogResult = DialogResult.OK;
            Close();
        }
    }
}
