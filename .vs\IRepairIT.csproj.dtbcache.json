{"RootPath": "E:\\Dev\\IRepairIT2", "ProjectFileName": "IRepairIT.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Data\\Common\\DeviceCommands.cs"}, {"SourceFile": "Data\\Common\\InventoryCommands.cs"}, {"SourceFile": "Data\\Common\\DashboardCommands.cs"}, {"SourceFile": "Data\\Common\\DataAccess.cs"}, {"SourceFile": "Data\\Common\\DebtCommands.cs"}, {"SourceFile": "Data\\Common\\CustomerCommands.cs"}, {"SourceFile": "Data\\Common\\PartCommands.cs"}, {"SourceFile": "Data\\Common\\RepairOrderCommands.cs"}, {"SourceFile": "Data\\Common\\ServiceCommands.cs"}, {"SourceFile": "Data\\Common\\ShopInfoCommands.cs"}, {"SourceFile": "Data\\Common\\UserCommands.cs"}, {"SourceFile": "Data\\DatabaseManager.cs"}, {"SourceFile": "DB\\ConnectionSettings.cs"}, {"SourceFile": "Enums\\ItemType.cs"}, {"SourceFile": "Enums\\PaymentStatus.cs"}, {"SourceFile": "Enums\\RepairOrderStatus.cs"}, {"SourceFile": "Enums\\TransactionType.cs"}, {"SourceFile": "Enums\\UserRoles.cs"}, {"SourceFile": "Enums\\UserStatus.cs"}, {"SourceFile": "Form1.cs"}, {"SourceFile": "Form1.Designer.cs"}, {"SourceFile": "FRMS\\FCOMMON\\FRM_SELECT_CUSTOMER.cs"}, {"SourceFile": "FRMS\\FCOMMON\\FRM_SELECT_CUSTOMER.Designer.cs"}, {"SourceFile": "FRMS\\FCOMMON\\FRM_SELECT_ITEM.cs"}, {"SourceFile": "FRMS\\FCOMMON\\FRM_SELECT_ITEM.Designer.cs"}, {"SourceFile": "FRMS\\FCUSTOMERS\\FRM_CUSTOMER.cs"}, {"SourceFile": "FRMS\\FCUSTOMERS\\FRM_CUSTOMER.Designer.cs"}, {"SourceFile": "FRMS\\FCUSTOMERS\\FRM_CUSTOMERS_LIST.cs"}, {"SourceFile": "FRMS\\FCUSTOMERS\\FRM_CUSTOMERS_LIST.Designer.cs"}, {"SourceFile": "FRMS\\FCUSTOMERS\\FRM_CUSTOMERS_NEW_WIZARD.cs"}, {"SourceFile": "FRMS\\FCUSTOMERS\\FRM_CUSTOMERS_NEW_WIZARD.Designer.cs"}, {"SourceFile": "FRMS\\FDB\\FRM_DATABASELIST.cs"}, {"SourceFile": "FRMS\\FDB\\FRM_DATABASELIST.Designer.cs"}, {"SourceFile": "FRMS\\FDB\\FRM_DBCONNECTIONSTRING.cs"}, {"SourceFile": "FRMS\\FDB\\FRM_DBCONNECTIONSTRING.Designer.cs"}, {"SourceFile": "FRMS\\FDB\\FRM_SETUPDB.cs"}, {"SourceFile": "FRMS\\FDB\\FRM_SETUPDB.Designer.cs"}, {"SourceFile": "FRMS\\FDB\\FRM_YEARINPUT.cs"}, {"SourceFile": "FRMS\\FDB\\FRM_YEARINPUT.Designer.cs"}, {"SourceFile": "FRMS\\FDEVICES\\FRM_DEVICE.cs"}, {"SourceFile": "FRMS\\FDEVICES\\FRM_DEVICE.Designer.cs"}, {"SourceFile": "FRMS\\FDEVICES\\FRM_DEVICES_LIST.cs"}, {"SourceFile": "FRMS\\FDEVICES\\FRM_DEVICES_LIST.Designer.cs"}, {"SourceFile": "FRMS\\FDEVICES\\FRM_WEBCAM_CAPTURE.cs"}, {"SourceFile": "FRMS\\FDEVICES\\FRM_WEBCAM_CAPTURE.Designer.cs"}, {"SourceFile": "FRMS\\FDEBTS\\FRM_DEBT.cs"}, {"SourceFile": "FRMS\\FDEBTS\\FRM_DEBT.Designer.cs"}, {"SourceFile": "FRMS\\FDEBTS\\FRM_DEBTS_LIST.cs"}, {"SourceFile": "FRMS\\FDEBTS\\FRM_DEBTS_LIST.Designer.cs"}, {"SourceFile": "FRMS\\FREPORTS\\FRM_REPORTS_LIST.cs"}, {"SourceFile": "FRMS\\FREPORTS\\FRM_REPORTS_LIST.Designer.cs"}, {"SourceFile": "FRMS\\FREPORTS\\FRM_SALES_REPORT.cs"}, {"SourceFile": "FRMS\\FREPORTS\\FRM_SALES_REPORT.Designer.cs"}, {"SourceFile": "FRMS\\FINVENTORY\\FRM_INVENTORY.cs"}, {"SourceFile": "FRMS\\FINVENTORY\\FRM_INVENTORY.Designer.cs"}, {"SourceFile": "FRMS\\FINVENTORY\\FRM_INVENTORY_LIST.cs"}, {"SourceFile": "FRMS\\FINVENTORY\\FRM_INVENTORY_LIST.Designer.cs"}, {"SourceFile": "FRMS\\FINVENTORY\\FRM_INVENTORY_TRANSACTIONS.cs"}, {"SourceFile": "FRMS\\FINVENTORY\\FRM_INVENTORY_TRANSACTIONS.Designer.cs"}, {"SourceFile": "FRMS\\FINVENTORY\\FRM_PART_SELECTOR.cs"}, {"SourceFile": "FRMS\\FINVENTORY\\FRM_PART_SELECTOR.Designer.cs"}, {"SourceFile": "FRMS\\FLOGIN\\FRM_CHANGEPASSWORD.cs"}, {"SourceFile": "FRMS\\FLOGIN\\FRM_CHANGEPASSWORD.Designer.cs"}, {"SourceFile": "FRMS\\FLOGIN\\FRM_LOGIN.cs"}, {"SourceFile": "FRMS\\FLOGIN\\FRM_LOGIN.Designer.cs"}, {"SourceFile": "FRMS\\FLOGIN\\FRM_PASSWORDINPUT.cs"}, {"SourceFile": "FRMS\\FLOGIN\\FRM_PASSWORDINPUT.Designer.cs"}, {"SourceFile": "FRMS\\FMAIN\\FRM_DASHBOARD.cs"}, {"SourceFile": "FRMS\\FMAIN\\FRM_DASHBOARD.Designer.cs"}, {"SourceFile": "FRMS\\FMAIN\\FRM_MAIN.cs"}, {"SourceFile": "FRMS\\FMAIN\\FRM_MAIN.Designer.cs"}, {"SourceFile": "FRMS\\FMAIN\\FRM_SESSION_INFO.cs"}, {"SourceFile": "FRMS\\FMAIN\\FRM_SESSION_INFO.Designer.cs"}, {"SourceFile": "FRMS\\FMAIN\\FRM_SESSION_PASSWORD.cs"}, {"SourceFile": "FRMS\\FMAIN\\FRM_SESSION_PASSWORD.Designer.cs"}, {"SourceFile": "FRMS\\FREPAIRS\\FRM_PAYMENT.cs"}, {"SourceFile": "FRMS\\FREPAIRS\\FRM_PAYMENT.Designer.cs"}, {"SourceFile": "FRMS\\FREPAIRS\\FRM_REPAIR_ORDER.cs"}, {"SourceFile": "FRMS\\FREPAIRS\\FRM_REPAIR_ORDER.Designer.cs"}, {"SourceFile": "FRMS\\FREPAIRS\\FRM_REPAIR_ORDERS_LIST.cs"}, {"SourceFile": "FRMS\\FREPAIRS\\FRM_REPAIR_ORDERS_LIST.Designer.cs"}, {"SourceFile": "FRMS\\FSERVICES\\FRM_SERVICES.cs"}, {"SourceFile": "FRMS\\FSERVICES\\FRM_SERVICES.Designer.cs"}, {"SourceFile": "FRMS\\FSERVICES\\FRM_SERVICES_LIST.cs"}, {"SourceFile": "FRMS\\FSERVICES\\FRM_SERVICES_LIST.Designer.cs"}, {"SourceFile": "FRMS\\FSERVICES\\FRM_SERVICE_SELECTOR.cs"}, {"SourceFile": "FRMS\\FSERVICES\\FRM_SERVICE_SELECTOR.Designer.cs"}, {"SourceFile": "FRMS\\FSHOP\\FRM_SHOP.cs"}, {"SourceFile": "FRMS\\FSHOP\\FRM_SHOP.Designer.cs"}, {"SourceFile": "FRMS\\FUSERS\\FRM_USERS.cs"}, {"SourceFile": "FRMS\\FUSERS\\FRM_USERS.Designer.cs"}, {"SourceFile": "FRMS\\FUSERS\\FRM_USERS_LIST.cs"}, {"SourceFile": "FRMS\\FUSERS\\FRM_USERS_LIST.Designer.cs"}, {"SourceFile": "Models\\Customer.cs"}, {"SourceFile": "Models\\Debt.cs"}, {"SourceFile": "Models\\Device.cs"}, {"SourceFile": "Models\\DeviceImage.cs"}, {"SourceFile": "Models\\DTO\\LatestCustomerDto.cs"}, {"SourceFile": "Models\\DTO\\LatestDto.cs"}, {"SourceFile": "Models\\DTO\\PagedUsers.cs"}, {"SourceFile": "Models\\Expense.cs"}, {"SourceFile": "Models\\InventoryTransaction.cs"}, {"SourceFile": "Models\\Part.cs"}, {"SourceFile": "Models\\Payment.cs"}, {"SourceFile": "Models\\Repair.cs"}, {"SourceFile": "Models\\RepairOrder.cs"}, {"SourceFile": "Models\\RepairOrderItem.cs"}, {"SourceFile": "Models\\RepairOrderPart.cs"}, {"SourceFile": "Models\\RepairOrderService.cs"}, {"SourceFile": "Models\\Service.cs"}, {"SourceFile": "Models\\Setting.cs"}, {"SourceFile": "Models\\ShopInfo.cs"}, {"SourceFile": "Models\\StatusHistory.cs"}, {"SourceFile": "Models\\User.cs"}, {"SourceFile": "Models\\UserSession.cs"}, {"SourceFile": "Program.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "FRMS\\FSPLASH\\FRM_SPLASH.cs"}, {"SourceFile": "FRMS\\FSPLASH\\FRM_SPLASH.Designer.cs"}, {"SourceFile": "utilities\\DGV_STYLE.cs"}, {"SourceFile": "utilities\\EntityValidator.cs"}, {"SourceFile": "utilities\\TabManager.cs"}, {"SourceFile": "utilities\\WebcamCapture.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.6.2.AssemblyAttributes.cs"}], "References": [{"Reference": "E:\\Dev\\IRepairIT2\\packages\\AForge.2.2.5\\lib\\AForge.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\Dev\\IRepairIT2\\packages\\AForge.Video.DirectShow.2.2.5\\lib\\AForge.Video.DirectShow.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\Dev\\IRepairIT2\\packages\\AForge.Video.2.2.5\\lib\\AForge.Video.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\Dev\\IRepairIT2\\packages\\Dapper.2.1.66\\lib\\net461\\Dapper.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\FastReport.Bars\\v4.0_2020.3.7.0__db7e5ce63278458c\\FastReport.Bars.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\FastReport.Compat\\v4.0_2020.3.2.0__406e1f4c3c8ef97e\\FastReport.Compat.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\FastReport.DataVisualization\\v4.0_2020.3.1.0__5ceb240df42bf6e8\\FastReport.DataVisualization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\FastReport\\v4.0_2020.3.7.0__db7e5ce63278458c\\FastReport.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\FastReport.Editor\\v4.0_2020.3.7.0__db7e5ce63278458c\\FastReport.Editor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\Dev\\IRepairIT2\\packages\\Krypton.Navigator.90.24.11.317\\lib\\net462\\Krypton.Navigator.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\Dev\\IRepairIT2\\packages\\Krypton.Toolkit.90.24.11.317\\lib\\net462\\Krypton.Toolkit.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\Dev\\IRepairIT2\\packages\\Microsoft.Bcl.AsyncInterfaces.9.0.1\\lib\\net462\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\Dev\\IRepairIT2\\bin\\Debug\\MySql.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\Dev\\IRepairIT2\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\Dev\\IRepairIT2\\packages\\System.Runtime.CompilerServices.Unsafe.4.5.3\\lib\\net461\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\Dev\\IRepairIT2\\packages\\System.Threading.Tasks.Extensions.4.5.4\\lib\\net461\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\Dev\\IRepairIT2\\bin\\Debug\\IRepairIT.exe", "OutputItemRelativePath": "IRepairIT.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}