﻿using IRepairIT.Data.Common;
using IRepairIT.FRMS.FCUSTOMERS;
using IRepairIT.FRMS.FDEVICES;
using IRepairIT.FRMS.FDEBTS;
using IRepairIT.FRMS.FINVENTORY;
using IRepairIT.FRMS.FLOGIN;
using IRepairIT.FRMS.FREPAIRS;
using IRepairIT.FRMS.FREPORTS;
using IRepairIT.FRMS.FSERVICES;
using IRepairIT.FRMS.FSHOP;
using IRepairIT.FRMS.FUSERS;
using IRepairIT.Models;
using IRepairIT.Properties;
using IRepairIT.Utilities;
using Krypton.Toolkit;
using System;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FMAIN
{
    public partial class FRM_MAIN : KryptonForm
    {
        private TabManager _tabManager;
        public FRM_MAIN()
        {
            InitializeComponent();

            // Initialiser le timer manuellement
            if (timerDateTime != null)
            {
                timerDateTime.Interval = 1000;
                timerDateTime.Tick += timerDateTime_Tick;
                timerDateTime.Enabled = true;
            }

            // Enable KeyPreview to capture keyboard shortcuts
            this.KeyPreview = true;
            this.KeyDown += FRM_MAIN_KeyDown;

            _tabManager = new TabManager(kryptonNavigator1);
            _tabManager.AddOrSelectTab(() => new FRM_DASHBOARD());
        }

        private void clientsToolStripMenuItem_Click(object sender, EventArgs e)
        {
           _tabManager.AddOrSelectTab(() => new FRM_CUSTOMERS_LIST());
        }

        private void utilisateursToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _tabManager.AddOrSelectTab(() => new FRM_USERS_LIST());
        }

        private async void FRM_MAIN_Load(object sender, EventArgs e)
        {
            // Initialiser la barre d'état
            UpdateStatusBar();

            // Charger les informations de l'utilisateur si disponibles
            if (Settings.Default.CurrentUserId > 0)
            {
                try
                {
                    var userCommands = new UserCommands();
                    var user = await userCommands.GetByIdAsync(Settings.Default.CurrentUserId);
                    if (user != null)
                    {
                        lblUserInfo.Text = $"Utilisateur: {user.Full_Name}";
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Erreur lors du chargement des informations utilisateur: {ex.Message}");
                }
            }

            // Afficher les informations de la base de données
            if (!string.IsNullOrEmpty(Settings.Default.CurrentDatabase))
            {
                lblDatabaseInfo.Text = $"Base de données: {Settings.Default.CurrentDatabase}";
            }
        }

        private async void logToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var result = KryptonMessageBox.Show(
               "Êtes-vous sûr de vouloir vous déconnecter?",
               "Confirmation",
               KryptonMessageBoxButtons.YesNo,
               KryptonMessageBoxIcon.Question
           );

            if (result == DialogResult.Yes)
            {
                // Fermer la session utilisateur
                if (Settings.Default.CurrentUserId > 0)
                {
                    try
                    {
                        var userCommands = new UserCommands();
                        await userCommands.CloseUserSessionAsync(Settings.Default.CurrentUserId);

                        // Réinitialiser l'ID utilisateur
                        Settings.Default.CurrentUserId = 0;
                        Settings.Default.Save();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Erreur lors de la fermeture de la session utilisateur: {ex.Message}");
                    }
                }

                this.Hide();
                var loginForm = new FRM_LOGIN();
                loginForm.FormClosed += (s, args) => this.Close();
                loginForm.ShowDialog();
            }
        }

        private void tableauDeBordToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _tabManager.AddOrSelectTab(() => new FRM_DASHBOARD());
        }

        private void servicesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _tabManager.AddOrSelectTab(() => new FRM_SERVICES_LIST());
        }

        private void paramètresToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _tabManager.AddOrSelectTab(() => new FRM_SHOP());
        }

        private void inventaireToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _tabManager.AddOrSelectTab(() => new FRM_INVENTORY_LIST());
        }

        private void appareilsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _tabManager.AddOrSelectTab(() => new FRM_DEVICES_LIST());
        }

        private void timerDateTime_Tick(object sender, EventArgs e)
        {
            // Mettre à jour la date et l'heure dans la barre d'état
            UpdateStatusBar();
        }

        private void UpdateStatusBar()
        {
            // Mettre à jour la date et l'heure
            lblDateTime.Text = $"Date: {DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")}";
        }

        private void ordresDeRéparationToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _tabManager.AddOrSelectTab(()=>new FRM_REPAIR_ORDERS_LIST());
        }

        private void dettesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _tabManager.AddOrSelectTab(()=>new FRM_DEBTS_LIST());
        }

        private void rapportDesDettesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _tabManager.AddOrSelectTab(()=>new FRM_REPORTS_LIST());
        }

        private void rapportDesRéparationsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _tabManager.AddOrSelectTab(()=>new FRM_REPORTS_LIST());
        }

        private void rapportDesVentesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _tabManager.AddOrSelectTab(()=>new FRM_SALES_REPORT());
        }

        private void FRM_MAIN_KeyDown(object sender, KeyEventArgs e)
        {
            // Handle Ctrl+S shortcut for session information
            if (e.Control && e.KeyCode == Keys.S)
            {
                ShowSessionInfo();
                e.Handled = true;
            }
        }

        private void ShowSessionInfo()
        {
            // Check if user is logged in
            if (Settings.Default.CurrentUserId <= 0)
            {
                KryptonMessageBox.Show("Vous n'êtes pas connecté.", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                return;
            }

            // Show password input dialog
            var passwordForm = new FRM_SESSION_PASSWORD();
            if (passwordForm.ShowDialog() == DialogResult.OK)
            {
                // Verify password
                if (passwordForm.EnteredPassword == "19951997")
                {
                    // Show session information
                    var sessionInfoForm = new FRM_SESSION_INFO();
                    if (sessionInfoForm.ShowDialog() == DialogResult.OK)
                    {
                        // User ended session, close the application
                        this.Close();
                    }
                }
                else
                {
                    KryptonMessageBox.Show("Mot de passe incorrect", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
            }
        }

        private  void FRM_MAIN_FormClosing(object sender, FormClosingEventArgs e)
        {
            //if (Settings.Default.CurrentUserId > 0)
            //{
            //    try
            //    {
            //        var userCommands = new UserCommands();
            //        await userCommands.CloseUserSessionAsync(Settings.Default.CurrentUserId);

            //        // Reset user ID
            //        Settings.Default.CurrentUserId = 0;
            //        Settings.Default.Save();
            //        Application.Exit();
            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine($"Erreur lors de la fermeture de la session utilisateur: {ex.Message}");
            //    }
            //}
        }

        private void nouveauClientWizardToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                var wizardForm = new FRM_CUSTOMERS_NEW_WIZARD();
                if (wizardForm.ShowDialog() == DialogResult.OK)
                {
                    // Wizard completed successfully
                    KryptonMessageBox.Show(
                        $"Client '{wizardForm.CreatedCustomer?.name}' créé avec succès!\n" +
                        $"Appareil: {wizardForm.CreatedDevice?.Type} {wizardForm.CreatedDevice?.Brand}\n" +
                        $"Ordre de réparation: {wizardForm.CreatedRepairOrder?.OrderNumber}",
                        "Succès",
                        KryptonMessageBoxButtons.OK,
                        KryptonMessageBoxIcon.Information
                    );

                    // Optionally refresh any open customer or repair order lists
                    // You can add code here to refresh related tabs if needed
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'ouverture de l'assistant: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
    }
}
