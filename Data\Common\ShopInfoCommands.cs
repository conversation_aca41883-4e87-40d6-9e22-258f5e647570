﻿﻿using IRepairIT.Models;
using System;
using System.Threading.Tasks;

namespace IRepairIT.Data.Common
{
    public class ShopInfoCommands
    {
        private readonly DataAccess _db;

        public ShopInfoCommands()
        {
            _db = new DataAccess();
        }

        public async Task<ShopInfo> GetShopInfoAsync()
        {
            const string sql = @"SELECT * FROM shop_info ORDER BY id DESC LIMIT 1";
            return await _db.QueryFirstOrDefaultQuery<ShopInfo>(sql);
        }

        public async Task<int> UpdateShopInfoAsync(ShopInfo shopInfo)
        {
            const string sql = @"
                UPDATE shop_info SET
                    name = @p_name,
                    activity = @p_activity,
                    address = @p_address,
                    phone = @p_phone,
                    email = @p_email,
                    website = @p_website,
                    logo_path = @p_logo_path,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = @p_id";

            var parameters = new
            {
                p_id = shopInfo.id,
                p_name = shopInfo.name,
                p_activity = shopInfo.activity,
                p_address = shopInfo.address,
                p_phone = shopInfo.phone,
                p_email = shopInfo.email,
                p_website = shopInfo.website,
                p_logo_path = shopInfo.logo_path
            };

            return await _db.ExecuteQuery(sql, parameters);
        }

        public async Task<int> InsertShopInfoAsync(ShopInfo shopInfo)
        {
            const string sql = @"
                INSERT INTO shop_info (
                    name, activity, address, phone, email, website, logo_path
                ) VALUES (
                    @p_name, @p_activity, @p_address, @p_phone, @p_email, @p_website, @p_logo_path
                )";

            var parameters = new
            {
                p_name = shopInfo.name,
                p_activity = shopInfo.activity,
                p_address = shopInfo.address,
                p_phone = shopInfo.phone,
                p_email = shopInfo.email,
                p_website = shopInfo.website,
                p_logo_path = shopInfo.logo_path
            };

            return await _db.InsertAndGetIdAsync(sql, parameters);
        }
    }
}
