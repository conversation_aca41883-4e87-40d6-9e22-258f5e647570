﻿using IRepairIT.Data.Common;
using IRepairIT.Enums;
using IRepairIT.Models;
using Krypton.Toolkit;
using MySql.Data.MySqlClient;
using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FUSERS
{
    public partial class FRM_USERS : KryptonForm
    {
        private readonly UserCommands _userCommands;
        private User _user;
        private bool _isEditMode;
        private bool _passwordChanged;

        public bool HasChanges { get; private set; }

        public FRM_USERS()
        {
            InitializeComponent();
            _userCommands = new UserCommands();
            _user = new User();
            _isEditMode = false;
            _passwordChanged = true;
        }

        public FRM_USERS(User user)
        {
            InitializeComponent();
            _userCommands = new UserCommands();
            _user = user;
            _isEditMode = true;
            _passwordChanged = false;
        }

        private void FRM_USERS_Load(object sender, EventArgs e)
        {
            InitializeControls();

            FillComboBoxes();

            if (_isEditMode)
            {
                FillFormWithUserData();
            }
        }

        private void InitializeControls()
        {
            btnShowPassword.Click += BtnShowPassword_Click;
            btnShowConfirmPassword.Click += BtnShowConfirmPassword_Click;

            txtPassword.Tag = "0";
            txtConfirmPassword.Tag = "0";
        }

        private void FillComboBoxes()
        {
            cboRole.Items.Clear();
            foreach (UserRoles role in Enum.GetValues(typeof(UserRoles)))
            {
                cboRole.Items.Add(role);
            }
            if (cboRole.Items.Count > 0)
                cboRole.SelectedIndex = 0;

            cboStatus.Items.Clear();
            foreach (UserStatus status in Enum.GetValues(typeof(UserStatus)))
            {
                cboStatus.Items.Add(status);
            }
            if (cboStatus.Items.Count > 0)
                cboStatus.SelectedIndex = 0;
        }

        private void FillFormWithUserData()
        {
            txtUsername.Text = _user.Username;
            txtFullName.Text = _user.Full_Name;
            txtEmail.Text = _user.Email;
            txtPhone.Text = _user.Phone;

            for (int i = 0; i < cboRole.Items.Count; i++)
            {
                if ((UserRoles)cboRole.Items[i] == _user.Role)
                {
                    cboRole.SelectedIndex = i;
                    break;
                }
            }

            for (int i = 0; i < cboStatus.Items.Count; i++)
            {
                if ((UserStatus)cboStatus.Items[i] == _user.Status)
                {
                    cboStatus.SelectedIndex = i;
                    break;
                }
            }

            txtUsername.Enabled = false;

            this.Text = "Modifier l'utilisateur";
        }

        private void BtnShowPassword_Click(object sender, EventArgs e)
        {
            txtPassword.PasswordChar = (txtPassword.Tag as string == "0") ? '\0' : '#';
            btnShowPassword.Image = (txtPassword.Tag as string == "0") ?
                Properties.Resources.eye_no : Properties.Resources.eye;
            txtPassword.Tag = (txtPassword.Tag as string == "0") ? "1" : "0";
        }

        private void BtnShowConfirmPassword_Click(object sender, EventArgs e)
        {
            txtConfirmPassword.PasswordChar = (txtConfirmPassword.Tag as string == "0") ? '\0' : '#';
            btnShowConfirmPassword.Image = (txtConfirmPassword.Tag as string == "0") ?
                Properties.Resources.eye_no : Properties.Resources.eye;
            txtConfirmPassword.Tag = (txtConfirmPassword.Tag as string == "0") ? "1" : "0";
        }

        private async Task<bool> ValidateForm()
        {
            errorProvider.Clear();
            bool isValid = true;

            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                errorProvider.SetError(txtUsername, "Le nom d'utilisateur est obligatoire");
                isValid = false;
                txtUsername.Focus();
            }

            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                errorProvider.SetError(txtFullName, "Le nom complet est obligatoire");
                isValid = false;
                if (string.IsNullOrWhiteSpace(txtUsername.Text) == false)
                    txtFullName.Focus();
            }

            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                if (!IsValidEmail(txtEmail.Text))
                {
                    errorProvider.SetError(txtEmail, "L'adresse email n'est pas valide");
                    isValid = false;
                    if (string.IsNullOrWhiteSpace(txtUsername.Text) == false &&
                        string.IsNullOrWhiteSpace(txtFullName.Text) == false)
                        txtEmail.Focus();
                }
                else
                {
                    bool emailExists = await _userCommands.EmailExistsAsync(txtEmail.Text.Trim(), _isEditMode ? _user.Id : 0);
                    if (emailExists)
                    {
                        errorProvider.SetError(txtEmail, "Cette adresse email est déjà utilisée par un autre utilisateur");
                        isValid = false;
                        txtEmail.Focus();
                    }
                }
            }

            if (!_isEditMode || (_isEditMode && !string.IsNullOrWhiteSpace(txtPassword.Text)))
            {
                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    errorProvider.SetError(txtPassword, "Le mot de passe est obligatoire");
                    isValid = false;
                }
                else if (txtPassword.Text.Length < 4)
                {
                    errorProvider.SetError(txtPassword, "Le mot de passe doit contenir au moins 4 caractères");
                    isValid = false;
                }

                if (string.IsNullOrWhiteSpace(txtConfirmPassword.Text))
                {
                    errorProvider.SetError(txtConfirmPassword, "La confirmation du mot de passe est obligatoire");
                    isValid = false;
                }
                else if (txtPassword.Text != txtConfirmPassword.Text)
                {
                    errorProvider.SetError(txtConfirmPassword, "Les mots de passe ne correspondent pas");
                    isValid = false;
                }
            }

            return isValid;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void UpdateUserFromForm()
        {
            _user.Username = txtUsername.Text.Trim();
            _user.Full_Name = txtFullName.Text.Trim();
            _user.Email = txtEmail.Text.Trim();
            _user.Phone = txtPhone.Text.Trim();

            if (cboRole.SelectedIndex >= 0)
            {
                _user.Role = (UserRoles)cboRole.SelectedItem;
            }

            if (cboStatus.SelectedIndex >= 0)
            {
                _user.Status = (UserStatus)cboStatus.SelectedItem;
            }

            if (!string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                _user.Password = txtPassword.Text;
                _passwordChanged = true;
            }
        }

        private async Task<bool> SaveUser()
        {
            try
            {
                if (!await ValidateForm())
                    return false;

                UpdateUserFromForm();

                if (_isEditMode)
                {
                    await _userCommands.UpdateAsync(_user);

                    if (_passwordChanged)
                    {
                        await _userCommands.ChangePasswordAsync(_user.Id, _user.Password);
                    }

                    KryptonMessageBox.Show("L'utilisateur a été mis à jour avec succès", "Succès",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                    HasChanges = true;
                }
                else
                {
                    int userId = await _userCommands.InsertAsync(_user);
                    _user.Id = userId;
                    _isEditMode = true;

                    KryptonMessageBox.Show("L'utilisateur a été ajouté avec succès", "Succès",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                    HasChanges = true;
                }

                return true;
            }
            catch (MySqlException ex) when (ex.Number == 1062)
            {
                string errorMessage = ex.Message.ToLower();
                if (errorMessage.Contains("email"))
                {
                    KryptonMessageBox.Show("Cette adresse email est déjà utilisée par un autre utilisateur", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    txtEmail.Focus();
                }
                else if (errorMessage.Contains("username"))
                {
                    KryptonMessageBox.Show("Ce nom d'utilisateur est déjà utilisé", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    txtUsername.Focus();
                }
                else
                {
                    KryptonMessageBox.Show($"Erreur de duplication: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
                return false;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'enregistrement de l'utilisateur: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                return false;
            }
        }

        private void ResetForm()
        {
            txtUsername.Text = string.Empty;
            txtFullName.Text = string.Empty;
            txtEmail.Text = string.Empty;
            txtPhone.Text = string.Empty;
            txtPassword.Text = string.Empty;
            txtConfirmPassword.Text = string.Empty;
            cboRole.SelectedIndex = 0;
            cboStatus.SelectedIndex = 0;

            txtUsername.Enabled = true;

            _user = new User();
            _isEditMode = false;
            _passwordChanged = true;

            this.Text = "Ajouter un utilisateur";

            errorProvider.Clear();

            txtUsername.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            await SaveUser();
        }

        private async void btnSaveAndNew_Click(object sender, EventArgs e)
        {
            if (await SaveUser())
            {
                ResetForm();
            }
        }

        private async void btnSaveAndClose_Click(object sender, EventArgs e)
        {
            if (await SaveUser())
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
