﻿﻿using IRepairIT.Data.Common;
using IRepairIT.Enums;
using IRepairIT.Models;
using Krypton.Toolkit;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FINVENTORY
{
    public partial class FRM_INVENTORY : KryptonForm
    {
        private readonly InventoryCommands _inventoryCommands;
        private Part _part;
        private bool _isEditMode;
        private List<string> _categories;
        private List<string> _suppliers;

        public bool HasChanges { get; private set; }

        // Propriété pour pré-remplir le nom de la pièce
        public string PartName
        {
            get { return txtName.Text; }
            set { txtName.Text = value; }
        }

        public FRM_INVENTORY()
        {
            InitializeComponent();
            _inventoryCommands = new InventoryCommands();
            _part = new Part();
            _isEditMode = false;
            _categories = new List<string>();
            _suppliers = new List<string>();
        }

        public FRM_INVENTORY(int partId)
        {
            InitializeComponent();
            _inventoryCommands = new InventoryCommands();
            _isEditMode = true;
            _categories = new List<string>();
            _suppliers = new List<string>();
            LoadPartAsync(partId);
        }

        private async void LoadPartAsync(int partId)
        {
            try
            {
                _part = await _inventoryCommands.GetByIdAsync(partId);
                if (_part != null)
                {
                    FillFormWithPartData();
                }
                else
                {
                    KryptonMessageBox.Show("Article non trouvé.", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement de l'article: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                this.Close();
            }
        }

        private async void FRM_INVENTORY_Load(object sender, EventArgs e)
        {
            try
            {
                InitializeControls();
                await LoadCategoriesAndSuppliers();

                if (_isEditMode)
                {
                    this.Text = "Modifier un article";
                    // Allow quantity changes in edit mode to enable inventory transactions
                }
                else
                {
                    this.Text = "Ajouter un article";
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du formulaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void InitializeControls()
        {
            // Set up error provider
            errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;

            // Set up numeric controls
            numPurchasePrice.Minimum = 0;
            numPurchasePrice.Maximum = 999999;
            numPurchasePrice.DecimalPlaces = 2;

            numSellingPrice.Minimum = 0;
            numSellingPrice.Maximum = 999999;
            numSellingPrice.DecimalPlaces = 2;

            numQuantity.Minimum = 0;
            numQuantity.Maximum = 999999;

            numMinQuantity.Minimum = 0;
            numMinQuantity.Maximum = 999999;
        }

        private async Task LoadCategoriesAndSuppliers()
        {
            try
            {
                // Load categories
                var categories = await _inventoryCommands.GetCategoriesListAsync();
                _categories = categories.ToList();
                cboCategory.Items.Clear();
                cboCategory.Items.Add("");
                foreach (var category in _categories)
                {
                    cboCategory.Items.Add(category);
                }

                // Load suppliers
                var suppliers = await _inventoryCommands.GetSuppliersListAsync();
                _suppliers = suppliers.ToList();
                cboSupplier.Items.Clear();
                cboSupplier.Items.Add("");
                foreach (var supplier in _suppliers)
                {
                    cboSupplier.Items.Add(supplier);
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des catégories et fournisseurs: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void FillFormWithPartData()
        {
            txtName.Text = _part.Name;
            txtCode.Text = _part.Code;
            txtDescription.Text = _part.Description;
            numPurchasePrice.Value = _part.Purchase_Price;
            numSellingPrice.Value = _part.Selling_Price;
            numQuantity.Value = _part.Quantity;
            numMinQuantity.Value = _part.Min_Quantity;

            if (!string.IsNullOrEmpty(_part.Category))
            {
                int index = cboCategory.Items.IndexOf(_part.Category);
                if (index >= 0)
                {
                    cboCategory.SelectedIndex = index;
                }
                else
                {
                    cboCategory.Text = _part.Category;
                }
            }

            if (!string.IsNullOrEmpty(_part.Supplier))
            {
                int index = cboSupplier.Items.IndexOf(_part.Supplier);
                if (index >= 0)
                {
                    cboSupplier.SelectedIndex = index;
                }
                else
                {
                    cboSupplier.Text = _part.Supplier;
                }
            }
        }

        private async Task<bool> ValidateForm()
        {
            errorProvider.Clear();
            bool isValid = true;

            // Validate name
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                errorProvider.SetError(txtName, "Le nom est obligatoire");
                isValid = false;
                txtName.Focus();
            }

            // Validate code if provided
            if (!string.IsNullOrWhiteSpace(txtCode.Text))
            {
                // Check if code already exists
                bool codeExists = await _inventoryCommands.CodeExistsAsync(txtCode.Text.Trim(), _isEditMode ? _part.Id : 0);
                if (codeExists)
                {
                    errorProvider.SetError(txtCode, "Ce code est déjà utilisé par un autre article");
                    isValid = false;
                    if (string.IsNullOrWhiteSpace(txtName.Text) == false)
                        txtCode.Focus();
                }
            }

            // Validate purchase price
            if (numPurchasePrice.Value <= 0)
            {
                errorProvider.SetError(numPurchasePrice, "Le prix d'achat doit être supérieur à 0");
                isValid = false;
                if (string.IsNullOrWhiteSpace(txtName.Text) == false &&
                    string.IsNullOrWhiteSpace(txtCode.Text) == false)
                    numPurchasePrice.Focus();
            }

            // Validate selling price
            if (numSellingPrice.Value <= 0)
            {
                errorProvider.SetError(numSellingPrice, "Le prix de vente doit être supérieur à 0");
                isValid = false;
                if (string.IsNullOrWhiteSpace(txtName.Text) == false &&
                    string.IsNullOrWhiteSpace(txtCode.Text) == false &&
                    numPurchasePrice.Value > 0)
                    numSellingPrice.Focus();
            }

            return isValid;
        }

        private void UpdatePartFromForm()
        {
            _part.Name = txtName.Text.Trim();
            _part.Code = txtCode.Text.Trim();
            _part.Description = txtDescription.Text.Trim();
            _part.Purchase_Price = numPurchasePrice.Value;
            _part.Selling_Price = numSellingPrice.Value;
            _part.Quantity = (int)numQuantity.Value;
            _part.Min_Quantity = (int)numMinQuantity.Value;
            _part.Category = cboCategory.Text.Trim();
            _part.Supplier = cboSupplier.Text.Trim();
        }

        private async Task<bool> SavePart()
        {
            try
            {
                if (!await ValidateForm())
                    return false;

                UpdatePartFromForm();

                if (_isEditMode)
                {
                    // Check if quantity has changed
                    int originalQuantity = 0;
                    var originalPart = await _inventoryCommands.GetByIdAsync(_part.Id);
                    if (originalPart != null)
                    {
                        originalQuantity = originalPart.Quantity;
                    }

                    bool success = await _inventoryCommands.UpdateAsync(_part);
                    if (success)
                    {
                        // Add inventory transaction if quantity has changed
                        if (_part.Quantity != originalQuantity)
                        {
                            int quantityDifference = _part.Quantity - originalQuantity;
                            TransactionType transactionType = quantityDifference > 0 ?
                                TransactionType.in_transaction : TransactionType.out_transaction;

                            // Create more detailed reference and notes
                            string reference = quantityDifference > 0 ? "Ajout de stock" : "Retrait de stock";
                            string notes = $"Modification de la quantité de {originalQuantity} à {_part.Quantity} - {_part.Name} ({_part.Code})";

                            // Get current user ID if available
                            int? userId = null;
                            if (Properties.Settings.Default.CurrentUserId > 0)
                            {
                                userId = Properties.Settings.Default.CurrentUserId;
                            }

                            await _inventoryCommands.AddInventoryTransactionAsync(
                                _part.Id,
                                transactionType,
                                Math.Abs(quantityDifference),
                                reference,
                                notes,
                                userId);
                        }

                        KryptonMessageBox.Show("L'article a été mis à jour avec succès", "Succès",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        HasChanges = true;
                        this.DialogResult = DialogResult.OK;
                    }
                    else
                    {
                        KryptonMessageBox.Show("Erreur lors de la mise à jour de l'article", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return false;
                    }
                }
                else
                {
                    int partId = await _inventoryCommands.InsertAsync(_part);
                    if (partId > 0)
                    {
                        _part.Id = partId;
                        _isEditMode = true;

                        // Add inventory transaction if quantity is greater than zero
                        if (_part.Quantity > 0)
                        {
                            // Create more detailed reference and notes
                            string reference = "Stock initial";
                            string notes = $"Ajout de stock initial pour l'article: {_part.Name} ({_part.Code}) - Quantité: {_part.Quantity}";

                            // Get current user ID if available
                            int? userId = null;
                            if (Properties.Settings.Default.CurrentUserId > 0)
                            {
                                userId = Properties.Settings.Default.CurrentUserId;
                            }

                            await _inventoryCommands.AddInventoryTransactionAsync(
                                _part.Id,
                                TransactionType.in_transaction,
                                _part.Quantity,
                                reference,
                                notes,
                                userId);
                        }

                        KryptonMessageBox.Show("L'article a été ajouté avec succès", "Succès",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        HasChanges = true;
                        this.DialogResult = DialogResult.OK;

                        // Keep quantity field enabled to allow future adjustments
                    }
                    else
                    {
                        KryptonMessageBox.Show("Erreur lors de l'ajout de l'article", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return false;
                    }
                }

                return true;
            }
            catch (MySqlException ex) when (ex.Number == 1062)
            {
                string errorMessage = ex.Message.ToLower();
                if (errorMessage.Contains("code"))
                {
                    KryptonMessageBox.Show("Ce code est déjà utilisé par un autre article", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    txtCode.Focus();
                }
                else
                {
                    KryptonMessageBox.Show($"Erreur de duplication: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
                return false;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'enregistrement de l'article: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                return false;
            }
        }

        private void ResetForm()
        {
            txtName.Text = string.Empty;
            txtCode.Text = string.Empty;
            txtDescription.Text = string.Empty;
            numPurchasePrice.Value = 0;
            numSellingPrice.Value = 0;
            numQuantity.Value = 0;
            numMinQuantity.Value = 1;
            cboCategory.SelectedIndex = -1;
            cboCategory.Text = string.Empty;
            cboSupplier.SelectedIndex = -1;
            cboSupplier.Text = string.Empty;

            _part = new Part();
            _isEditMode = false;
            numQuantity.Enabled = true;

            this.Text = "Ajouter un article";

            errorProvider.Clear();

            txtName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            await SavePart();
        }

        private async void btnSaveAndClose_Click(object sender, EventArgs e)
        {
            if (await SavePart())
            {
                this.Close();
            }
        }

        private async void btnSaveAndNew_Click(object sender, EventArgs e)
        {
            if (await SavePart())
            {
                ResetForm();
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
