using IRepairIT.Data.Common;
using IRepairIT.Models;
using IRepairIT.Properties;
using Krypton.Toolkit;
using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FMAIN
{
    public partial class FRM_SESSION_INFO : KryptonForm
    {
        private readonly UserCommands _userCommands;
        private User _currentUser;
        private UserSession _currentSession;

        public FRM_SESSION_INFO()
        {
            InitializeComponent();
            _userCommands = new UserCommands();
        }

        private async void FRM_SESSION_INFO_Load(object sender, EventArgs e)
        {
            await LoadUserInfo();
        }

        private async Task LoadUserInfo()
        {
            try
            {
                if (Settings.Default.CurrentUserId > 0)
                {
                    // Load user information
                    _currentUser = await _userCommands.GetByIdAsync(Settings.Default.CurrentUserId);
                    if (_currentUser != null)
                    {
                        lblUsername.Text = _currentUser.Username;
                        lblFullName.Text = _currentUser.Full_Name;
                        lblEmail.Text = _currentUser.Email;
                        lblPhone.Text = _currentUser.Phone;
                        lblRole.Text = _currentUser.Role.ToString();
                        lblStatus.Text = _currentUser.Status.ToString();
                    }

                    // Load session information
                    _currentSession = await _userCommands.GetActiveSessionAsync(Settings.Default.CurrentUserId);
                    if (_currentSession != null)
                    {
                        lblSessionId.Text = _currentSession.SessionId;
                        lblLoginTime.Text = _currentSession.LoginTime.ToString("dd/MM/yyyy HH:mm:ss");
                        lblLastActivity.Text = _currentSession.LastActivity.ToString("dd/MM/yyyy HH:mm:ss");
                        lblIpAddress.Text = _currentSession.IpAddress;
                        lblHostname.Text = _currentSession.Hostname;
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des informations utilisateur: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void BtnEndSession_Click(object sender, EventArgs e)
        {
            try
            {
                var result = KryptonMessageBox.Show(
                    "Êtes-vous sûr de vouloir mettre fin à cette session?",
                    "Confirmation",
                    KryptonMessageBoxButtons.YesNo,
                    KryptonMessageBoxIcon.Question
                );

                if (result == DialogResult.Yes)
                {
                    // End the session
                    await _userCommands.CloseUserSessionAsync(Settings.Default.CurrentUserId);

                    // Reset user ID
                    Settings.Default.CurrentUserId = 0;
                    Settings.Default.Save();

                    KryptonMessageBox.Show("Session terminée avec succès", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                    // Close this form and the main form
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la fermeture de la session: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadUserInfo();
        }
    }
}
