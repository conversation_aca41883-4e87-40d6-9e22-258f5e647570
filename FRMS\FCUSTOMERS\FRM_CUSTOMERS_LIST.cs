﻿using FastReport;
using IRepairIT.Data.Common;
using IRepairIT.Models;
using IRepairIT.Utilities;
using Krypton.Toolkit;
using System;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FCUSTOMERS
{
    public partial class FRM_CUSTOMERS_LIST : KryptonForm
    {
        private readonly CustomerCommands _Cmd;
        private int _pageSize;
        private int _currentPage = 1;
        private int _totalRows = 0;
        private string _searchTerm = "";

        public FRM_CUSTOMERS_LIST()
        {
            InitializeComponent();
            _Cmd = new CustomerCommands();

            // Set default page size to 100
            kryptonComboBox1.Items.AddRange(new object[] { "10", "25", "50", "100", "250", "500" });
            kryptonComboBox1.SelectedIndex = 3; // Select 100 by default
            _pageSize = 100;

            // Set up event handlers
            kryptonComboBox1.SelectedIndexChanged += CmbPageView_SelectedIndexChanged;
            TXTSearch.TextChanged += TXTSearch_TextChanged;

            // Set pagination button text
            btnFirst.Text = "Premier";
            btnPrev.Text = "Précédent";
            btnNext.Text = "Suivant";
            btnLast.Text = "Dernier";

            // Set up button click handlers
            btnFirst.Click += BtnFirst_Click;
            btnPrev.Click += BtnPrev_Click;
            btnNext.Click += BtnNext_Click;
            btnLast.Click += BtnLast_Click;
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnSearch.Click += BtnSearch_Click;
            btnClearSearch.Click += BtnClearSearch_Click;
        }
        public async Task LoadCustomers()
        {
            try
            {
                // Get page size from combo box
                if (kryptonComboBox1.SelectedItem != null)
                {
                    _pageSize = Convert.ToInt32(kryptonComboBox1.Text);
                }

                // Calculate offset based on current page and page size
                int offset = (_currentPage - 1) * _pageSize;

                // Get customers with pagination
                var result = await _Cmd.GetALL(_searchTerm, offset, _pageSize);
                _totalRows = await _Cmd.GetCount(_searchTerm);

                // Set data source
                kryptonDataGridView1.DataSource = result;

                // Hide unnecessary columns
                kryptonDataGridView1.Columns[nameof(Customer.address)].Visible = false;
                kryptonDataGridView1.Columns[nameof(Customer.notes)].Visible = false;
                kryptonDataGridView1.Columns[nameof(Customer.updated_at)].Visible = false;

                // Update pagination info
                kryptonHeaderGroup1.ValuesSecondary.Heading = $"Total: {_totalRows} - Page: {_currentPage}/{Math.Ceiling((double)_totalRows / _pageSize)}";

                // Enable/disable pagination buttons based on current page
                UpdatePaginationButtons();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des clients: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
        private async void FRM_CUSTOMERS_LIST_Load(object sender, EventArgs e)
        {
            try
            {
                // Set row height
                kryptonDataGridView1.RowTemplate.Height = 32;

                // Load customers data
                await LoadCustomers();

                // Apply enhanced styling
                DGV_STYLE.DesignDGV(kryptonDataGridView1);

                // Set up context menu
                SetupContextMenu();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du formulaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void SetupContextMenu()
        {
            try
            {
                // Create a standard context menu strip
                ContextMenuStrip contextMenuStrip = new ContextMenuStrip();

                // Add items to the standard context menu
                ToolStripMenuItem refreshItem = new ToolStripMenuItem("Rafraîchir", null, async (s, e) => await LoadCustomers());
                ToolStripMenuItem addItem = new ToolStripMenuItem("Ajouter", null, BtnAdd_Click);
                ToolStripMenuItem editItem = new ToolStripMenuItem("Modifier", null, BtnEdit_Click);
                ToolStripMenuItem deleteItem = new ToolStripMenuItem("Supprimer", null, BtnDelete_Click);
                ToolStripSeparator separator = new ToolStripSeparator();
                ToolStripMenuItem printItem = new ToolStripMenuItem("Imprimer", null, PrintDataGridView);

                // Set icons if available
                if (btnAdd.Image != null) addItem.Image = btnAdd.Image;

                // Add items to the menu
                contextMenuStrip.Items.Add(refreshItem);
                contextMenuStrip.Items.Add(addItem);
                contextMenuStrip.Items.Add(editItem);
                contextMenuStrip.Items.Add(deleteItem);
                contextMenuStrip.Items.Add(separator);
                contextMenuStrip.Items.Add(printItem);

                // Attach the context menu to the DataGridView
                kryptonDataGridView1.ContextMenuStrip = contextMenuStrip;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la configuration du menu contextuel: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void UpdatePaginationButtons()
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);

            // For Krypton ButtonSpecHeaderGroup, we need to use ButtonEnabled enum instead of bool
            btnFirst.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnPrev.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnNext.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
            btnLast.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
        }

        private async void CmbPageView_SelectedIndexChanged(object sender, EventArgs e)
        {
            _currentPage = 1; // Reset to first page when changing page size
            await LoadCustomers();
        }

        private async void TXTSearch_TextChanged(object sender, EventArgs e)
        {
            _searchTerm = TXTSearch.Text.Trim();
            _currentPage = 1; // Reset to first page when searching
            await LoadCustomers();
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            _searchTerm = TXTSearch.Text.Trim();
            _currentPage = 1;
            await LoadCustomers();
        }

        private async void BtnClearSearch_Click(object sender, EventArgs e)
        {
            TXTSearch.Clear();
            _searchTerm = "";
            _currentPage = 1;
            await LoadCustomers();
        }

        private async void BtnFirst_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage = 1;
                await LoadCustomers();
            }
        }

        private async void BtnPrev_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadCustomers();
            }
        }

        private async void BtnNext_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage++;
                await LoadCustomers();
            }
        }

        private async void BtnLast_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage = totalPages;
                await LoadCustomers();
            }
        }

        private async void BtnAdd_Click(object sender, EventArgs e)
        {
            var frm = new FRM_CUSTOMER();
            frm.ShowDialog();

            if (frm.HasChanges)
            {
                await LoadCustomers();
            }
        }

        private async void BtnEdit_Click(object sender, EventArgs e)
        {
            if (kryptonDataGridView1.SelectedRows.Count > 0)
            {
                int customerId = Convert.ToInt32(kryptonDataGridView1.SelectedRows[0].Cells["id"].Value);
                var frm = new FRM_CUSTOMER(customerId);
                frm.ShowDialog();

                if (frm.HasChanges)
                {
                    await LoadCustomers();
                }
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un client à modifier", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (kryptonDataGridView1.SelectedRows.Count > 0)
            {
                int customerId = Convert.ToInt32(kryptonDataGridView1.SelectedRows[0].Cells["id"].Value);
                string customerName = kryptonDataGridView1.SelectedRows[0].Cells["name"].Value.ToString();

                var result = KryptonMessageBox.Show($"Êtes-vous sûr de vouloir supprimer le client '{customerName}'?", "Confirmation",
                    KryptonMessageBoxButtons.YesNo, KryptonMessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        bool success = await _Cmd.DeleteAsync(customerId);
                        if (success)
                        {
                            KryptonMessageBox.Show("Le client a été supprimé avec succès", "Succès",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                            // Refresh the list after deleting
                            await LoadCustomers();
                        }
                        else
                        {
                            KryptonMessageBox.Show("Erreur lors de la suppression du client", "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        KryptonMessageBox.Show($"Erreur lors de la suppression du client: {ex.Message}", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un client à supprimer", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private void PrintDataGridView(object sender, EventArgs e)
        {
            try
            {
                // Check if there's data to print
                if (kryptonDataGridView1.Rows.Count == 0)
                {
                    KryptonMessageBox.Show("Aucune donnée à imprimer", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                // Get the report template file path
                string reportFile = Path.Combine(Application.StartupPath, "Reports", "CustomersList.frx");

                if (!File.Exists(reportFile))
                {
                    KryptonMessageBox.Show("Le fichier de rapport n'existe pas: " + reportFile, "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    return;
                }

                // Create a new report
                Report report = new Report();

                // Load the report template
                report.Load(reportFile);

                // Convert DataGridView data to DataTable
                DataTable dt = new DataTable("Customers");

                // Add columns for the report
                dt.Columns.Add("Nom", typeof(string));
                dt.Columns.Add("Telephone", typeof(string));
                dt.Columns.Add("Email", typeof(string));
                dt.Columns.Add("DateInscription", typeof(string));

                // Add rows from DataGridView
                foreach (DataGridViewRow row in kryptonDataGridView1.Rows)
                {
                    if (!row.IsNewRow)
                    {
                        DataRow dataRow = dt.NewRow();
                        dataRow["Nom"] = row.Cells[nameof(Customer.name)].Value?.ToString() ?? "";
                        dataRow["Telephone"] = row.Cells[nameof(Customer.phone)].Value?.ToString() ?? "";
                        dataRow["Email"] = row.Cells[nameof(Customer.email)].Value?.ToString() ?? "";
                        dataRow["DateInscription"] = row.Cells[nameof(Customer.created_at)].Value != null
                            ? Convert.ToDateTime(row.Cells[nameof(Customer.created_at)].Value).ToString("dd/MM/yyyy")
                            : "";
                        dt.Rows.Add(dataRow);
                    }
                }

                // Register the data source
                report.RegisterData(dt, "Customers");

                // Set report parameters
                report.SetParameterValue("Date", DateTime.Now.ToString("dd/MM/yyyy"));
                report.SetParameterValue("TotalCustomers", dt.Rows.Count.ToString());

                // Prepare and show the report
                report.Prepare();
                report.Show();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'impression: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
    }
}
