﻿namespace IRepairIT.FRMS.FINVENTORY
{
    public partial class FRM_INVENTORY_LIST
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FRM_INVENTORY_LIST));
            this.btnFirst = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnPrev = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnNext = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnLast = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.kryptonComboBox1 = new Krypton.Toolkit.KryptonComboBox();
            this.kryptonHeaderGroup1 = new Krypton.Toolkit.KryptonHeaderGroup();
            this.btnAdd = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnEdit = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnDelete = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.kryptonDataGridView1 = new Krypton.Toolkit.KryptonDataGridView();
            this.kryptonPanel2 = new Krypton.Toolkit.KryptonPanel();
            this.kryptonTableLayoutPanel1 = new Krypton.Toolkit.KryptonTableLayoutPanel();
            this.kryptonPanel5 = new Krypton.Toolkit.KryptonPanel();
            this.LblTotalActif = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel6 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonPanel4 = new Krypton.Toolkit.KryptonPanel();
            this.LblLowStock = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel4 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonPanel3 = new Krypton.Toolkit.KryptonPanel();
            this.lblCategorie = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel2 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonPanel1 = new Krypton.Toolkit.KryptonPanel();
            this.lblTotalCount = new Krypton.Toolkit.KryptonLabel();
            this.lblTotalTitle = new Krypton.Toolkit.KryptonLabel();
            this.TXTSearch = new Krypton.Toolkit.KryptonTextBox();
            this.btnSearch = new Krypton.Toolkit.ButtonSpecAny();
            this.btnClearSearch = new Krypton.Toolkit.ButtonSpecAny();
            this.kryptonContextMenu1 = new Krypton.Toolkit.KryptonContextMenu();
            this.kryptonContextMenuItems1 = new Krypton.Toolkit.KryptonContextMenuItems();
            this.kryptonContextMenuItem1 = new Krypton.Toolkit.KryptonContextMenuItem();
            this.kryptonContextMenuItem2 = new Krypton.Toolkit.KryptonContextMenuItem();
            this.kryptonContextMenuItem3 = new Krypton.Toolkit.KryptonContextMenuItem();
            this.kryptonContextMenuItem4 = new Krypton.Toolkit.KryptonContextMenuItem();
            this.kryptonContextMenuSeparator1 = new Krypton.Toolkit.KryptonContextMenuSeparator();
            this.kryptonContextMenuItem5 = new Krypton.Toolkit.KryptonContextMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1.Panel)).BeginInit();
            this.kryptonHeaderGroup1.Panel.SuspendLayout();
            this.kryptonHeaderGroup1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel2)).BeginInit();
            this.kryptonPanel2.SuspendLayout();
            this.kryptonTableLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel5)).BeginInit();
            this.kryptonPanel5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel4)).BeginInit();
            this.kryptonPanel4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel3)).BeginInit();
            this.kryptonPanel3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).BeginInit();
            this.kryptonPanel1.SuspendLayout();
            this.SuspendLayout();
            //
            // kryptonHeaderGroup1
            //
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnAdd);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnEdit);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnDelete);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnFirst);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnPrev);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnNext);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnLast);
            this.kryptonHeaderGroup1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonHeaderGroup1.Location = new System.Drawing.Point(0, 0);
            this.kryptonHeaderGroup1.Name = "kryptonHeaderGroup1";
            //
            // kryptonHeaderGroup1.Panel
            //
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.kryptonDataGridView1);
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.kryptonPanel2);
            this.kryptonHeaderGroup1.Size = new System.Drawing.Size(800, 450);
            this.kryptonHeaderGroup1.TabIndex = 4;
            this.kryptonHeaderGroup1.ValuesPrimary.Heading = "Gestion de l\'inventaire";
            this.kryptonHeaderGroup1.ValuesPrimary.Image = null;
            this.kryptonHeaderGroup1.ValuesSecondary.Heading = "";
            //
            // btnAdd
            //
            this.btnAdd.Image = global::IRepairIT.Properties.Resources.button_circle_add;
            this.btnAdd.Style = Krypton.Toolkit.PaletteButtonStyle.Standalone;
            this.btnAdd.Text = "Ajouter un utilisateur";
            this.btnAdd.UniqueName = "8833e85e72444aeb812cb717fdc0b39c";
            this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
            //
            // btnEdit
            //
            this.btnEdit.Image = global::IRepairIT.Properties.Resources.tool_pencil;
            this.btnEdit.Style = Krypton.Toolkit.PaletteButtonStyle.Standalone;
            this.btnEdit.Text = "Modifier";
            this.btnEdit.UniqueName = "bb4bc62dda7046dabd9cdcdd316134ff";
            this.btnEdit.Click += new System.EventHandler(this.btnEdit_Click);
            //
            // btnDelete
            //
            this.btnDelete.Image = global::IRepairIT.Properties.Resources.trash;
            this.btnDelete.Style = Krypton.Toolkit.PaletteButtonStyle.Standalone;
            this.btnDelete.Text = "Supprimer";
            this.btnDelete.UniqueName = "7185fa9cee2e479d9ecc6064ad018e0f";
            this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click);
            //
            // btnFirst
            //
            this.btnFirst.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowLeft;
            this.btnFirst.UniqueName = "btnFirst";
            //
            // btnPrev
            //
            this.btnPrev.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowLeft;
            this.btnPrev.UniqueName = "btnPrev";
            //
            // btnNext
            //
            this.btnNext.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowRight;
            this.btnNext.UniqueName = "btnNext";
            //
            // btnLast
            //
            this.btnLast.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowRight;
            this.btnLast.UniqueName = "btnLast";
            //
            // kryptonComboBox1
            //
            this.kryptonComboBox1.DropDownWidth = 121;
            this.kryptonComboBox1.Location = new System.Drawing.Point(401, 106);
            this.kryptonComboBox1.Name = "kryptonComboBox1";
            this.kryptonComboBox1.Size = new System.Drawing.Size(121, 25);
            this.kryptonComboBox1.TabIndex = 2;
            //
            // kryptonDataGridView1
            //
            this.kryptonDataGridView1.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.kryptonDataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonDataGridView1.Location = new System.Drawing.Point(0, 143);
            this.kryptonDataGridView1.Name = "kryptonDataGridView1";
            this.kryptonDataGridView1.Size = new System.Drawing.Size(798, 269);
            this.kryptonDataGridView1.TabIndex = 2;
            //
            // kryptonPanel2
            //
            this.kryptonPanel2.Controls.Add(this.kryptonTableLayoutPanel1);
            this.kryptonPanel2.Controls.Add(this.TXTSearch);
            this.kryptonPanel2.Controls.Add(this.kryptonComboBox1);
            this.kryptonPanel2.Dock = System.Windows.Forms.DockStyle.Top;
            this.kryptonPanel2.Location = new System.Drawing.Point(0, 0);
            this.kryptonPanel2.Name = "kryptonPanel2";
            this.kryptonPanel2.Size = new System.Drawing.Size(798, 143);
            this.kryptonPanel2.TabIndex = 1;
            //
            // kryptonTableLayoutPanel1
            //
            this.kryptonTableLayoutPanel1.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("kryptonTableLayoutPanel1.BackgroundImage")));
            this.kryptonTableLayoutPanel1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.kryptonTableLayoutPanel1.ColumnCount = 4;
            this.kryptonTableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel1.Controls.Add(this.kryptonPanel5, 3, 0);
            this.kryptonTableLayoutPanel1.Controls.Add(this.kryptonPanel4, 2, 0);
            this.kryptonTableLayoutPanel1.Controls.Add(this.kryptonPanel3, 1, 0);
            this.kryptonTableLayoutPanel1.Controls.Add(this.kryptonPanel1, 0, 0);
            this.kryptonTableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.kryptonTableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.kryptonTableLayoutPanel1.Name = "kryptonTableLayoutPanel1";
            this.kryptonTableLayoutPanel1.RowCount = 1;
            this.kryptonTableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.kryptonTableLayoutPanel1.Size = new System.Drawing.Size(798, 100);
            this.kryptonTableLayoutPanel1.TabIndex = 1;
            //
            // kryptonPanel5
            //
            this.kryptonPanel5.Controls.Add(this.LblTotalActif);
            this.kryptonPanel5.Controls.Add(this.kryptonLabel6);
            this.kryptonPanel5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel5.Location = new System.Drawing.Point(600, 3);
            this.kryptonPanel5.Name = "kryptonPanel5";
            this.kryptonPanel5.Size = new System.Drawing.Size(195, 94);
            this.kryptonPanel5.StateNormal.Color2 = System.Drawing.Color.Gray;
            this.kryptonPanel5.StateNormal.ColorStyle = Krypton.Toolkit.PaletteColorStyle.SolidAllLine;
            this.kryptonPanel5.TabIndex = 3;
            //
            // LblTotalActif
            //
            this.LblTotalActif.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.LblTotalActif.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.LblTotalActif.Location = new System.Drawing.Point(79, 35);
            this.LblTotalActif.Name = "LblTotalActif";
            this.LblTotalActif.Size = new System.Drawing.Size(36, 49);
            this.LblTotalActif.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 24F, System.Drawing.FontStyle.Bold);
            this.LblTotalActif.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.LblTotalActif.TabIndex = 3;
            this.LblTotalActif.Values.Text = "0";
            //
            // kryptonLabel6
            //
            this.kryptonLabel6.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.kryptonLabel6.Location = new System.Drawing.Point(40, 10);
            this.kryptonLabel6.Name = "kryptonLabel6";
            this.kryptonLabel6.Size = new System.Drawing.Size(114, 22);
            this.kryptonLabel6.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.kryptonLabel6.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel6.TabIndex = 2;
            this.kryptonLabel6.Values.Text = "Rupture de stock";
            //
            // kryptonPanel4
            //
            this.kryptonPanel4.Controls.Add(this.LblLowStock);
            this.kryptonPanel4.Controls.Add(this.kryptonLabel4);
            this.kryptonPanel4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel4.Location = new System.Drawing.Point(401, 3);
            this.kryptonPanel4.Name = "kryptonPanel4";
            this.kryptonPanel4.Size = new System.Drawing.Size(193, 94);
            this.kryptonPanel4.StateNormal.Color2 = System.Drawing.Color.Gray;
            this.kryptonPanel4.StateNormal.ColorStyle = Krypton.Toolkit.PaletteColorStyle.SolidAllLine;
            this.kryptonPanel4.TabIndex = 2;
            //
            // LblLowStock
            //
            this.LblLowStock.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.LblLowStock.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.LblLowStock.Location = new System.Drawing.Point(78, 35);
            this.LblLowStock.Name = "LblLowStock";
            this.LblLowStock.Size = new System.Drawing.Size(36, 49);
            this.LblLowStock.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 24F, System.Drawing.FontStyle.Bold);
            this.LblLowStock.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.LblLowStock.TabIndex = 3;
            this.LblLowStock.Values.Text = "0";
            //
            // kryptonLabel4
            //
            this.kryptonLabel4.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.kryptonLabel4.Location = new System.Drawing.Point(56, 10);
            this.kryptonLabel4.Name = "kryptonLabel4";
            this.kryptonLabel4.Size = new System.Drawing.Size(81, 22);
            this.kryptonLabel4.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.kryptonLabel4.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel4.TabIndex = 2;
            this.kryptonLabel4.Values.Text = "Stock faible";
            //
            // kryptonPanel3
            //
            this.kryptonPanel3.Controls.Add(this.lblCategorie);
            this.kryptonPanel3.Controls.Add(this.kryptonLabel2);
            this.kryptonPanel3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel3.Location = new System.Drawing.Point(202, 3);
            this.kryptonPanel3.Name = "kryptonPanel3";
            this.kryptonPanel3.Size = new System.Drawing.Size(193, 94);
            this.kryptonPanel3.StateNormal.Color2 = System.Drawing.Color.Gray;
            this.kryptonPanel3.StateNormal.ColorStyle = Krypton.Toolkit.PaletteColorStyle.SolidAllLine;
            this.kryptonPanel3.TabIndex = 1;
            //
            // lblCategorie
            //
            this.lblCategorie.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblCategorie.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.lblCategorie.Location = new System.Drawing.Point(78, 35);
            this.lblCategorie.Name = "lblCategorie";
            this.lblCategorie.Size = new System.Drawing.Size(36, 49);
            this.lblCategorie.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 24F, System.Drawing.FontStyle.Bold);
            this.lblCategorie.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.lblCategorie.TabIndex = 3;
            this.lblCategorie.Values.Text = "0";
            //
            // kryptonLabel2
            //
            this.kryptonLabel2.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.kryptonLabel2.Location = new System.Drawing.Point(58, 10);
            this.kryptonLabel2.Name = "kryptonLabel2";
            this.kryptonLabel2.Size = new System.Drawing.Size(76, 22);
            this.kryptonLabel2.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.kryptonLabel2.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel2.TabIndex = 2;
            this.kryptonLabel2.Values.Text = "Catégories";
            //
            // kryptonPanel1
            //
            this.kryptonPanel1.Controls.Add(this.lblTotalCount);
            this.kryptonPanel1.Controls.Add(this.lblTotalTitle);
            this.kryptonPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel1.Location = new System.Drawing.Point(3, 3);
            this.kryptonPanel1.Name = "kryptonPanel1";
            this.kryptonPanel1.Size = new System.Drawing.Size(193, 94);
            this.kryptonPanel1.StateNormal.Color2 = System.Drawing.Color.Gray;
            this.kryptonPanel1.StateNormal.ColorStyle = Krypton.Toolkit.PaletteColorStyle.SolidAllLine;
            this.kryptonPanel1.TabIndex = 0;
            //
            // lblTotalCount
            //
            this.lblTotalCount.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblTotalCount.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.lblTotalCount.Location = new System.Drawing.Point(78, 35);
            this.lblTotalCount.Name = "lblTotalCount";
            this.lblTotalCount.Size = new System.Drawing.Size(36, 49);
            this.lblTotalCount.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 24F, System.Drawing.FontStyle.Bold);
            this.lblTotalCount.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.lblTotalCount.TabIndex = 3;
            this.lblTotalCount.Values.Text = "0";
            //
            // lblTotalTitle
            //
            this.lblTotalTitle.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblTotalTitle.Location = new System.Drawing.Point(42, 10);
            this.lblTotalTitle.Name = "lblTotalTitle";
            this.lblTotalTitle.Size = new System.Drawing.Size(108, 22);
            this.lblTotalTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.lblTotalTitle.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.lblTotalTitle.TabIndex = 2;
            this.lblTotalTitle.Values.Text = "Total des pièces";
            //
            // TXTSearch
            //
            this.TXTSearch.ButtonSpecs.Add(this.btnSearch);
            this.TXTSearch.ButtonSpecs.Add(this.btnClearSearch);
            this.TXTSearch.CueHint.Color1 = System.Drawing.Color.Silver;
            this.TXTSearch.CueHint.CueHintText = "Rechercher ...";
            this.TXTSearch.Location = new System.Drawing.Point(5, 106);
            this.TXTSearch.Name = "TXTSearch";
            this.TXTSearch.Size = new System.Drawing.Size(390, 29);
            this.TXTSearch.StateCommon.Border.Rounding = 5F;
            this.TXTSearch.TabIndex = 0;
            this.TXTSearch.TextChanged += new System.EventHandler(this.TXTSearch_TextChanged);
            //
            // btnSearch
            //
            this.btnSearch.ToolTipTitle = "Rechercher";
            this.btnSearch.UniqueName = "58f205b2a5e84fd6af6c035b128ba21a";
            //
            // btnClearSearch
            //
            this.btnClearSearch.ToolTipTitle = "Effacer la recherche";
            this.btnClearSearch.UniqueName = "ClearSearch";
            //
            // kryptonContextMenu1
            //
            this.kryptonContextMenu1.Items.AddRange(new Krypton.Toolkit.KryptonContextMenuItemBase[] {
            this.kryptonContextMenuItems1});
            //
            // kryptonContextMenuItems1
            //
            this.kryptonContextMenuItems1.Items.AddRange(new Krypton.Toolkit.KryptonContextMenuItemBase[] {
            this.kryptonContextMenuItem1,
            this.kryptonContextMenuItem2,
            this.kryptonContextMenuItem3,
            this.kryptonContextMenuItem4,
            this.kryptonContextMenuSeparator1,
            this.kryptonContextMenuItem5});
            this.kryptonContextMenuItems1.Text = "";
            //
            // kryptonContextMenuItem1
            //
            this.kryptonContextMenuItem1.Image = ((System.Drawing.Image)(resources.GetObject("kryptonContextMenuItem1.Image")));
            this.kryptonContextMenuItem1.ShortcutKeys = System.Windows.Forms.Keys.F5;
            this.kryptonContextMenuItem1.Text = "Rafraîchir";
            //
            // kryptonContextMenuItem2
            //
            this.kryptonContextMenuItem2.Image = ((System.Drawing.Image)(resources.GetObject("kryptonContextMenuItem2.Image")));
            this.kryptonContextMenuItem2.ShortcutKeys = System.Windows.Forms.Keys.F1;
            this.kryptonContextMenuItem2.Text = "Ajouter";
            //
            // kryptonContextMenuItem3
            //
            this.kryptonContextMenuItem3.Image = ((System.Drawing.Image)(resources.GetObject("kryptonContextMenuItem3.Image")));
            this.kryptonContextMenuItem3.ShortcutKeys = System.Windows.Forms.Keys.F2;
            this.kryptonContextMenuItem3.Text = "Modifier";
            //
            // kryptonContextMenuItem4
            //
            this.kryptonContextMenuItem4.Image = ((System.Drawing.Image)(resources.GetObject("kryptonContextMenuItem4.Image")));
            this.kryptonContextMenuItem4.ShortcutKeys = System.Windows.Forms.Keys.Delete;
            this.kryptonContextMenuItem4.Text = "Supprimer";
            //
            // kryptonContextMenuSeparator1
            //
            this.kryptonContextMenuSeparator1.Text = "";
            //
            // kryptonContextMenuItem5
            //
            this.kryptonContextMenuItem5.Image = ((System.Drawing.Image)(resources.GetObject("kryptonContextMenuItem5.Image")));
            this.kryptonContextMenuItem5.Text = "Dupliquer";
            //
            // FRM_INVENTORY_LIST
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.kryptonHeaderGroup1);
            this.Name = "FRM_INVENTORY_LIST";
            this.Text = "Gestion de l\'inventaire";
            this.Load += new System.EventHandler(this.FRM_INVENTORY_LIST_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1.Panel)).EndInit();
            this.kryptonHeaderGroup1.Panel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1)).EndInit();
            this.kryptonHeaderGroup1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel2)).EndInit();
            this.kryptonPanel2.ResumeLayout(false);
            this.kryptonPanel2.PerformLayout();
            this.kryptonTableLayoutPanel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel5)).EndInit();
            this.kryptonPanel5.ResumeLayout(false);
            this.kryptonPanel5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel4)).EndInit();
            this.kryptonPanel4.ResumeLayout(false);
            this.kryptonPanel4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel3)).EndInit();
            this.kryptonPanel3.ResumeLayout(false);
            this.kryptonPanel3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).EndInit();
            this.kryptonPanel1.ResumeLayout(false);
            this.kryptonPanel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonHeaderGroup kryptonHeaderGroup1;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnAdd;
        private Krypton.Toolkit.KryptonDataGridView kryptonDataGridView1;
        private Krypton.Toolkit.KryptonPanel kryptonPanel2;
        private Krypton.Toolkit.KryptonTableLayoutPanel kryptonTableLayoutPanel1;
        private Krypton.Toolkit.KryptonPanel kryptonPanel5;
        private Krypton.Toolkit.KryptonLabel LblTotalActif;
        private Krypton.Toolkit.KryptonLabel kryptonLabel6;
        private Krypton.Toolkit.KryptonPanel kryptonPanel4;
        private Krypton.Toolkit.KryptonLabel LblLowStock;
        private Krypton.Toolkit.KryptonLabel kryptonLabel4;
        private Krypton.Toolkit.KryptonPanel kryptonPanel3;
        private Krypton.Toolkit.KryptonLabel lblCategorie;
        private Krypton.Toolkit.KryptonLabel kryptonLabel2;
        private Krypton.Toolkit.KryptonPanel kryptonPanel1;
        private Krypton.Toolkit.KryptonLabel lblTotalCount;
        private Krypton.Toolkit.KryptonLabel lblTotalTitle;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnEdit;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnDelete;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnFirst;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnPrev;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnNext;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnLast;
        private Krypton.Toolkit.KryptonComboBox kryptonComboBox1;
        private Krypton.Toolkit.KryptonContextMenu kryptonContextMenu1;
        private Krypton.Toolkit.KryptonContextMenuItems kryptonContextMenuItems1;
        private Krypton.Toolkit.KryptonContextMenuItem kryptonContextMenuItem1;
        private Krypton.Toolkit.KryptonContextMenuItem kryptonContextMenuItem2;
        private Krypton.Toolkit.KryptonContextMenuItem kryptonContextMenuItem3;
        private Krypton.Toolkit.KryptonContextMenuItem kryptonContextMenuItem4;
        private Krypton.Toolkit.KryptonContextMenuSeparator kryptonContextMenuSeparator1;
        private Krypton.Toolkit.KryptonContextMenuItem kryptonContextMenuItem5;
        private Krypton.Toolkit.KryptonTextBox TXTSearch;
        private Krypton.Toolkit.ButtonSpecAny btnSearch;
        private Krypton.Toolkit.ButtonSpecAny btnClearSearch;
    }
}