﻿﻿using Dapper;
using IRepairIT.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IRepairIT.Data.Common
{
    public class DeviceCommands
    {
        private readonly DataAccess _db;

        public DeviceCommands()
        {
            _db = new DataAccess();
        }

        public async Task<int> GetCount(string searchTerm, string deviceType = null, string brand = null, int? customerId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            string sql = @"devices d
                JOIN customers c ON d.customer_id = c.id
                WHERE (d.type LIKE CONCAT('%', @searchTerm, '%')
                OR d.brand LIKE CONCAT('%', @searchTerm, '%')
                OR d.model LIKE CONCAT('%', @searchTerm, '%')
                OR c.name LIKE CONCAT('%', @searchTerm, '%'))";

            // Add advanced filters if provided
            if (!string.IsNullOrEmpty(deviceType))
            {
                sql += " AND d.type = @deviceType";
            }

            if (!string.IsNullOrEmpty(brand))
            {
                sql += " AND d.brand = @brand";
            }

            if (customerId.HasValue && customerId.Value > 0)
            {
                sql += " AND d.customer_id = @customerId";
            }

            if (startDate.HasValue)
            {
                sql += " AND d.receipt_date >= @startDate";
            }

            if (endDate.HasValue)
            {
                sql += " AND d.receipt_date <= @endDate";
            }

            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);

            // Add parameters for advanced filters
            if (!string.IsNullOrEmpty(deviceType))
            {
                parameters.Add("deviceType", deviceType);
            }

            if (!string.IsNullOrEmpty(brand))
            {
                parameters.Add("brand", brand);
            }

            if (customerId.HasValue && customerId.Value > 0)
            {
                parameters.Add("customerId", customerId.Value);
            }

            if (startDate.HasValue)
            {
                parameters.Add("startDate", startDate.Value.Date);
            }

            if (endDate.HasValue)
            {
                parameters.Add("endDate", endDate.Value.Date.AddDays(1).AddSeconds(-1));
            }

            return await _db.CountQuery(sql, parameters);
        }

        public async Task<IEnumerable<dynamic>> GetAll(string searchTerm, int offset, int pageSize, string deviceType = null, string brand = null, int? customerId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            string sql = @"
                SELECT
                    d.id,
                    c.id as customer_id,
                    c.name as client,
                    d.type,
                    d.brand,
                    d.model,
                    d.receipt_date as date_de_reception
                FROM devices d
                JOIN customers c ON d.customer_id = c.id
                WHERE (d.type LIKE CONCAT('%', @searchTerm, '%')
                    OR d.brand LIKE CONCAT('%', @searchTerm, '%')
                    OR d.model LIKE CONCAT('%', @searchTerm, '%')
                    OR c.name LIKE CONCAT('%', @searchTerm, '%'))";

            // Add advanced filters if provided
            if (!string.IsNullOrEmpty(deviceType))
            {
                sql += " AND d.type = @deviceType";
            }

            if (!string.IsNullOrEmpty(brand))
            {
                sql += " AND d.brand = @brand";
            }

            if (customerId.HasValue && customerId.Value > 0)
            {
                sql += " AND d.customer_id = @customerId";
            }

            if (startDate.HasValue)
            {
                sql += " AND d.receipt_date >= @startDate";
            }

            if (endDate.HasValue)
            {
                sql += " AND d.receipt_date <= @endDate";
            }

            // Add order by and pagination
            sql += @"
                ORDER BY d.receipt_date DESC
                LIMIT @pageSize OFFSET @offset";

            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            parameters.Add("offset", offset);
            parameters.Add("pageSize", pageSize);

            // Add parameters for advanced filters
            if (!string.IsNullOrEmpty(deviceType))
            {
                parameters.Add("deviceType", deviceType);
            }

            if (!string.IsNullOrEmpty(brand))
            {
                parameters.Add("brand", brand);
            }

            if (customerId.HasValue && customerId.Value > 0)
            {
                parameters.Add("customerId", customerId.Value);
            }

            if (startDate.HasValue)
            {
                parameters.Add("startDate", startDate.Value.Date);
            }

            if (endDate.HasValue)
            {
                parameters.Add("endDate", endDate.Value.Date.AddDays(1).AddSeconds(-1));
            }

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        public async Task<Device> GetByIdAsync(int id)
        {
            // MySQL doesn't like the reserved words 'condition' and 'password' as column aliases
            // So we'll use a simpler query and let Dapper handle the mapping
            const string sql = @"SELECT * FROM devices WHERE id = @Id";
            return await _db.QuerySingleOrDefaultQuery<Device>(sql, new { Id = id });
        }

        public async Task<IEnumerable<string>> GetDeviceTypesAsync()
        {
            const string sql = @"SELECT DISTINCT type FROM devices WHERE type IS NOT NULL AND type != '' ORDER BY type";
            return await _db.QueryQuery<string>(sql);
        }

        public async Task<IEnumerable<string>> GetDeviceBrandsAsync()
        {
            const string sql = @"SELECT DISTINCT brand FROM devices WHERE brand IS NOT NULL AND brand != '' ORDER BY brand";
            return await _db.QueryQuery<string>(sql);
        }

        public async Task<int> InsertAsync(Device device)
        {
            const string sql = @"
                INSERT INTO devices (
                    customer_id, type, brand, model, serial_number,
                    imei, problem, `condition`, `password`, accessories,
                    receipt_date, expected_delivery_date, created_at
                ) VALUES (
                    @CustomerId, @Type, @Brand, @Model, @SerialNumber,
                    @IMEI, @Problem, @Condition, @Password, @Accessories,
                    @ReceiptDate, @ExpectedDeliveryDate, @CreatedAt
                )";

            device.CreatedAt = DateTime.Now;
            device.ReceiptDate = DateTime.Now;

            return await _db.InsertAndGetIdAsync(sql, device);
        }

        public async Task<bool> UpdateAsync(Device device)
        {
            const string sql = @"
                UPDATE devices
                SET customer_id = @CustomerId,
                    type = @Type,
                    brand = @Brand,
                    model = @Model,
                    serial_number = @SerialNumber,
                    imei = @IMEI,
                    problem = @Problem,
                    `condition` = @Condition,
                    `password` = @Password,
                    accessories = @Accessories,
                    expected_delivery_date = @ExpectedDeliveryDate,
                    updated_at = @UpdatedAt
                WHERE id = @Id";

            device.UpdatedAt = DateTime.Now;

            int rowsAffected = await _db.ExecuteQuery(sql, device);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            const string sql = @"DELETE FROM devices WHERE id = @Id";
            int rowsAffected = await _db.ExecuteQuery(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<int> SaveDeviceImageAsync(DeviceImage image)
        {
            const string sql = @"
                INSERT INTO device_images (device_id, image_path, created_at)
                VALUES (@DeviceId, @ImagePath, @CreatedAt)";

            image.CreatedAt = DateTime.Now;

            return await _db.InsertAndGetIdAsync(sql, image);
        }

        public async Task<IEnumerable<DeviceImage>> GetDeviceImagesAsync(int deviceId)
        {
            try
            {
                Console.WriteLine($"Fetching images for device ID: {deviceId}");
                const string sql = @"SELECT id, device_id, image_path, created_at FROM device_images WHERE device_id = @DeviceId";
                var images = await _db.QueryQuery<DeviceImage>(sql, new { DeviceId = deviceId });

                // Log the results
                foreach (var img in images)
                {
                    Console.WriteLine($"Database image: ID={img.Id}, DeviceId={img.DeviceId}, Path={img.ImagePath}");
                }

                return images;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching device images: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> DeleteDeviceImageAsync(int imageId)
        {
            const string sql = @"DELETE FROM device_images WHERE id = @Id";
            int rowsAffected = await _db.ExecuteQuery(sql, new { Id = imageId });
            return rowsAffected > 0;
        }

        public async Task<bool> UpdateDeviceImagePathAsync(int imageId, string newPath)
        {
            try
            {
                Console.WriteLine($"Updating image path for ID {imageId} to: {newPath}");
                const string sql = @"UPDATE device_images SET image_path = @ImagePath WHERE id = @Id";
                int rowsAffected = await _db.ExecuteQuery(sql, new { Id = imageId, ImagePath = newPath });
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating image path: {ex.Message}");
                return false;
            }
        }
    }
}
