﻿using Dapper;
using IRepairIT.Enums;
using IRepairIT.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IRepairIT.Data.Common
{
    public class InventoryCommands
    {
        private readonly DataAccess _db;
        public InventoryCommands()
        {
            _db = new DataAccess();
        }

        public async Task<int> GetLowStock()
        {
            const string sql = @"parts WHERE quantity <= min_quantity";
            return await _db.CountQuery(sql);
        }
        public async Task<int> GetCategories()
        {
            // Use a direct query instead of CountQuery to get the count of distinct categories
            const string sql = @"SELECT COUNT(DISTINCT category) FROM parts WHERE category IS NOT NULL AND category != ''";
            using (var con = new MySql.Data.MySqlClient.MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
            {
                if (con.State != System.Data.ConnectionState.Open)
                    con.Open();

                return await con.ExecuteScalarAsync<int>(sql);
            }
        }
        public async Task<int> GetCount(string searchTerm)
        {
            const string sql = @"parts WHERE (name LIKE CONCAT('%', @searchTerm, '%') OR code LIKE CONCAT('%', @searchTerm, '%') OR category LIKE CONCAT('%', @searchTerm, '%'))";
            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            return await _db.CountQuery(sql, parameters);
        }

        public async Task<IEnumerable<Part>> GetALL(string searchTerm, int offset, int pageSize)
        {
            const string sql = @"SELECT * FROM parts WHERE (name LIKE CONCAT('%', @searchTerm, '%') OR code LIKE CONCAT('%', @searchTerm, '%') OR category LIKE CONCAT('%', @searchTerm, '%')) ORDER BY name ASC LIMIT @pageSize OFFSET @offsetVal";

            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            parameters.Add("offsetVal", offset);
            parameters.Add("pageSize", pageSize);
            var results = await _db.QueryQuery<Part>(sql, parameters);
            return results;
        }

        public async Task<Part> GetByIdAsync(int id)
        {
            const string sql = @"SELECT * FROM parts WHERE id = @Id";
            return await _db.QuerySingleOrDefaultQuery<Part>(sql, new { Id = id });
        }

        public async Task<int> InsertAsync(Part part)
        {
            const string sql = @"
                INSERT INTO parts (name, code, description, purchase_price, selling_price, quantity, min_quantity, category, supplier, created_at)
                VALUES (@Name, @Code, @Description, @Purchase_Price, @Selling_Price, @Quantity, @Min_Quantity, @Category, @Supplier, @CreatedAt)";

            part.CreatedAt = DateTime.Now;

            return await _db.InsertAndGetIdAsync(sql, part);
        }

        public async Task<bool> UpdateAsync(Part part)
        {
            const string sql = @"
                UPDATE parts
                SET name = @Name,
                    code = @Code,
                    description = @Description,
                    purchase_price = @Purchase_Price,
                    selling_price = @Selling_Price,
                    quantity = @Quantity,
                    min_quantity = @Min_Quantity,
                    category = @Category,
                    supplier = @Supplier,
                    updated_at = @UpdatedAt
                WHERE id = @Id";

            part.UpdatedAt = DateTime.Now;

            int rowsAffected = await _db.ExecuteQuery(sql, part);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            const string sql = @"DELETE FROM parts WHERE id = @Id";
            int rowsAffected = await _db.ExecuteQuery(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<bool> UpdateQuantityAsync(int partId, int quantity)
        {
            const string sql = @"
                UPDATE parts
                SET quantity = @Quantity,
                    updated_at = @UpdatedAt
                WHERE id = @Id";

            var parameters = new DynamicParameters();
            parameters.Add("Id", partId);
            parameters.Add("Quantity", quantity);
            parameters.Add("UpdatedAt", DateTime.Now);

            int rowsAffected = await _db.ExecuteQuery(sql, parameters);
            return rowsAffected > 0;
        }

        public async Task<bool> AddInventoryTransactionAsync(int partId, TransactionType transactionType, int quantity, string reference, string notes, int? userId)
        {
            const string sql = @"
                INSERT INTO inventory_transactions (part_id, transaction_type, quantity, reference, notes, user_id, created_at)
                VALUES (@PartId, @TransactionType, @Quantity, @Reference, @Notes, @UserId, @CreatedAt)";

            // Convert enum to database value
            string dbTransactionType;
            switch (transactionType)
            {
                case TransactionType.in_transaction:
                    dbTransactionType = "in";
                    break;
                case TransactionType.out_transaction:
                    dbTransactionType = "out";
                    break;
                case TransactionType.adjustment:
                    dbTransactionType = "adjustment";
                    break;
                default:
                    throw new ArgumentException($"Invalid transaction type: {transactionType}");
            }

            var parameters = new DynamicParameters();
            parameters.Add("PartId", partId);
            parameters.Add("TransactionType", dbTransactionType);
            parameters.Add("Quantity", quantity);
            parameters.Add("Reference", reference);
            parameters.Add("Notes", notes);
            parameters.Add("UserId", userId);
            parameters.Add("CreatedAt", DateTime.Now);

            int rowsAffected = await _db.ExecuteQuery(sql, parameters);
            return rowsAffected > 0;
        }

        /// <summary>
        /// Updates the quantity of a part and adds an inventory transaction record
        /// </summary>
        /// <param name="partId">The ID of the part</param>
        /// <param name="newQuantity">The new quantity value</param>
        /// <param name="reference">Reference for the transaction</param>
        /// <param name="notes">Notes for the transaction</param>
        /// <param name="userId">User ID who made the change (optional)</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> UpdateQuantityWithTransactionAsync(int partId, int newQuantity, string reference, string notes, int? userId)
        {
            try
            {
                // Get the current part to determine the original quantity
                var part = await GetByIdAsync(partId);
                if (part == null)
                    return false;

                int originalQuantity = part.Quantity;

                // If quantity hasn't changed, no need to do anything
                if (originalQuantity == newQuantity)
                    return true;

                // Update the quantity
                bool updateSuccess = await UpdateQuantityAsync(partId, newQuantity);
                if (!updateSuccess)
                    return false;

                // Add inventory transaction
                int quantityDifference = newQuantity - originalQuantity;
                TransactionType transactionType = quantityDifference > 0 ?
                    TransactionType.in_transaction : TransactionType.out_transaction;

                // If no reference provided, create a default one
                if (string.IsNullOrEmpty(reference))
                {
                    reference = quantityDifference > 0 ? "Ajout de stock" : "Retrait de stock";
                }

                // If no notes provided, create a default one
                if (string.IsNullOrEmpty(notes))
                {
                    notes = $"Modification de la quantité de {originalQuantity} à {newQuantity}";
                }

                bool transactionSuccess = await AddInventoryTransactionAsync(
                    partId,
                    transactionType,
                    Math.Abs(quantityDifference),
                    reference,
                    notes,
                    userId);

                return transactionSuccess;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> CodeExistsAsync(string code, int excludeId = 0)
        {
            if (string.IsNullOrWhiteSpace(code))
                return false;

            const string sql = @"
                SELECT COUNT(*) FROM parts
                WHERE code = @Code AND id != @ExcludeId";

            int count = await _db.ExecuteScalerQuery<int>(sql, new { Code = code, ExcludeId = excludeId });
            return count > 0;
        }

        public async Task<IEnumerable<string>> GetCategoriesListAsync()
        {
            const string sql = @"SELECT DISTINCT category FROM parts WHERE category IS NOT NULL AND category != '' ORDER BY category";
            return await _db.QueryQuery<string>(sql);
        }

        public async Task<IEnumerable<string>> GetSuppliersListAsync()
        {
            const string sql = @"SELECT DISTINCT supplier FROM parts WHERE supplier IS NOT NULL AND supplier != '' ORDER BY supplier";
            return await _db.QueryQuery<string>(sql);
        }

        public async Task<IEnumerable<Part>> GetAllForAutocomplete()
        {
            const string sql = @"SELECT Id, Name, Code FROM parts ORDER BY Name ASC";
            return await _db.QueryQuery<Part>(sql);
        }

        public async Task<IEnumerable<Part>> SearchByNameAsync(string searchTerm)
        {
            const string sql = @"SELECT * FROM parts WHERE Name LIKE @p_search OR Code LIKE @p_search ORDER BY Name ASC";
            var parameters = new { p_search = $"%{searchTerm}%" };
            return await _db.QueryQuery<Part>(sql, parameters);
        }

        public async Task<Part> GetByNameAsync(string name)
        {
            const string sql = @"SELECT * FROM parts WHERE Name = @p_name LIMIT 1";
            var parameters = new { p_name = name };
            return await _db.QuerySingleOrDefaultQuery<Part>(sql, parameters);
        }

        /// <summary>
        /// Gets inventory transactions with pagination
        /// </summary>
        /// <param name="partId">Optional part ID to filter transactions</param>
        /// <param name="offset">Pagination offset</param>
        /// <param name="pageSize">Pagination page size</param>
        /// <returns>List of inventory transactions</returns>
        public async Task<IEnumerable<dynamic>> GetInventoryTransactionsAsync(int? partId = null, int offset = 0, int pageSize = 50)
        {
            string whereClause = partId.HasValue ? "WHERE it.part_id = @PartId" : "";

            string sql = $@"
                SELECT it.*, p.name as part_name, p.code as part_code, u.full_name as user_name,
                CASE
                    WHEN it.transaction_type = 'in' THEN 'Ajout de stock'
                    WHEN it.transaction_type = 'out' THEN 'Retrait de stock'
                    WHEN it.transaction_type = 'adjustment' THEN 'Ajustement de stock'
                    ELSE it.transaction_type
                END as transaction_type_display
                FROM inventory_transactions it
                LEFT JOIN parts p ON it.part_id = p.id
                LEFT JOIN users u ON it.user_id = u.id
                {whereClause}
                ORDER BY it.created_at DESC
                LIMIT @PageSize OFFSET @Offset";

            var parameters = new DynamicParameters();
            if (partId.HasValue)
                parameters.Add("PartId", partId.Value);
            parameters.Add("Offset", offset);
            parameters.Add("PageSize", pageSize);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        /// <summary>
        /// Gets the total count of inventory transactions
        /// </summary>
        /// <param name="partId">Optional part ID to filter transactions</param>
        /// <returns>Total count of inventory transactions</returns>
        public async Task<int> GetInventoryTransactionsCountAsync(int? partId = null)
        {
            string whereClause = partId.HasValue ? "WHERE part_id = @PartId" : "";
            string sql = $"SELECT COUNT(*) FROM inventory_transactions {whereClause}";

            var parameters = new DynamicParameters();
            if (partId.HasValue)
                parameters.Add("PartId", partId.Value);

            return await _db.ExecuteScalerQuery<int>(sql, parameters);
        }
    }
}
