﻿using FastReport;
using IRepairIT.Data.Common;
using IRepairIT.Models;
using IRepairIT.Utilities;
using Krypton.Toolkit;
using System;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FUSERS
{
    public partial class FRM_USERS_LIST : KryptonForm
    {
        private readonly UserCommands _cmd;
        private int _pageSize;
        private int _currentPage = 1;
        private int _totalRows = 0;
        private string _searchTerm = "";

        public FRM_USERS_LIST()
        {
            InitializeComponent();
            _cmd = new UserCommands();

            CmbPageView.SelectedIndex = 2;
            _pageSize = 100;

            CmbPageView.SelectedIndexChanged += CmbPageView_SelectedIndexChanged;

            btnFirst.Text = "Premier";
            btnPrev.Text = "Précédent";
            btnNext.Text = "Suivant";
            btnLast.Text = "Dernier";
        }

        public async Task LoadUsers()
        {
            try
            {
                if (CmbPageView.SelectedItem != null)
                {
                    _pageSize = Convert.ToInt32(CmbPageView.Text);
                }

                int offset = (_currentPage - 1) * _pageSize;
                _searchTerm = TXTSearch?.Text ?? "";

                var users = await _cmd.SearchAsync(_searchTerm, offset, _pageSize);
                var totalCount = await _cmd.GetCountAsync(_searchTerm);
                _totalRows = totalCount;

                if (users != null)
                {
                    kryptonDataGridView1.DataSource = users;
                    kryptonDataGridView1.Columns[nameof(User.Password)].Visible = false;
                    kryptonDataGridView1.Columns[nameof(User.CreatedAt)].Visible = false;
                    kryptonDataGridView1.Columns[nameof(User.UpdatedAt)].Visible = false;

                    lblTotalCount.Text = totalCount.ToString();
                    lblTotalAdmin.Text = users.Where(x => x.Role == Enums.UserRoles.admin).Count().ToString();
                    LblTotalTechnician.Text = users.Where(x => x.Role == Enums.UserRoles.technician).Count().ToString();
                    LblTotalActif.Text = users.Where(x => x.Status == Enums.UserStatus.active).Count().ToString();

                    kryptonHeaderGroup1.ValuesSecondary.Heading = $"Total: {_totalRows} - Page: {_currentPage}/{Math.Ceiling((double)_totalRows / _pageSize)}";

                    UpdatePaginationButtons();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des utilisateurs: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void UpdatePaginationButtons()
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);

            kryptonHeaderGroup1.ValuesSecondary.Heading = $"Total: {_totalRows} - Page: {_currentPage}/{Math.Max(1, totalPages)}";

            btnFirst.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnPrev.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnNext.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
            btnLast.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
        }

        private async void FRM_USERS_LIST_Load(object sender, EventArgs e)
        {
            try
            {
                // Set row height
                kryptonDataGridView1.RowTemplate.Height = 32;

                // Load users data
                await LoadUsers();

                // Apply enhanced styling
                DGV_STYLE.DesignDGV(kryptonDataGridView1);

                // Set up context menu
                SetupContextMenu();

                // Set panel colors
                kryptonPanel1.StateNormal.Color1 = Color.FromArgb(52, 152, 219); // Blue
                kryptonPanel1.StateNormal.Color2 = Color.FromArgb(41, 128, 185);

                kryptonPanel3.StateNormal.Color1 = Color.FromArgb(231, 76, 60); // Red
                kryptonPanel3.StateNormal.Color2 = Color.FromArgb(192, 57, 43);

                kryptonPanel4.StateNormal.Color1 = Color.FromArgb(46, 204, 113); // Green
                kryptonPanel4.StateNormal.Color2 = Color.FromArgb(39, 174, 96);

                kryptonPanel5.StateNormal.Color1 = Color.FromArgb(155, 89, 182); // Purple
                kryptonPanel5.StateNormal.Color2 = Color.FromArgb(142, 68, 173);

                // Set label colors
                lblTotalTitle.StateCommon.ShortText.Color1 = Color.White;
                lblTotalCount.StateCommon.ShortText.Color1 = Color.White;

                kryptonLabel2.StateCommon.ShortText.Color1 = Color.White;
                lblTotalAdmin.StateCommon.ShortText.Color1 = Color.White;

                kryptonLabel4.StateCommon.ShortText.Color1 = Color.White;
                LblTotalTechnician.StateCommon.ShortText.Color1 = Color.White;

                kryptonLabel6.StateCommon.ShortText.Color1 = Color.White;
                LblTotalActif.StateCommon.ShortText.Color1 = Color.White;

                // Focus on search box
                TXTSearch.Focus();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du formulaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void SetupContextMenu()
        {
            try
            {
                kryptonContextMenuItem1.Text = "Rafraîchir";
                kryptonContextMenuItem2.Text = "Ajouter";
                kryptonContextMenuItem3.Text = "Modifier";
                kryptonContextMenuItem4.Text = "Supprimer";
                kryptonContextMenuItem5.Text = "Imprimer";

                kryptonContextMenuItem1.Click -= async (s, e) => await LoadUsers();
                kryptonContextMenuItem2.Click -= btnAdd_Click;
                kryptonContextMenuItem3.Click -= btnEdit_Click;
                kryptonContextMenuItem4.Click -= btnDelete_Click;
                kryptonContextMenuItem5.Click -= PrintDataGridView;

                kryptonContextMenuItem1.Click += async (s, e) => await LoadUsers();
                kryptonContextMenuItem2.Click += btnAdd_Click;
                kryptonContextMenuItem3.Click += btnEdit_Click;
                kryptonContextMenuItem4.Click += btnDelete_Click;
                kryptonContextMenuItem5.Click += PrintDataGridView;

                ContextMenuStrip contextMenuStrip = new ContextMenuStrip();

                ToolStripMenuItem refreshItem = new ToolStripMenuItem("Rafraîchir", null, async (s, e) => await LoadUsers());
                ToolStripMenuItem addItem = new ToolStripMenuItem("Ajouter", null, btnAdd_Click);
                ToolStripMenuItem editItem = new ToolStripMenuItem("Modifier", null, btnEdit_Click);
                ToolStripMenuItem deleteItem = new ToolStripMenuItem("Supprimer", null, btnDelete_Click);
                ToolStripSeparator separator = new ToolStripSeparator();
                ToolStripMenuItem printItem = new ToolStripMenuItem("Imprimer", null, PrintDataGridView);

                contextMenuStrip.Items.Add(refreshItem);
                contextMenuStrip.Items.Add(addItem);
                contextMenuStrip.Items.Add(editItem);
                contextMenuStrip.Items.Add(deleteItem);
                contextMenuStrip.Items.Add(separator);
                contextMenuStrip.Items.Add(printItem);

                kryptonDataGridView1.ContextMenuStrip = contextMenuStrip;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la configuration du menu contextuel: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            var frm = new FRM_USERS();
            frm.ShowDialog();

            if (frm.HasChanges)
            {
                await LoadUsers();
            }
        }

        private async void TXTSearch_TextChanged(object sender, EventArgs e)
        {
            _currentPage = 1;
            await LoadUsers();
        }

        private async void btnEdit_Click(object sender, EventArgs e)
        {
            if (kryptonDataGridView1.CurrentRow == null || kryptonDataGridView1.CurrentRow.DataBoundItem == null)
            {
                KryptonMessageBox.Show("Veuillez sélectionner un utilisateur à modifier", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                return;
            }

            var selectedUser = (User)kryptonDataGridView1.CurrentRow.DataBoundItem;
            var frm = new FRM_USERS(selectedUser);
            frm.ShowDialog();

            if (frm.HasChanges)
            {
                await LoadUsers();
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (kryptonDataGridView1.CurrentRow == null || kryptonDataGridView1.CurrentRow.DataBoundItem == null)
            {
                KryptonMessageBox.Show("Veuillez sélectionner un utilisateur à supprimer", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                return;
            }

            var selectedUser = (User)kryptonDataGridView1.CurrentRow.DataBoundItem;

            var result = KryptonMessageBox.Show(
                $"Êtes-vous sûr de vouloir supprimer l'utilisateur '{selectedUser.Username}'?",
                "Confirmation",
                KryptonMessageBoxButtons.YesNo,
                KryptonMessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    await _cmd.DeleteAsync(selectedUser.Id);
                    KryptonMessageBox.Show("L'utilisateur a été supprimé avec succès", "Succès",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    await LoadUsers();
                }
                catch (Exception ex)
                {
                    KryptonMessageBox.Show($"Erreur lors de la suppression: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
            }
        }

        private void PrintDataGridView(object sender, EventArgs e)
        {
            try
            {
                if (kryptonDataGridView1.Rows.Count == 0)
                {
                    KryptonMessageBox.Show("Aucune donnée à imprimer", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                string reportFile = Path.Combine(Application.StartupPath, "Reports", "UsersList.frx");

                if (!File.Exists(reportFile))
                {
                    KryptonMessageBox.Show("Le fichier de rapport n'existe pas: " + reportFile, "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    return;
                }

                Report report = new Report();

                report.Load(reportFile);

                DataTable dt = new DataTable("Users");

                dt.Columns.Add("Username", typeof(string));
                dt.Columns.Add("FullName", typeof(string));
                dt.Columns.Add("Email", typeof(string));
                dt.Columns.Add("Role", typeof(string));
                dt.Columns.Add("Status", typeof(string));

                foreach (DataGridViewRow row in kryptonDataGridView1.Rows)
                {
                    if (!row.IsNewRow)
                    {
                        DataRow dataRow = dt.NewRow();

                        for (int i = 0; i < kryptonDataGridView1.Columns.Count; i++)
                        {
                            if (kryptonDataGridView1.Columns[i].Visible)
                            {
                                string columnName = kryptonDataGridView1.Columns[i].Name;
                                object cellValue = row.Cells[i].Value;

                                if (columnName == "Username" || columnName == "username")
                                    dataRow["Username"] = cellValue ?? DBNull.Value;
                                else if (columnName == "FullName" || columnName == "full_name")
                                    dataRow["FullName"] = cellValue ?? DBNull.Value;
                                else if (columnName == "Email" || columnName == "email")
                                    dataRow["Email"] = cellValue ?? DBNull.Value;
                                else if (columnName == "Role" || columnName == "role")
                                    dataRow["Role"] = cellValue ?? DBNull.Value;
                                else if (columnName == "Status" || columnName == "status")
                                    dataRow["Status"] = cellValue ?? DBNull.Value;
                            }
                        }

                        dt.Rows.Add(dataRow);
                    }
                }

                report.RegisterData(dt, "Users");

                report.SetParameterValue("TotalUsers", dt.Rows.Count.ToString());
                report.SetParameterValue("Date", DateTime.Now.ToString("dd/MM/yyyy"));

                report.Prepare();
                report.Show();

            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'impression: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void CmbPageView_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (CmbPageView.SelectedItem != null)
                {
                    _pageSize = Convert.ToInt32(CmbPageView.Text);
                }

                _currentPage = 1;

                await LoadUsers();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du changement de taille de page: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void btnFirst_Click(object sender, EventArgs e)
        {
            try
            {
                _currentPage = 1;
                await LoadUsers();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la navigation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void btnPrev_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentPage > 1)
                {
                    _currentPage--;
                    await LoadUsers();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la navigation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void btnNext_Click(object sender, EventArgs e)
        {
            try
            {
                int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);

                if (_currentPage < totalPages)
                {
                    _currentPage++;
                    await LoadUsers();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la navigation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void btnLast_Click(object sender, EventArgs e)
        {
            try
            {
                int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
                _currentPage = totalPages;
                await LoadUsers();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la navigation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
    }
}
