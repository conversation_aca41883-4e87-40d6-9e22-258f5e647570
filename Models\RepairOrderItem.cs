﻿using IRepairIT.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class RepairOrderItem
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Identifiant de la commande de réparation")]
        public int RepairOrderId { get; set; }

        [Display(Name = "Type d'élément")]
        public ItemType ItemType { get; set; }

        [Display(Name = "Identifiant de l'élément")]
        public int ItemId { get; set; }

        [Display(Name = "Quantité")]
        public int Quantity { get; set; }

        [Display(Name = "Prix")]
        public decimal Price { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Commande de réparation")]
        public RepairOrder RepairOrder { get; set; }
    }
}
