﻿using Krypton.Toolkit;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            KryptonDataGridViewButtonCell cell = new KryptonDataGridViewButtonCell();
            cell.ButtonStyle = ButtonStyle.Standalone;
            
            //kryptonDataGridView1
        }
    }
}
