﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="IRepairIT.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2" />
    </startup>
    <userSettings>
        <IRepairIT.Properties.Settings>
            <setting name="CurrentConnectionString" serializeAs="String">
                <value />
            </setting>
            <setting name="CurrentDatabase" serializeAs="String">
                <value />
            </setting>
            <setting name="ShowDatabaseSelector" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="DefaultDatabase" serializeAs="String">
                <value />
            </setting>
            <setting name="StartingWindow" serializeAs="String">
                <value />
            </setting>
            <setting name="CurrentUserId" serializeAs="String">
                <value>0</value>
            </setting>
        </IRepairIT.Properties.Settings>
    </userSettings>
</configuration>