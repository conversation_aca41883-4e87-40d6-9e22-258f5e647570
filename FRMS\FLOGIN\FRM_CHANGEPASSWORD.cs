using IRepairIT.Data.Common;
using Krypton.Toolkit;
using System;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FLOGIN
{
    public partial class FRM_CHANGEPASSWORD : KryptonForm
    {
        private readonly UserCommands _userCommands;
        private readonly int _userId;
        private readonly string _username;

        public FRM_CHANGEPASSWORD(int userId, string username)
        {
            InitializeComponent();
            _userCommands = new UserCommands();
            _userId = userId;
            _username = username;
            lblUsername.Values.Text = username;
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            // Vérification des entrées
            errorProvider1.Clear();

            if (string.IsNullOrWhiteSpace(txtNewPassword.Text))
            {
                errorProvider1.SetError(txtNewPassword, "Veuillez entrer le nouveau mot de passe");
                txtNewPassword.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtConfirmPassword.Text))
            {
                errorProvider1.SetError(txtConfirmPassword, "Veuillez confirmer le nouveau mot de passe");
                txtConfirmPassword.Focus();
                return;
            }

            if (txtNewPassword.Text != txtConfirmPassword.Text)
            {
                errorProvider1.SetError(txtConfirmPassword, "Les mots de passe ne correspondent pas");
                txtConfirmPassword.Focus();
                txtConfirmPassword.SelectAll();
                return;
            }

            try
            {
                int rowsAffected = await _userCommands.ChangePasswordAsync(_userId, txtNewPassword.Text);

                if (rowsAffected > 0)
                {
                    KryptonMessageBox.Show("Mot de passe modifié avec succès", "Succès",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    DialogResult = DialogResult.OK;
                    Close();
                }
                else
                {
                    KryptonMessageBox.Show("Échec de la modification du mot de passe", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Une erreur s'est produite: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void btnShowNew_Click(object sender, EventArgs e)
        {
            txtNewPassword.PasswordChar = (txtNewPassword.Tag as string == "0") ? '\0' : '#';
            txtNewPassword.ButtonSpecs[0].Image = (txtNewPassword.Tag as string == "0") ?
                Properties.Resources.eye_no : Properties.Resources.eye;
            txtNewPassword.Tag = (txtNewPassword.Tag as string == "0") ? "1" : "0";
        }

        private void btnShowConfirm_Click(object sender, EventArgs e)
        {
            txtConfirmPassword.PasswordChar = (txtConfirmPassword.Tag as string == "0") ? '\0' : '#';
            txtConfirmPassword.ButtonSpecs[0].Image = (txtConfirmPassword.Tag as string == "0") ?
                Properties.Resources.eye_no : Properties.Resources.eye;
            txtConfirmPassword.Tag = (txtConfirmPassword.Tag as string == "0") ? "1" : "0";
        }

        private void txtNewPassword_Enter(object sender, EventArgs e)
        {
            // Sélectionner tout le texte quand le champ reçoit le focus
            txtNewPassword.SelectAll();
        }

        private void txtConfirmPassword_Enter(object sender, EventArgs e)
        {
            // Sélectionner tout le texte quand le champ reçoit le focus
            txtConfirmPassword.SelectAll();
        }
    }
}
