﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class Repair
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Identifiant du client")]
        public int CustomerId { get; set; }

        [Display(Name = "Numéro de référence")]
        public string ReferenceNumber { get; set; }

        [Display(Name = "Type d'appareil")]
        public string DeviceType { get; set; }

        [Display(Name = "Marque")]
        public string Brand { get; set; }

        [Display(Name = "Modèle")]
        public string Model { get; set; }

        [Display(Name = "Numéro de série")]
        public string SerialNumber { get; set; }

        [Display(Name = "Description du problème")]
        public string ProblemDescription { get; set; }

        [Display(Name = "Diagnostic")]
        public string Diagnosis { get; set; }

        [Display(Name = "Solution")]
        public string Solution { get; set; }

        [Display(Name = "Statut")]
        public int Status { get; set; }

        [Display(Name = "Coût estimé")]
        public decimal? EstimatedCost { get; set; }

        [Display(Name = "Coût final")]
        public decimal? FinalCost { get; set; }

        [Display(Name = "Date de réception")]
        public DateTime ReceivedDate { get; set; }

        [Display(Name = "Date d'achèvement estimée")]
        public DateTime? EstimatedCompletionDate { get; set; }

        [Display(Name = "Date d'achèvement")]
        public DateTime? CompletionDate { get; set; }

        [Display(Name = "Date de retour")]
        public DateTime? ReturnedDate { get; set; }

        [Display(Name = "Notes")]
        public string Notes { get; set; }

        [Display(Name = "Technicien assigné")]
        public string AssignedTechnician { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Date de mise à jour")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation property
        [Display(Name = "Client")]
        public Customer Customer { get; set; }
    }
}
