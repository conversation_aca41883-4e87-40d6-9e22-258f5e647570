﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class ShopInfo
    {
        [Display(Name = "Identifiant")]
        public int id { get; set; }

        [Display(Name = "Nom"), Required(ErrorMessage = "Le champ [ Nom ] doit être renseigné")]
        public string name { get; set; }

        [Display(Name = "Activité"), Required(ErrorMessage = "Le champ [ Activité ] doit être renseigné")]
        public string activity { get; set; }

        [Display(Name = "Adresse")]
        public string address { get; set; }

        [Display(Name = "Téléphone")]
        public string phone { get; set; }

        [Display(Name = "Email")]
        public string email { get; set; }

        [Display(Name = "Site web")]
        public string website { get; set; }

        [Display(Name = "Chemin du logo")]
        public string logo_path { get; set; }

        [Display(Name = "Date de création")]
        public DateTime created_at { get; set; }

        [Display(Name = "Date de mise à jour")]
        public DateTime? updated_at { get; set; }
    }
}
