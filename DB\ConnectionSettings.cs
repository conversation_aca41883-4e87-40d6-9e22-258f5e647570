﻿using Newtonsoft.Json;
using System;

namespace IRepairIT.DB
{
    public class ConnectionSettings
    {
        public string Server { get; set; }
        public int Port { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }

        public ConnectionSettings()
        {
            Server = "localhost";
            Port = 3306;
            Username = "root";
            Password = "";
        }

        public string GetMasterConnectionString()
        {
            return $"Server={Server};Port={Port};User ID={Username};Password={Password};";
        }

        public string GetConnectionString(string database)
        {
            return $"Server={Server};Port={Port};Database={database};User ID={Username};Password={Password};";
        }

        public static ConnectionSettings LoadFromJson(string filePath)
        {
            try
            {
                if (System.IO.File.Exists(filePath))
                {
                    string json = System.IO.File.ReadAllText(filePath);
                    return JsonConvert.DeserializeObject<ConnectionSettings>(json);
                }
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"Error loading connection settings: {ex.Message}", "Error", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }

            return new ConnectionSettings();
        }

        public void SaveToJson(string filePath)
        {
            try
            {
                string json = JsonConvert.SerializeObject(this, Formatting.Indented);
                System.IO.File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"Error saving connection settings: {ex.Message}", "Error", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }
    }
}
