using Krypton.Toolkit;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FCOMMON
{
    public partial class FRM_SELECT_ITEM : KryptonForm
    {
        public object SelectedItem { get; private set; }
        
        public FRM_SELECT_ITEM()
        {
            InitializeComponent();
            SelectedItem = null;
        }
        
        public void SetItems<T>(IEnumerable<T> items, string title, string displayMember, string valueMember)
        {
            this.Text = title;
            lblTitle.Text = title;
            
            // Configure the list
            lstItems.DisplayMember = displayMember;
            lstItems.ValueMember = valueMember;
            lstItems.DataSource = items.ToList();
            
            // Select the first item by default
            if (lstItems.Items.Count > 0)
            {
                lstItems.SelectedIndex = 0;
            }
        }
        
        private void btnSelect_Click(object sender, EventArgs e)
        {
            if (lstItems.SelectedItem != null)
            {
                SelectedItem = lstItems.SelectedItem;
                DialogResult = DialogResult.OK;
                Close();
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un élément", "Sélection requise",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
            }
        }
        
        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
        
        private void lstItems_DoubleClick(object sender, EventArgs e)
        {
            if (lstItems.SelectedItem != null)
            {
                SelectedItem = lstItems.SelectedItem;
                DialogResult = DialogResult.OK;
                Close();
            }
        }
    }
}
