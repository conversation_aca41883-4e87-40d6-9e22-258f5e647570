﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class Setting
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Clé de paramètre")]
        public string SettingKey { get; set; }

        [Display(Name = "Valeur de paramètre")]
        public string SettingValue { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Date de mise à jour")]
        public DateTime? UpdatedAt { get; set; }
    }
}
