namespace IRepairIT.FRMS.FMAIN
{
    partial class FRM_SESSION_INFO
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.kryptonPanel1 = new Krypton.Toolkit.KryptonPanel();
            this.kryptonGroupBox2 = new Krypton.Toolkit.KryptonGroupBox();
            this.lblHostname = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel12 = new Krypton.Toolkit.KryptonLabel();
            this.lblIpAddress = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel10 = new Krypton.Toolkit.KryptonLabel();
            this.lblLastActivity = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel8 = new Krypton.Toolkit.KryptonLabel();
            this.lblLoginTime = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel6 = new Krypton.Toolkit.KryptonLabel();
            this.lblSessionId = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel4 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonGroupBox1 = new Krypton.Toolkit.KryptonGroupBox();
            this.lblStatus = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel14 = new Krypton.Toolkit.KryptonLabel();
            this.lblRole = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel16 = new Krypton.Toolkit.KryptonLabel();
            this.lblPhone = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel18 = new Krypton.Toolkit.KryptonLabel();
            this.lblEmail = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel20 = new Krypton.Toolkit.KryptonLabel();
            this.lblFullName = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel22 = new Krypton.Toolkit.KryptonLabel();
            this.lblUsername = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel24 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel1 = new Krypton.Toolkit.KryptonLabel();
            this.btnEndSession = new Krypton.Toolkit.KryptonButton();
            this.btnClose = new Krypton.Toolkit.KryptonButton();
            this.btnRefresh = new Krypton.Toolkit.KryptonButton();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).BeginInit();
            this.kryptonPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox2.Panel)).BeginInit();
            this.kryptonGroupBox2.Panel.SuspendLayout();
            this.kryptonGroupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1.Panel)).BeginInit();
            this.kryptonGroupBox1.Panel.SuspendLayout();
            this.kryptonGroupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // kryptonPanel1
            // 
            this.kryptonPanel1.Controls.Add(this.btnRefresh);
            this.kryptonPanel1.Controls.Add(this.btnClose);
            this.kryptonPanel1.Controls.Add(this.btnEndSession);
            this.kryptonPanel1.Controls.Add(this.kryptonGroupBox2);
            this.kryptonPanel1.Controls.Add(this.kryptonGroupBox1);
            this.kryptonPanel1.Controls.Add(this.kryptonLabel1);
            this.kryptonPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel1.Location = new System.Drawing.Point(0, 0);
            this.kryptonPanel1.Name = "kryptonPanel1";
            this.kryptonPanel1.Size = new System.Drawing.Size(584, 561);
            this.kryptonPanel1.TabIndex = 0;
            // 
            // kryptonGroupBox2
            // 
            this.kryptonGroupBox2.Location = new System.Drawing.Point(12, 307);
            this.kryptonGroupBox2.Name = "kryptonGroupBox2";
            // 
            // kryptonGroupBox2.Panel
            // 
            this.kryptonGroupBox2.Panel.Controls.Add(this.lblHostname);
            this.kryptonGroupBox2.Panel.Controls.Add(this.kryptonLabel12);
            this.kryptonGroupBox2.Panel.Controls.Add(this.lblIpAddress);
            this.kryptonGroupBox2.Panel.Controls.Add(this.kryptonLabel10);
            this.kryptonGroupBox2.Panel.Controls.Add(this.lblLastActivity);
            this.kryptonGroupBox2.Panel.Controls.Add(this.kryptonLabel8);
            this.kryptonGroupBox2.Panel.Controls.Add(this.lblLoginTime);
            this.kryptonGroupBox2.Panel.Controls.Add(this.kryptonLabel6);
            this.kryptonGroupBox2.Panel.Controls.Add(this.lblSessionId);
            this.kryptonGroupBox2.Panel.Controls.Add(this.kryptonLabel4);
            this.kryptonGroupBox2.Size = new System.Drawing.Size(560, 180);
            this.kryptonGroupBox2.TabIndex = 1;
            this.kryptonGroupBox2.Values.Heading = "Informations de session";
            // 
            // lblHostname
            // 
            this.lblHostname.Location = new System.Drawing.Point(150, 140);
            this.lblHostname.Name = "lblHostname";
            this.lblHostname.Size = new System.Drawing.Size(16, 20);
            this.lblHostname.TabIndex = 9;
            this.lblHostname.Values.Text = "-";
            // 
            // kryptonLabel12
            // 
            this.kryptonLabel12.Location = new System.Drawing.Point(20, 140);
            this.kryptonLabel12.Name = "kryptonLabel12";
            this.kryptonLabel12.Size = new System.Drawing.Size(92, 20);
            this.kryptonLabel12.TabIndex = 8;
            this.kryptonLabel12.Values.Text = "Nom d\'hôte:";
            // 
            // lblIpAddress
            // 
            this.lblIpAddress.Location = new System.Drawing.Point(150, 110);
            this.lblIpAddress.Name = "lblIpAddress";
            this.lblIpAddress.Size = new System.Drawing.Size(16, 20);
            this.lblIpAddress.TabIndex = 7;
            this.lblIpAddress.Values.Text = "-";
            // 
            // kryptonLabel10
            // 
            this.kryptonLabel10.Location = new System.Drawing.Point(20, 110);
            this.kryptonLabel10.Name = "kryptonLabel10";
            this.kryptonLabel10.Size = new System.Drawing.Size(83, 20);
            this.kryptonLabel10.TabIndex = 6;
            this.kryptonLabel10.Values.Text = "Adresse IP:";
            // 
            // lblLastActivity
            // 
            this.lblLastActivity.Location = new System.Drawing.Point(150, 80);
            this.lblLastActivity.Name = "lblLastActivity";
            this.lblLastActivity.Size = new System.Drawing.Size(16, 20);
            this.lblLastActivity.TabIndex = 5;
            this.lblLastActivity.Values.Text = "-";
            // 
            // kryptonLabel8
            // 
            this.kryptonLabel8.Location = new System.Drawing.Point(20, 80);
            this.kryptonLabel8.Name = "kryptonLabel8";
            this.kryptonLabel8.Size = new System.Drawing.Size(124, 20);
            this.kryptonLabel8.TabIndex = 4;
            this.kryptonLabel8.Values.Text = "Dernière activité:";
            // 
            // lblLoginTime
            // 
            this.lblLoginTime.Location = new System.Drawing.Point(150, 50);
            this.lblLoginTime.Name = "lblLoginTime";
            this.lblLoginTime.Size = new System.Drawing.Size(16, 20);
            this.lblLoginTime.TabIndex = 3;
            this.lblLoginTime.Values.Text = "-";
            // 
            // kryptonLabel6
            // 
            this.kryptonLabel6.Location = new System.Drawing.Point(20, 50);
            this.kryptonLabel6.Name = "kryptonLabel6";
            this.kryptonLabel6.Size = new System.Drawing.Size(124, 20);
            this.kryptonLabel6.TabIndex = 2;
            this.kryptonLabel6.Values.Text = "Heure de connexion:";
            // 
            // lblSessionId
            // 
            this.lblSessionId.Location = new System.Drawing.Point(150, 20);
            this.lblSessionId.Name = "lblSessionId";
            this.lblSessionId.Size = new System.Drawing.Size(16, 20);
            this.lblSessionId.TabIndex = 1;
            this.lblSessionId.Values.Text = "-";
            // 
            // kryptonLabel4
            // 
            this.kryptonLabel4.Location = new System.Drawing.Point(20, 20);
            this.kryptonLabel4.Name = "kryptonLabel4";
            this.kryptonLabel4.Size = new System.Drawing.Size(124, 20);
            this.kryptonLabel4.TabIndex = 0;
            this.kryptonLabel4.Values.Text = "Identifiant de session:";
            // 
            // kryptonGroupBox1
            // 
            this.kryptonGroupBox1.Location = new System.Drawing.Point(12, 61);
            this.kryptonGroupBox1.Name = "kryptonGroupBox1";
            // 
            // kryptonGroupBox1.Panel
            // 
            this.kryptonGroupBox1.Panel.Controls.Add(this.lblStatus);
            this.kryptonGroupBox1.Panel.Controls.Add(this.kryptonLabel14);
            this.kryptonGroupBox1.Panel.Controls.Add(this.lblRole);
            this.kryptonGroupBox1.Panel.Controls.Add(this.kryptonLabel16);
            this.kryptonGroupBox1.Panel.Controls.Add(this.lblPhone);
            this.kryptonGroupBox1.Panel.Controls.Add(this.kryptonLabel18);
            this.kryptonGroupBox1.Panel.Controls.Add(this.lblEmail);
            this.kryptonGroupBox1.Panel.Controls.Add(this.kryptonLabel20);
            this.kryptonGroupBox1.Panel.Controls.Add(this.lblFullName);
            this.kryptonGroupBox1.Panel.Controls.Add(this.kryptonLabel22);
            this.kryptonGroupBox1.Panel.Controls.Add(this.lblUsername);
            this.kryptonGroupBox1.Panel.Controls.Add(this.kryptonLabel24);
            this.kryptonGroupBox1.Size = new System.Drawing.Size(560, 230);
            this.kryptonGroupBox1.TabIndex = 0;
            this.kryptonGroupBox1.Values.Heading = "Informations utilisateur";
            // 
            // lblStatus
            // 
            this.lblStatus.Location = new System.Drawing.Point(150, 170);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(16, 20);
            this.lblStatus.TabIndex = 11;
            this.lblStatus.Values.Text = "-";
            // 
            // kryptonLabel14
            // 
            this.kryptonLabel14.Location = new System.Drawing.Point(20, 170);
            this.kryptonLabel14.Name = "kryptonLabel14";
            this.kryptonLabel14.Size = new System.Drawing.Size(50, 20);
            this.kryptonLabel14.TabIndex = 10;
            this.kryptonLabel14.Values.Text = "Statut:";
            // 
            // lblRole
            // 
            this.lblRole.Location = new System.Drawing.Point(150, 140);
            this.lblRole.Name = "lblRole";
            this.lblRole.Size = new System.Drawing.Size(16, 20);
            this.lblRole.TabIndex = 9;
            this.lblRole.Values.Text = "-";
            // 
            // kryptonLabel16
            // 
            this.kryptonLabel16.Location = new System.Drawing.Point(20, 140);
            this.kryptonLabel16.Name = "kryptonLabel16";
            this.kryptonLabel16.Size = new System.Drawing.Size(42, 20);
            this.kryptonLabel16.TabIndex = 8;
            this.kryptonLabel16.Values.Text = "Rôle:";
            // 
            // lblPhone
            // 
            this.lblPhone.Location = new System.Drawing.Point(150, 110);
            this.lblPhone.Name = "lblPhone";
            this.lblPhone.Size = new System.Drawing.Size(16, 20);
            this.lblPhone.TabIndex = 7;
            this.lblPhone.Values.Text = "-";
            // 
            // kryptonLabel18
            // 
            this.kryptonLabel18.Location = new System.Drawing.Point(20, 110);
            this.kryptonLabel18.Name = "kryptonLabel18";
            this.kryptonLabel18.Size = new System.Drawing.Size(77, 20);
            this.kryptonLabel18.TabIndex = 6;
            this.kryptonLabel18.Values.Text = "Téléphone:";
            // 
            // lblEmail
            // 
            this.lblEmail.Location = new System.Drawing.Point(150, 80);
            this.lblEmail.Name = "lblEmail";
            this.lblEmail.Size = new System.Drawing.Size(16, 20);
            this.lblEmail.TabIndex = 5;
            this.lblEmail.Values.Text = "-";
            // 
            // kryptonLabel20
            // 
            this.kryptonLabel20.Location = new System.Drawing.Point(20, 80);
            this.kryptonLabel20.Name = "kryptonLabel20";
            this.kryptonLabel20.Size = new System.Drawing.Size(47, 20);
            this.kryptonLabel20.TabIndex = 4;
            this.kryptonLabel20.Values.Text = "Email:";
            // 
            // lblFullName
            // 
            this.lblFullName.Location = new System.Drawing.Point(150, 50);
            this.lblFullName.Name = "lblFullName";
            this.lblFullName.Size = new System.Drawing.Size(16, 20);
            this.lblFullName.TabIndex = 3;
            this.lblFullName.Values.Text = "-";
            // 
            // kryptonLabel22
            // 
            this.kryptonLabel22.Location = new System.Drawing.Point(20, 50);
            this.kryptonLabel22.Name = "kryptonLabel22";
            this.kryptonLabel22.Size = new System.Drawing.Size(97, 20);
            this.kryptonLabel22.TabIndex = 2;
            this.kryptonLabel22.Values.Text = "Nom complet:";
            // 
            // lblUsername
            // 
            this.lblUsername.Location = new System.Drawing.Point(150, 20);
            this.lblUsername.Name = "lblUsername";
            this.lblUsername.Size = new System.Drawing.Size(16, 20);
            this.lblUsername.TabIndex = 1;
            this.lblUsername.Values.Text = "-";
            // 
            // kryptonLabel24
            // 
            this.kryptonLabel24.Location = new System.Drawing.Point(20, 20);
            this.kryptonLabel24.Name = "kryptonLabel24";
            this.kryptonLabel24.Size = new System.Drawing.Size(124, 20);
            this.kryptonLabel24.TabIndex = 0;
            this.kryptonLabel24.Values.Text = "Nom d\'utilisateur:";
            // 
            // kryptonLabel1
            // 
            this.kryptonLabel1.LabelStyle = Krypton.Toolkit.LabelStyle.TitlePanel;
            this.kryptonLabel1.Location = new System.Drawing.Point(12, 12);
            this.kryptonLabel1.Name = "kryptonLabel1";
            this.kryptonLabel1.Size = new System.Drawing.Size(246, 29);
            this.kryptonLabel1.TabIndex = 0;
            this.kryptonLabel1.Values.Text = "Informations de session";
            // 
            // btnEndSession
            // 
            this.btnEndSession.Location = new System.Drawing.Point(12, 507);
            this.btnEndSession.Name = "btnEndSession";
            this.btnEndSession.Size = new System.Drawing.Size(180, 42);
            this.btnEndSession.TabIndex = 2;
            this.btnEndSession.Values.Text = "Terminer la session";
            this.btnEndSession.Click += new System.EventHandler(this.BtnEndSession_Click);
            // 
            // btnClose
            // 
            this.btnClose.Location = new System.Drawing.Point(392, 507);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new System.Drawing.Size(180, 42);
            this.btnClose.TabIndex = 3;
            this.btnClose.Values.Text = "Fermer";
            this.btnClose.Click += new System.EventHandler(this.BtnClose_Click);
            // 
            // btnRefresh
            // 
            this.btnRefresh.Location = new System.Drawing.Point(202, 507);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(180, 42);
            this.btnRefresh.TabIndex = 4;
            this.btnRefresh.Values.Text = "Actualiser";
            this.btnRefresh.Click += new System.EventHandler(this.BtnRefresh_Click);
            // 
            // FRM_SESSION_INFO
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(584, 561);
            this.Controls.Add(this.kryptonPanel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FRM_SESSION_INFO";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Informations de session";
            this.Load += new System.EventHandler(this.FRM_SESSION_INFO_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).EndInit();
            this.kryptonPanel1.ResumeLayout(false);
            this.kryptonPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox2.Panel)).EndInit();
            this.kryptonGroupBox2.Panel.ResumeLayout(false);
            this.kryptonGroupBox2.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox2)).EndInit();
            this.kryptonGroupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1.Panel)).EndInit();
            this.kryptonGroupBox1.Panel.ResumeLayout(false);
            this.kryptonGroupBox1.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1)).EndInit();
            this.kryptonGroupBox1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonPanel kryptonPanel1;
        private Krypton.Toolkit.KryptonGroupBox kryptonGroupBox2;
        private Krypton.Toolkit.KryptonLabel lblHostname;
        private Krypton.Toolkit.KryptonLabel kryptonLabel12;
        private Krypton.Toolkit.KryptonLabel lblIpAddress;
        private Krypton.Toolkit.KryptonLabel kryptonLabel10;
        private Krypton.Toolkit.KryptonLabel lblLastActivity;
        private Krypton.Toolkit.KryptonLabel kryptonLabel8;
        private Krypton.Toolkit.KryptonLabel lblLoginTime;
        private Krypton.Toolkit.KryptonLabel kryptonLabel6;
        private Krypton.Toolkit.KryptonLabel lblSessionId;
        private Krypton.Toolkit.KryptonLabel kryptonLabel4;
        private Krypton.Toolkit.KryptonGroupBox kryptonGroupBox1;
        private Krypton.Toolkit.KryptonLabel lblStatus;
        private Krypton.Toolkit.KryptonLabel kryptonLabel14;
        private Krypton.Toolkit.KryptonLabel lblRole;
        private Krypton.Toolkit.KryptonLabel kryptonLabel16;
        private Krypton.Toolkit.KryptonLabel lblPhone;
        private Krypton.Toolkit.KryptonLabel kryptonLabel18;
        private Krypton.Toolkit.KryptonLabel lblEmail;
        private Krypton.Toolkit.KryptonLabel kryptonLabel20;
        private Krypton.Toolkit.KryptonLabel lblFullName;
        private Krypton.Toolkit.KryptonLabel kryptonLabel22;
        private Krypton.Toolkit.KryptonLabel lblUsername;
        private Krypton.Toolkit.KryptonLabel kryptonLabel24;
        private Krypton.Toolkit.KryptonLabel kryptonLabel1;
        private Krypton.Toolkit.KryptonButton btnRefresh;
        private Krypton.Toolkit.KryptonButton btnClose;
        private Krypton.Toolkit.KryptonButton btnEndSession;
    }
}
