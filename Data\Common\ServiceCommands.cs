﻿using Dapper;
using IRepairIT.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IRepairIT.Data.Common
{
    public class ServiceCommands
    {
        private readonly DataAccess _db;
        public ServiceCommands()
        {
            _db = new DataAccess();
        }
        public async Task<int> GetCount(string searchTerm)
        {
            const string sql = @"services WHERE (name LIKE CONCAT('%', @searchTerm, '%') OR description LIKE CONCAT('%', @searchTerm, '%'))";
            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            return await _db.CountQuery(sql, parameters);
        }
        public async Task<IEnumerable<Service>> GetALL(string searchTerm, int offset, int pageSize)
        {
            const string sql = @"SELECT * FROM services WHERE (name LIKE CONCAT('%', @searchTerm, '%') OR description LIKE CONCAT('%', @searchTerm, '%')) ORDER BY name ASC LIMIT @pageSize OFFSET @offsetVal";

            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            parameters.Add("offsetVal", offset);
            parameters.Add("pageSize", pageSize);
            var results = await _db.QueryQuery<Service>(sql, parameters);
            return results;
        }
        public async Task<int> InsertAsync(Service service)
        {
            const string sql = @"INSERT INTO services (name,description,price,duration) VALUES (@p_name,@p_description,@p_price,@p_duration);";

            var parameters = new
            {
                p_name=service.name,
                p_description = service.description,
                p_price=service.price,
                p_duration =service.duration,
                ureated_at = DateTime.Now,
                updated_at = DateTime.Now
            };

            return await _db.InsertAndGetIdAsync(sql, parameters);
        }
        public async Task<int> UpdateAsync(Service service)
        {
            const string sql = @"UPDATE services SET name = @p_name,description = @p_description,price = @p_price,duration = @p_duration,updated_at = CURRENT_TIMESTAMP WHERE id = @p_id;";

            var parameters = new
            {
                p_id=service.id,
                p_name = service.name,
                p_description = service.description,
                p_price = service.price,
                p_duration = service.duration
            };

            return await _db.ExecuteQuery(sql, parameters);
        }

        public async Task<Service> GetByIdAsync(int id)
        {
            const string sql = @"SELECT * FROM services WHERE id = @p_id";

            var parameters = new
            {
                p_id = id
            };

            return await _db.QuerySingleOrDefaultQuery<Service>(sql, parameters);
        }


        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                // First check if the service is used in any repair orders
                const string checkSql = @"SELECT COUNT(*) FROM repair_order_services WHERE service_id = @p_id";
                var checkParams = new { p_id = id };
                int usageCount = await _db.ExecuteScalerQuery<int>(checkSql, checkParams);

                if (usageCount > 0)
                {
                    // Service is in use, cannot delete
                    throw new Exception($"Ce service est utilisé dans {usageCount} commandes de réparation et ne peut pas être supprimé.");
                }

                // If not in use, proceed with deletion
                const string deleteSql = @"DELETE FROM services WHERE id = @p_id";
                var deleteParams = new { p_id = id };
                int rowsAffected = await _db.ExecuteQuery(deleteSql, deleteParams);

                return rowsAffected > 0;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<IEnumerable<Service>> GetAllForAutocomplete()
        {
            const string sql = @"SELECT id, name FROM services ORDER BY name ASC";
            return await _db.QueryQuery<Service>(sql);
        }

        public async Task<IEnumerable<Service>> SearchByNameAsync(string searchTerm)
        {
            const string sql = @"SELECT * FROM services WHERE name LIKE @p_search ORDER BY name ASC";
            var parameters = new { p_search = $"%{searchTerm}%" };
            return await _db.QueryQuery<Service>(sql, parameters);
        }

        public async Task<Service> GetByNameAsync(string name)
        {
            const string sql = @"SELECT * FROM services WHERE name = @p_name LIMIT 1";
            var parameters = new { p_name = name };
            return await _db.QuerySingleOrDefaultQuery<Service>(sql, parameters);
        }

        public async Task<IEnumerable<dynamic>> GetTotalServicesAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT
                    s.id,
                    s.name,
                    COUNT(ros.id) as service_count,
                    SUM(ros.price) as total_amount
                FROM services s
                JOIN repair_order_services ros ON s.id = ros.service_id
                JOIN repair_orders ro ON ros.repair_order_id = ro.id
                WHERE ro.created_at BETWEEN @startDate AND @endDate
                GROUP BY s.id, s.name
                ORDER BY total_amount DESC";

            var parameters = new DynamicParameters();
            parameters.Add("startDate", startDate);
            parameters.Add("endDate", endDate);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        public async Task<IEnumerable<dynamic>> GetTopServicesAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT
                    s.id,
                    s.name,
                    COUNT(ros.id) as service_count,
                    SUM(ros.price) as total_amount
                FROM services s
                JOIN repair_order_services ros ON s.id = ros.service_id
                JOIN repair_orders ro ON ros.repair_order_id = ro.id
                WHERE ro.created_at BETWEEN @startDate AND @endDate
                GROUP BY s.id, s.name
                ORDER BY service_count DESC
                LIMIT 10";

            var parameters = new DynamicParameters();
            parameters.Add("startDate", startDate);
            parameters.Add("endDate", endDate);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }
    }
}
