﻿namespace IRepairIT.FRMS.FLOGIN
{
    partial class FRM_LOGIN
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FRM_LOGIN));
            this.kryptonNavigator1 = new Krypton.Navigator.KryptonNavigator();
            this.buttonLeft = new Krypton.Navigator.ButtonSpecNavigator();
            this.pageAbout = new Krypton.Navigator.KryptonPage();
            this.kryptonPictureBox1 = new Krypton.Toolkit.KryptonPictureBox();
            this.kryptonPanel1 = new Krypton.Toolkit.KryptonPanel();
            this.kryptonLabel1 = new Krypton.Toolkit.KryptonLabel();
            this.BTNLOGIN = new Krypton.Toolkit.KryptonButton();
            this.CHKREMEMBERME = new Krypton.Toolkit.KryptonCheckBox();
            this.LBLHEAD = new Krypton.Toolkit.KryptonLabel();
            this.LBLPASSWORD = new Krypton.Toolkit.KryptonLabel();
            this.TXTUSER = new Krypton.Toolkit.KryptonComboBox();
            this.LBLUSERNAME = new Krypton.Toolkit.KryptonLabel();
            this.TXTPASSWORD = new Krypton.Toolkit.KryptonTextBox();
            this.btnShow = new Krypton.Toolkit.ButtonSpecAny();
            this.btnEditDB = new Krypton.Toolkit.ButtonSpecAny();
            this.errorProvider1 = new System.Windows.Forms.ErrorProvider(this.components);
            this.btnNewDB = new Krypton.Toolkit.ButtonSpecAny();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonNavigator1)).BeginInit();
            this.kryptonNavigator1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pageAbout)).BeginInit();
            this.pageAbout.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPictureBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).BeginInit();
            this.kryptonPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.TXTUSER)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.errorProvider1)).BeginInit();
            this.SuspendLayout();
            //
            // kryptonNavigator1
            //
            this.kryptonNavigator1.AutoSize = true;
            this.kryptonNavigator1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.kryptonNavigator1.Button.ButtonDisplayLogic = Krypton.Navigator.ButtonDisplayLogic.None;
            this.kryptonNavigator1.Button.ButtonSpecs.Add(this.buttonLeft);
            this.kryptonNavigator1.Button.CloseButtonAction = Krypton.Navigator.CloseButtonAction.RemovePageAndDispose;
            this.kryptonNavigator1.Button.CloseButtonDisplay = Krypton.Navigator.ButtonDisplay.Hide;
            this.kryptonNavigator1.Button.ContextButtonAction = Krypton.Navigator.ContextButtonAction.SelectPage;
            this.kryptonNavigator1.Button.ContextButtonDisplay = Krypton.Navigator.ButtonDisplay.Logic;
            this.kryptonNavigator1.Button.ContextMenuMapImage = Krypton.Navigator.MapKryptonPageImage.Small;
            this.kryptonNavigator1.Button.ContextMenuMapText = Krypton.Navigator.MapKryptonPageText.TextTitle;
            this.kryptonNavigator1.Button.NextButtonAction = Krypton.Navigator.DirectionButtonAction.ModeAppropriateAction;
            this.kryptonNavigator1.Button.NextButtonDisplay = Krypton.Navigator.ButtonDisplay.Logic;
            this.kryptonNavigator1.Button.PreviousButtonAction = Krypton.Navigator.DirectionButtonAction.ModeAppropriateAction;
            this.kryptonNavigator1.Button.PreviousButtonDisplay = Krypton.Navigator.ButtonDisplay.Logic;
            this.kryptonNavigator1.ControlKryptonFormFeatures = false;
            this.kryptonNavigator1.Dock = System.Windows.Forms.DockStyle.Left;
            this.kryptonNavigator1.Header.HeaderPositionBar = Krypton.Toolkit.VisualOrientation.Right;
            this.kryptonNavigator1.Header.HeaderPositionPrimary = Krypton.Toolkit.VisualOrientation.Top;
            this.kryptonNavigator1.Header.HeaderPositionSecondary = Krypton.Toolkit.VisualOrientation.Bottom;
            this.kryptonNavigator1.Header.HeaderStyleBar = Krypton.Toolkit.HeaderStyle.Secondary;
            this.kryptonNavigator1.Header.HeaderStylePrimary = Krypton.Toolkit.HeaderStyle.Primary;
            this.kryptonNavigator1.Header.HeaderStyleSecondary = Krypton.Toolkit.HeaderStyle.Secondary;
            this.kryptonNavigator1.Location = new System.Drawing.Point(0, 0);
            this.kryptonNavigator1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.kryptonNavigator1.Name = "kryptonNavigator1";
            this.kryptonNavigator1.NavigatorMode = Krypton.Navigator.NavigatorMode.HeaderBarCheckButtonGroup;
            this.kryptonNavigator1.Owner = null;
            this.kryptonNavigator1.PageBackStyle = Krypton.Toolkit.PaletteBackStyle.ControlClient;
            this.kryptonNavigator1.Pages.AddRange(new Krypton.Navigator.KryptonPage[] {
            this.pageAbout});
            this.kryptonNavigator1.PopupPages.AllowPopupPages = Krypton.Navigator.PopupPageAllow.OnlyCompatibleModes;
            this.kryptonNavigator1.PopupPages.Element = Krypton.Navigator.PopupPageElement.Navigator;
            this.kryptonNavigator1.PopupPages.Position = Krypton.Navigator.PopupPagePosition.FarMatch;
            this.kryptonNavigator1.SelectedIndex = 0;
            this.kryptonNavigator1.Size = new System.Drawing.Size(208, 419);
            this.kryptonNavigator1.StateNormal.HeaderGroup.Border.DrawBorders = ((Krypton.Toolkit.PaletteDrawBorders)((((Krypton.Toolkit.PaletteDrawBorders.Top | Krypton.Toolkit.PaletteDrawBorders.Bottom)
            | Krypton.Toolkit.PaletteDrawBorders.Left)
            | Krypton.Toolkit.PaletteDrawBorders.Right)));
            this.kryptonNavigator1.StateNormal.HeaderGroup.Border.Rounding = 0F;
            this.kryptonNavigator1.StateNormal.HeaderGroup.Border.Width = 0;
            this.kryptonNavigator1.StateNormal.HeaderGroup.HeaderBar.Border.DrawBorders = ((Krypton.Toolkit.PaletteDrawBorders)((((Krypton.Toolkit.PaletteDrawBorders.Top | Krypton.Toolkit.PaletteDrawBorders.Bottom)
            | Krypton.Toolkit.PaletteDrawBorders.Left)
            | Krypton.Toolkit.PaletteDrawBorders.Right)));
            this.kryptonNavigator1.StateNormal.HeaderGroup.HeaderBar.Border.Rounding = 0F;
            this.kryptonNavigator1.StateNormal.HeaderGroup.HeaderBar.Border.Width = 0;
            this.kryptonNavigator1.StateNormal.Panel.Color1 = System.Drawing.Color.White;
            this.kryptonNavigator1.StateNormal.Panel.Color2 = System.Drawing.Color.White;
            this.kryptonNavigator1.StateNormal.Panel.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Solid;
            this.kryptonNavigator1.TabIndex = 1;
            this.kryptonNavigator1.Text = "kryptonNavigator1";
            //
            // buttonLeft
            //
            this.buttonLeft.Edge = Krypton.Toolkit.PaletteRelativeEdgeAlign.Near;
            this.buttonLeft.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowLeft;
            this.buttonLeft.TypeRestricted = Krypton.Navigator.PaletteNavButtonSpecStyle.ArrowLeft;
            this.buttonLeft.UniqueName = "0285aa91770549d4b8b4e9197d60e623";
            this.buttonLeft.Click += new System.EventHandler(this.buttonLeft_Click);
            //
            // pageAbout
            //
            this.pageAbout.AutoHiddenSlideSize = new System.Drawing.Size(200, 200);
            this.pageAbout.Controls.Add(this.kryptonPictureBox1);
            this.pageAbout.Flags = 65534;
            this.pageAbout.LastVisibleSet = true;
            this.pageAbout.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.pageAbout.MinimumSize = new System.Drawing.Size(175, 58);
            this.pageAbout.Name = "pageAbout";
            this.pageAbout.Size = new System.Drawing.Size(176, 417);
            this.pageAbout.Text = "À propos";
            this.pageAbout.TextDescription = "À propos";
            this.pageAbout.TextTitle = "À propos";
            this.pageAbout.ToolTipTitle = "Page ToolTip";
            this.pageAbout.UniqueName = "fa1b39ad10e647a2bb08afc504590db3";
            //
            // kryptonPictureBox1
            //
            this.kryptonPictureBox1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.kryptonPictureBox1.Image = ((System.Drawing.Image)(resources.GetObject("kryptonPictureBox1.Image")));
            this.kryptonPictureBox1.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.kryptonPictureBox1.Location = new System.Drawing.Point(0, 249);
            this.kryptonPictureBox1.Name = "kryptonPictureBox1";
            this.kryptonPictureBox1.Size = new System.Drawing.Size(176, 168);
            this.kryptonPictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.kryptonPictureBox1.TabIndex = 8;
            this.kryptonPictureBox1.TabStop = false;
            //
            // kryptonPanel1
            //
            this.kryptonPanel1.Controls.Add(this.kryptonLabel1);
            this.kryptonPanel1.Controls.Add(this.BTNLOGIN);
            this.kryptonPanel1.Controls.Add(this.CHKREMEMBERME);
            this.kryptonPanel1.Controls.Add(this.LBLHEAD);
            this.kryptonPanel1.Controls.Add(this.LBLPASSWORD);
            this.kryptonPanel1.Controls.Add(this.TXTUSER);
            this.kryptonPanel1.Controls.Add(this.LBLUSERNAME);
            this.kryptonPanel1.Controls.Add(this.TXTPASSWORD);
            this.kryptonPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel1.Location = new System.Drawing.Point(208, 0);
            this.kryptonPanel1.Name = "kryptonPanel1";
            this.kryptonPanel1.Size = new System.Drawing.Size(331, 419);
            this.kryptonPanel1.TabIndex = 0;
            //
            // kryptonLabel1
            //
            this.kryptonLabel1.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.kryptonLabel1.Location = new System.Drawing.Point(22, 79);
            this.kryptonLabel1.Name = "kryptonLabel1";
            this.kryptonLabel1.Size = new System.Drawing.Size(287, 21);
            this.kryptonLabel1.TabIndex = 1;
            this.kryptonLabel1.Values.Text = "Système de gestion des ateliers de réparation";
            //
            // BTNLOGIN
            //
            this.BTNLOGIN.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.BTNLOGIN.Cursor = System.Windows.Forms.Cursors.Hand;
            this.BTNLOGIN.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.BTNLOGIN.Location = new System.Drawing.Point(19, 321);
            this.BTNLOGIN.Name = "BTNLOGIN";
            this.BTNLOGIN.OverrideDefault.Back.Color1 = System.Drawing.Color.SteelBlue;
            this.BTNLOGIN.OverrideDefault.Back.Color2 = System.Drawing.Color.DeepSkyBlue;
            this.BTNLOGIN.Size = new System.Drawing.Size(293, 45);
            this.BTNLOGIN.StateCommon.Border.DrawBorders = Krypton.Toolkit.PaletteDrawBorders.None;
            this.BTNLOGIN.StateCommon.Border.Rounding = 10F;
            this.BTNLOGIN.StateCommon.Border.Width = 0;
            this.BTNLOGIN.StateCommon.Content.Image.Effect = Krypton.Toolkit.PaletteImageEffect.Normal;
            this.BTNLOGIN.StateCommon.Content.Image.ImageH = Krypton.Toolkit.PaletteRelativeAlign.Near;
            this.BTNLOGIN.StateCommon.Content.Image.ImageV = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.BTNLOGIN.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.BTNLOGIN.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI Semibold", 12F);
            this.BTNLOGIN.StateNormal.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(123)))), ((int)(((byte)(255)))));
            this.BTNLOGIN.StateNormal.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(105)))), ((int)(((byte)(217)))));
            this.BTNLOGIN.StateNormal.Border.Rounding = 10F;
            this.BTNLOGIN.StateNormal.Border.Width = 0;
            this.BTNLOGIN.StatePressed.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(98)))), ((int)(((byte)(204)))));
            this.BTNLOGIN.StatePressed.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(91)))), ((int)(((byte)(187)))));
            this.BTNLOGIN.StateTracking.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(105)))), ((int)(((byte)(217)))));
            this.BTNLOGIN.StateTracking.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(98)))), ((int)(((byte)(204)))));
            this.BTNLOGIN.TabIndex = 7;
            this.BTNLOGIN.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.BTNLOGIN.Values.Text = "Connexion";
            this.BTNLOGIN.Click += new System.EventHandler(this.BTNLOGIN_Click);
            //
            // CHKREMEMBERME
            //
            this.CHKREMEMBERME.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.CHKREMEMBERME.Location = new System.Drawing.Point(19, 294);
            this.CHKREMEMBERME.Name = "CHKREMEMBERME";
            this.CHKREMEMBERME.Size = new System.Drawing.Size(140, 21);
            this.CHKREMEMBERME.StateCommon.DrawFocus = Krypton.Toolkit.InheritBool.False;
            this.CHKREMEMBERME.TabIndex = 6;
            this.CHKREMEMBERME.Values.Text = "Se souvenir de moi";
            //
            // LBLHEAD
            //
            this.LBLHEAD.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.LBLHEAD.LabelStyle = Krypton.Toolkit.LabelStyle.TitleControl;
            this.LBLHEAD.Location = new System.Drawing.Point(119, 40);
            this.LBLHEAD.Name = "LBLHEAD";
            this.LBLHEAD.Size = new System.Drawing.Size(92, 30);
            this.LBLHEAD.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(13)))), ((int)(((byte)(110)))), ((int)(((byte)(253)))));
            this.LBLHEAD.TabIndex = 0;
            this.LBLHEAD.Values.Text = "IRepairIT";
            //
            // LBLPASSWORD
            //
            this.LBLPASSWORD.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.LBLPASSWORD.Location = new System.Drawing.Point(19, 232);
            this.LBLPASSWORD.Name = "LBLPASSWORD";
            this.LBLPASSWORD.Size = new System.Drawing.Size(92, 21);
            this.LBLPASSWORD.TabIndex = 4;
            this.LBLPASSWORD.Values.Text = "Mot de passe";
            //
            // TXTUSER
            //
            this.TXTUSER.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.TXTUSER.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.TXTUSER.DropDownWidth = 337;
            this.TXTUSER.IntegralHeight = false;
            this.TXTUSER.Location = new System.Drawing.Point(19, 198);
            this.TXTUSER.Name = "TXTUSER";
            this.TXTUSER.Size = new System.Drawing.Size(293, 28);
            this.TXTUSER.StateCommon.ComboBox.Border.Rounding = 2F;
            this.TXTUSER.StateCommon.ComboBox.Border.Width = 1;
            this.TXTUSER.StateCommon.ComboBox.Content.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25F, System.Drawing.FontStyle.Bold);
            this.TXTUSER.StateCommon.Item.Content.ShortText.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25F, System.Drawing.FontStyle.Bold);
            this.TXTUSER.TabIndex = 3;
            //
            // LBLUSERNAME
            //
            this.LBLUSERNAME.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.LBLUSERNAME.Location = new System.Drawing.Point(19, 171);
            this.LBLUSERNAME.Name = "LBLUSERNAME";
            this.LBLUSERNAME.Size = new System.Drawing.Size(117, 21);
            this.LBLUSERNAME.TabIndex = 2;
            this.LBLUSERNAME.Values.Text = "Nom d\'utilisateur";
            //
            // TXTPASSWORD
            //
            this.TXTPASSWORD.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.TXTPASSWORD.ButtonSpecs.Add(this.btnShow);
            this.TXTPASSWORD.Location = new System.Drawing.Point(19, 259);
            this.TXTPASSWORD.Name = "TXTPASSWORD";
            this.TXTPASSWORD.PasswordChar = '#';
            this.TXTPASSWORD.Size = new System.Drawing.Size(293, 29);
            this.TXTPASSWORD.StateCommon.Border.Rounding = 2F;
            this.TXTPASSWORD.StateCommon.Border.Width = 1;
            this.TXTPASSWORD.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25F, System.Drawing.FontStyle.Bold);
            this.TXTPASSWORD.TabIndex = 5;
            this.TXTPASSWORD.Tag = "0";
            this.TXTPASSWORD.Enter += new System.EventHandler(this.TXTPASSWORD_Enter);
            //
            // btnShow
            //
            this.btnShow.Checked = Krypton.Toolkit.ButtonCheckState.Checked;
            this.btnShow.Image = global::IRepairIT.Properties.Resources.eye;
            this.btnShow.Tag = "show";
            this.btnShow.UniqueName = "9b157c0cc096438d81caa05581f76fc3";
            this.btnShow.Click += new System.EventHandler(this.btnShow_Click);
            //
            // btnEditDB
            //
            this.btnEditDB.UniqueName = "d0825624ba914488b4e8debba310f668";
            //
            // errorProvider1
            //
            this.errorProvider1.ContainerControl = this;
            //
            // btnNewDB
            //
            this.btnNewDB.Image = global::IRepairIT.Properties.Resources.folder;
            this.btnNewDB.UniqueName = "1cf23d9546c1458596052e4ce6703597";
            //
            // FRM_LOGIN
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ButtonSpecs.Add(this.btnNewDB);
            this.ButtonSpecs.Add(this.btnEditDB);
            this.ClientSize = new System.Drawing.Size(539, 419);
            this.Controls.Add(this.kryptonPanel1);
            this.Controls.Add(this.kryptonNavigator1);
            this.HeaderStyle = Krypton.Toolkit.HeaderStyle.Calendar;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FRM_LOGIN";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Connexion - iRepairIT";
            this.Load += new System.EventHandler(this.FRM_LOGIN_Load);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.FRM_LOGIN_KeyDown);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonNavigator1)).EndInit();
            this.kryptonNavigator1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.pageAbout)).EndInit();
            this.pageAbout.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPictureBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).EndInit();
            this.kryptonPanel1.ResumeLayout(false);
            this.kryptonPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.TXTUSER)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.errorProvider1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Krypton.Navigator.KryptonNavigator kryptonNavigator1;
        private Krypton.Navigator.ButtonSpecNavigator buttonLeft;
        private Krypton.Navigator.KryptonPage pageAbout;
        private Krypton.Toolkit.KryptonPanel kryptonPanel1;
        private Krypton.Toolkit.KryptonButton BTNLOGIN;
        private Krypton.Toolkit.KryptonCheckBox CHKREMEMBERME;
        private Krypton.Toolkit.KryptonLabel LBLHEAD;
        private Krypton.Toolkit.KryptonLabel LBLPASSWORD;
        private Krypton.Toolkit.KryptonComboBox TXTUSER;
        private Krypton.Toolkit.KryptonLabel LBLUSERNAME;
        private Krypton.Toolkit.KryptonTextBox TXTPASSWORD;
        private Krypton.Toolkit.ButtonSpecAny btnShow;
        private Krypton.Toolkit.KryptonPictureBox kryptonPictureBox1;
        private Krypton.Toolkit.KryptonLabel kryptonLabel1;
        private Krypton.Toolkit.ButtonSpecAny btnNewDB;
        private Krypton.Toolkit.ButtonSpecAny btnEditDB;
        private System.Windows.Forms.ErrorProvider errorProvider1;
    }
}