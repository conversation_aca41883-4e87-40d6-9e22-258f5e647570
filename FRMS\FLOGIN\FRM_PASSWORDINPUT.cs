using Krypton.Toolkit;
using System;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FLOGIN
{
    public partial class FRM_PASSWORDINPUT : KryptonForm
    {
        public string EnteredPassword { get; private set; }

        public FRM_PASSWORDINPUT()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            EnteredPassword = txtPassword.Text;
            DialogResult = DialogResult.OK;
            Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void txtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnOK_Click(sender, e);
            }
            else if (e.KeyCode == Keys.Escape)
            {
                btnCancel_Click(sender, e);
            }
        }

        private void txtPassword_Enter(object sender, EventArgs e)
        {
            txtPassword.SelectAll();
        }

        private void btnShow_Click(object sender, EventArgs e)
        {
            txtPassword.PasswordChar = (txtPassword.Tag as string == "0") ? '\0' : '#';
            txtPassword.ButtonSpecs[0].Image = (txtPassword.Tag as string == "0") ?
                Properties.Resources.eye_no : Properties.Resources.eye;
            txtPassword.Tag = (txtPassword.Tag as string == "0") ? "1" : "0";
        }
    }
}
