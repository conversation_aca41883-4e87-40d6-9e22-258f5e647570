﻿using FastReport;
using IRepairIT.Data.Common;
using IRepairIT.Models;
using IRepairIT.Utilities;
using Krypton.Toolkit;
using System;
using System.Data;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FINVENTORY
{
    public partial class FRM_INVENTORY_LIST : KryptonForm
    {
        private readonly InventoryCommands _Cmd;
        private int _pageSize;
        private int _currentPage = 1;
        private int _totalRows = 0;
        private string _searchTerm = "";

        public FRM_INVENTORY_LIST()
        {
            InitializeComponent();
            _Cmd = new InventoryCommands();

            // Set default page size to 100
            kryptonComboBox1.Items.AddRange(new object[] { "10", "25", "50", "100", "250", "500" });
            kryptonComboBox1.SelectedIndex = 3; // Select 100 by default
            _pageSize = 100;

            // Set up event handlers
            kryptonComboBox1.SelectedIndexChanged += CmbPageView_SelectedIndexChanged;
            TXTSearch.TextChanged += TXTSearch_TextChanged;

            // Set pagination button text
            btnFirst.Text = "Premier";
            btnPrev.Text = "Précédent";
            btnNext.Text = "Suivant";
            btnLast.Text = "Dernier";

            // Set up button click handlers
            btnFirst.Click += BtnFirst_Click;
            btnPrev.Click += BtnPrev_Click;
            btnNext.Click += BtnNext_Click;
            btnLast.Click += BtnLast_Click;
            btnSearch.Click += BtnSearch_Click;
            btnClearSearch.Click += BtnClearSearch_Click;
        }
        public async Task LoadDashboardData()
        {
            try
            {
                LblLowStock.Text = (await _Cmd.GetLowStock()).ToString();
                lblCategorie.Text = (await _Cmd.GetCategories()).ToString();
                lblTotalCount.Text = (await _Cmd.GetCount("")).ToString();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des données du tableau de bord: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        public async Task LoadInventory()
        {
            try
            {
                // Get page size from combo box
                if (kryptonComboBox1.SelectedItem != null)
                {
                    _pageSize = Convert.ToInt32(kryptonComboBox1.Text);
                }

                // Calculate offset based on current page and page size
                int offset = (_currentPage - 1) * _pageSize;

                // Get inventory items with pagination
                var result = await _Cmd.GetALL(_searchTerm, offset, _pageSize);
                _totalRows = await _Cmd.GetCount(_searchTerm);

                // Set data source
                kryptonDataGridView1.DataSource = result;

                // Update pagination info
                kryptonHeaderGroup1.ValuesSecondary.Heading = $"Total: {_totalRows} - Page: {_currentPage}/{Math.Ceiling((double)_totalRows / _pageSize)}";

                // Enable/disable pagination buttons based on current page
                UpdatePaginationButtons();

                // Apply styling to the DataGridView
                DGV_STYLE.DesignDGV(kryptonDataGridView1);

                // Configure DataGridView columns
                ConfigureDataGridViewColumns();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement de l'inventaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
        private async void FRM_INVENTORY_LIST_Load(object sender, EventArgs e)
        {
            try
            {
                // Set form title
                this.Text = "Pièces";
                kryptonHeaderGroup1.ValuesPrimary.Heading = "Pièces";

                // Set button text
                btnAdd.Text = "Ajouter une pièce";
                btnEdit.Text = "Modifier";
                btnDelete.Text = "Supprimer";

                // Load dashboard data
                await LoadDashboardData();

                // Load inventory data
                await LoadInventory();

                // Set up context menu
                SetupContextMenu();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du formulaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void SetupContextMenu()
        {
            try
            {
                // Create a standard context menu strip
                ContextMenuStrip contextMenuStrip = new ContextMenuStrip();

                // Add items to the standard context menu
                ToolStripMenuItem refreshItem = new ToolStripMenuItem("Rafraîchir", Properties.Resources.refresh, async (s, e) => await LoadInventory());
                ToolStripMenuItem addItem = new ToolStripMenuItem("Ajouter une pièce", Properties.Resources.button_circle_add, btnAdd_Click);
                ToolStripMenuItem editItem = new ToolStripMenuItem("Modifier", Properties.Resources.tool_pencil, btnEdit_Click);
                ToolStripMenuItem deleteItem = new ToolStripMenuItem("Supprimer", Properties.Resources.trash, btnDelete_Click);
                ToolStripMenuItem transactionsItem = new ToolStripMenuItem("Voir les mouvements de stock", Properties.Resources.eye, ViewTransactions_Click);
                ToolStripSeparator separator = new ToolStripSeparator();
                ToolStripMenuItem printItem = new ToolStripMenuItem("Imprimer", Properties.Resources.tablet_iphone, PrintDataGridView);

                // Add items to the menu
                contextMenuStrip.Items.Add(refreshItem);
                contextMenuStrip.Items.Add(addItem);
                contextMenuStrip.Items.Add(editItem);
                contextMenuStrip.Items.Add(deleteItem);
                contextMenuStrip.Items.Add(transactionsItem);
                contextMenuStrip.Items.Add(separator);
                contextMenuStrip.Items.Add(printItem);

                // Attach the context menu to the DataGridView
                kryptonDataGridView1.ContextMenuStrip = contextMenuStrip;

                // Style the context menu
                contextMenuStrip.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
                contextMenuStrip.ShowImageMargin = true;
                contextMenuStrip.ShowCheckMargin = false;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la configuration du menu contextuel: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void UpdatePaginationButtons()
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);

            // For Krypton ButtonSpecHeaderGroup, we need to use ButtonEnabled enum instead of bool
            btnFirst.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnPrev.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnNext.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
            btnLast.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
        }

        private async void CmbPageView_SelectedIndexChanged(object sender, EventArgs e)
        {
            _currentPage = 1; // Reset to first page when changing page size
            await LoadInventory();
        }

        private async void TXTSearch_TextChanged(object sender, EventArgs e)
        {
            _searchTerm = TXTSearch.Text.Trim();
            _currentPage = 1; // Reset to first page when searching
            await LoadInventory();
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            _searchTerm = TXTSearch.Text.Trim();
            _currentPage = 1;
            await LoadInventory();
        }

        private async void BtnClearSearch_Click(object sender, EventArgs e)
        {
            TXTSearch.Clear();
            _searchTerm = "";
            _currentPage = 1;
            await LoadInventory();
        }

        private async void BtnFirst_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage = 1;
                await LoadInventory();
            }
        }

        private async void BtnPrev_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadInventory();
            }
        }

        private async void BtnNext_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage++;
                await LoadInventory();
            }
        }

        private async void BtnLast_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage = totalPages;
                await LoadInventory();
            }
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            var frm = new FRM_INVENTORY();
            frm.ShowDialog();

            if (frm.HasChanges)
            {
                await LoadDashboardData();
                await LoadInventory();
            }
        }

        private async void btnEdit_Click(object sender, EventArgs e)
        {
            if (kryptonDataGridView1.SelectedRows.Count > 0)
            {
                int partId = Convert.ToInt32(kryptonDataGridView1.SelectedRows[0].Cells["Id"].Value);
                var frm = new FRM_INVENTORY(partId);
                frm.ShowDialog();

                if (frm.HasChanges)
                {
                    await LoadDashboardData();
                    await LoadInventory();
                }
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un article à modifier", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (kryptonDataGridView1.SelectedRows.Count > 0)
            {
                int partId = Convert.ToInt32(kryptonDataGridView1.SelectedRows[0].Cells["Id"].Value);
                string partName = kryptonDataGridView1.SelectedRows[0].Cells["Name"].Value.ToString();

                var result = KryptonMessageBox.Show($"Êtes-vous sûr de vouloir supprimer l'article '{partName}'?", "Confirmation",
                    KryptonMessageBoxButtons.YesNo, KryptonMessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        bool success = await _Cmd.DeleteAsync(partId);
                        if (success)
                        {
                            KryptonMessageBox.Show("L'article a été supprimé avec succès", "Succès",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                            // Refresh the data
                            await LoadDashboardData();
                            await LoadInventory();
                        }
                        else
                        {
                            KryptonMessageBox.Show("Erreur lors de la suppression de l'article", "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        KryptonMessageBox.Show($"Erreur lors de la suppression de l'article: {ex.Message}", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un article à supprimer", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private void ConfigureDataGridViewColumns()
        {
            try
            {
                // Clear existing columns
                kryptonDataGridView1.Columns.Clear();

                // Add columns
                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Id",
                    DataPropertyName = "Id",
                    HeaderText = "#",
                    Width = 40,
                    ReadOnly = true
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Nom",
                    DataPropertyName = "Name",
                    HeaderText = "Nom",
                    Width = 200,
                    ReadOnly = true
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Code",
                    DataPropertyName = "Code",
                    HeaderText = "Code",
                    Width = 100,
                    ReadOnly = true
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Categorie",
                    DataPropertyName = "Category",
                    HeaderText = "Catégorie",
                    Width = 120,
                    ReadOnly = true
                });

                // Add min_quantity column (hidden)
                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "min_quantity",
                    DataPropertyName = "min_quantity",
                    HeaderText = "Quantité Min",
                    Width = 80,
                    ReadOnly = true,
                    Visible = false
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "quantity",
                    DataPropertyName = "quantity",
                    HeaderText = "Quantité",
                    Width = 80,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N0",
                        Alignment = DataGridViewContentAlignment.MiddleCenter
                    }
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "PrixAchat",
                    DataPropertyName = "purchase_price",
                    HeaderText = "Prix d'achat",
                    Width = 100,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "C2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "PrixVente",
                    DataPropertyName = "selling_price",
                    HeaderText = "Prix de vente",
                    Width = 100,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "C2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                });

                // Add action buttons column
                DataGridViewButtonColumn editButtonColumn = new DataGridViewButtonColumn
                {
                    Name = "Actions",
                    HeaderText = "Actions",
                    Text = "Modifier",
                    UseColumnTextForButtonValue = true,
                    Width = 100
                };
                kryptonDataGridView1.Columns.Add(editButtonColumn);

                // Set row formatting
                kryptonDataGridView1.RowTemplate.Height = 35;
                kryptonDataGridView1.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(245, 245, 245);

                // Add low stock indicator
                kryptonDataGridView1.CellFormatting += (sender, e) =>
                {
                    try
                    {
                        if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
                        {
                            // Check if this is the quantity column
                            string columnName = kryptonDataGridView1.Columns[e.ColumnIndex].Name;
                            if (columnName.Equals("quantity", StringComparison.OrdinalIgnoreCase))
                            {
                                DataGridViewRow row = kryptonDataGridView1.Rows[e.RowIndex];

                                // Get the Part object from the DataBoundItem
                                if (row.DataBoundItem is Part part)
                                {
                                    int quantity = part.Quantity;
                                    int minQuantity = part.Min_Quantity;

                                    if (quantity <= minQuantity)
                                    {
                                        e.CellStyle.BackColor = Color.FromArgb(255, 200, 200);
                                        e.CellStyle.ForeColor = Color.Red;
                                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                                        row.Cells[e.ColumnIndex].ToolTipText = "Stock faible";
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Silently handle any errors in the cell formatting
                        System.Diagnostics.Debug.WriteLine($"Error in CellFormatting: {ex.Message}");
                    }
                };

                // Handle cell click for action buttons
                kryptonDataGridView1.CellClick += (sender, e) =>
                {
                    if (e.RowIndex >= 0 && e.ColumnIndex == kryptonDataGridView1.Columns["Actions"].Index)
                    {
                        int partId = Convert.ToInt32(kryptonDataGridView1.Rows[e.RowIndex].Cells["Id"].Value);
                        var frm = new FRM_INVENTORY(partId);
                        frm.ShowDialog();

                        if (frm.HasChanges)
                        {
                            LoadDashboardData().ConfigureAwait(false);
                            LoadInventory().ConfigureAwait(false);
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la configuration des colonnes: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void ViewTransactions_Click(object sender, EventArgs e)
        {
            if (kryptonDataGridView1.SelectedRows.Count > 0)
            {
                int partId = Convert.ToInt32(kryptonDataGridView1.SelectedRows[0].Cells["Id"].Value);
                var frm = new FRM_INVENTORY_TRANSACTIONS(partId);
                frm.ShowDialog();
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un article pour voir ses mouvements de stock", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private void PrintDataGridView(object sender, EventArgs e)
        {
            try
            {
                // Check if there's data to print
                if (kryptonDataGridView1.Rows.Count == 0)
                {
                    KryptonMessageBox.Show("Aucune donnée à imprimer", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                // Create a new report
                Report report = new Report();

                // Convert DataGridView data to DataTable
                DataTable dt = new DataTable("Inventory");

                // Add columns for the report
                dt.Columns.Add("Code", typeof(string));
                dt.Columns.Add("Nom", typeof(string));
                dt.Columns.Add("Categorie", typeof(string));
                dt.Columns.Add("PrixAchat", typeof(decimal));
                dt.Columns.Add("PrixVente", typeof(decimal));
                dt.Columns.Add("Quantite", typeof(int));
                dt.Columns.Add("QuantiteMin", typeof(int));

                // Add rows from DataGridView
                foreach (DataGridViewRow row in kryptonDataGridView1.Rows)
                {
                    if (!row.IsNewRow)
                    {
                        DataRow dataRow = dt.NewRow();
                        // Get the Part object from the DataBoundItem
                        if (row.DataBoundItem is Part part)
                        {
                            dataRow["Code"] = part.Code ?? "";
                            dataRow["Nom"] = part.Name ?? "";
                            dataRow["Categorie"] = part.Category ?? "";
                            dataRow["PrixAchat"] = part.Purchase_Price;
                            dataRow["PrixVente"] = part.Selling_Price;
                            dataRow["Quantite"] = part.Quantity;
                            dataRow["QuantiteMin"] = part.Min_Quantity;
                        }
                        else
                        {
                            // Fallback to cell values if DataBoundItem is not a Part
                            dataRow["Code"] = row.Cells["Code"].Value?.ToString() ?? "";
                            dataRow["Nom"] = row.Cells["Nom"].Value?.ToString() ?? "";
                            dataRow["Categorie"] = row.Cells["Categorie"].Value?.ToString() ?? "";
                            dataRow["PrixAchat"] = row.Cells["PrixAchat"].Value != null ? Convert.ToDecimal(row.Cells["PrixAchat"].Value) : 0;
                            dataRow["PrixVente"] = row.Cells["PrixVente"].Value != null ? Convert.ToDecimal(row.Cells["PrixVente"].Value) : 0;
                            dataRow["Quantite"] = 0;
                            dataRow["QuantiteMin"] = 0;
                        }
                        dt.Rows.Add(dataRow);
                    }
                }

                // Create a simple report design
                string reportXml = @"<?xml version=""1.0"" encoding=""utf-8""?>
<Report ScriptLanguage=""CSharp"" ReportInfo.Created=""01/01/2023 00:00:00"" ReportInfo.Modified=""01/01/2023 00:00:00"" ReportInfo.CreatorVersion=""*******"">
  <Dictionary>
    <TableDataSource Name=""Inventory"" ReferenceName=""Inventory"">
      <Column Name=""Code"" DataType=""System.String""/>
      <Column Name=""Nom"" DataType=""System.String""/>
      <Column Name=""Categorie"" DataType=""System.String""/>
      <Column Name=""PrixAchat"" DataType=""System.Decimal""/>
      <Column Name=""PrixVente"" DataType=""System.Decimal""/>
      <Column Name=""Quantite"" DataType=""System.Int32""/>
      <Column Name=""QuantiteMin"" DataType=""System.Int32""/>
    </TableDataSource>
    <Parameter Name=""Date"" DataType=""System.String""/>
    <Parameter Name=""TotalItems"" DataType=""System.String""/>
  </Dictionary>
  <ReportPage Name=""Page1"" Landscape=""true"" PaperWidth=""297"" PaperHeight=""210"" RawPaperSize=""9"" FirstPageSource=""15"" OtherPagesSource=""15"">
    <ReportTitleBand Name=""ReportTitle1"" Width=""1047.06"" Height=""75.6"">
      <TextObject Name=""Text1"" Width=""1047.06"" Height=""28.35"" Text=""Liste des Articles"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 14pt, style=Bold""/>
      <TextObject Name=""Text2"" Left=""858.06"" Top=""28.35"" Width=""189"" Height=""18.9"" Text=""Date: [Date]"" HorzAlign=""Right"" Font=""Arial, 10pt""/>
      <TextObject Name=""Text3"" Top=""47.25"" Width=""1047.06"" Height=""18.9"" Text=""Nombre total d'articles: [TotalItems]"" Font=""Arial, 10pt""/>
    </ReportTitleBand>
    <PageHeaderBand Name=""PageHeader1"" Top=""79.6"" Width=""1047.06"" Height=""28.35"" Fill.Color=""WhiteSmoke"">
      <TextObject Name=""Header0"" Width=""94.5"" Height=""28.35"" Text=""Code"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold"" Border.Lines=""All""/>
      <TextObject Name=""Header1"" Left=""94.5"" Width=""189"" Height=""28.35"" Text=""Nom"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold"" Border.Lines=""All""/>
      <TextObject Name=""Header2"" Left=""283.5"" Width=""141.75"" Height=""28.35"" Text=""Catégorie"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold"" Border.Lines=""All""/>
      <TextObject Name=""Header3"" Left=""425.25"" Width=""141.75"" Height=""28.35"" Text=""Prix d'achat"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold"" Border.Lines=""All""/>
      <TextObject Name=""Header4"" Left=""567"" Width=""141.75"" Height=""28.35"" Text=""Prix de vente"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold"" Border.Lines=""All""/>
      <TextObject Name=""Header5"" Left=""708.75"" Width=""141.75"" Height=""28.35"" Text=""Quantité"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold"" Border.Lines=""All""/>
      <TextObject Name=""Header6"" Left=""850.5"" Width=""141.75"" Height=""28.35"" Text=""Quantité Min"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold"" Border.Lines=""All""/>
    </PageHeaderBand>
    <DataBand Name=""Data1"" Top=""111.95"" Width=""1047.06"" Height=""18.9"" DataSource=""Inventory"">
      <TextObject Name=""Data0"" Width=""94.5"" Height=""18.9"" Text=""[Inventory.Code]"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 9pt"" Border.Lines=""All""/>
      <TextObject Name=""Data1"" Left=""94.5"" Width=""189"" Height=""18.9"" Text=""[Inventory.Nom]"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 9pt"" Border.Lines=""All""/>
      <TextObject Name=""Data2"" Left=""283.5"" Width=""141.75"" Height=""18.9"" Text=""[Inventory.Categorie]"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 9pt"" Border.Lines=""All""/>
      <TextObject Name=""Data3"" Left=""425.25"" Width=""141.75"" Height=""18.9"" Text=""[Inventory.PrixAchat]"" Format=""Currency"" Format.UseLocale=""true"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 9pt"" Border.Lines=""All""/>
      <TextObject Name=""Data4"" Left=""567"" Width=""141.75"" Height=""18.9"" Text=""[Inventory.PrixVente]"" Format=""Currency"" Format.UseLocale=""true"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 9pt"" Border.Lines=""All""/>
      <TextObject Name=""Data5"" Left=""708.75"" Width=""141.75"" Height=""18.9"" Text=""[Inventory.Quantite]"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 9pt"" Border.Lines=""All""/>
      <TextObject Name=""Data6"" Left=""850.5"" Width=""141.75"" Height=""18.9"" Text=""[Inventory.QuantiteMin]"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 9pt"" Border.Lines=""All""/>
    </DataBand>
    <PageFooterBand Name=""PageFooter1"" Top=""134.85"" Width=""1047.06"" Height=""18.9"">
      <TextObject Name=""Text4"" Width=""1047.06"" Height=""18.9"" Text=""Page [Page] de [TotalPages]"" HorzAlign=""Right"" Font=""Arial, 8pt""/>
    </PageFooterBand>
  </ReportPage>
</Report>";

                // Register the data source
                report.RegisterData(dt, "Inventory");

                // Set report parameters
                report.SetParameterValue("Date", DateTime.Now.ToString("dd/MM/yyyy"));
                report.SetParameterValue("TotalItems", dt.Rows.Count.ToString());

                // Load the report design
                report.Load(new MemoryStream(System.Text.Encoding.UTF8.GetBytes(reportXml)));

                // Show preview dialog
                report.Show();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'impression: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
    }
}
