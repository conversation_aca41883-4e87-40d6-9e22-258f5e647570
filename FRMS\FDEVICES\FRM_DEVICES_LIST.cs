﻿﻿using FastReport;
using IRepairIT.Data.Common;
using IRepairIT.Models;
using IRepairIT.Utilities;
using Krypton.Toolkit;
using System;
using System.Data;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FDEVICES
{
    public partial class FRM_DEVICES_LIST : KryptonForm
    {
        private readonly DeviceCommands _cmd;
        private readonly CustomerCommands _customerCmd;
        private int _pageSize;
        private int _currentPage = 1;
        private int _totalRows = 0;
        private string _searchTerm = "";

        // Advanced filter properties
        private string _selectedDeviceType = null;
        private string _selectedBrand = null;
        private int? _selectedCustomerId = null;
        private DateTime? _startDate = null;
        private DateTime? _endDate = null;
        private bool _advancedFiltersVisible = false;

        public FRM_DEVICES_LIST()
        {
            InitializeComponent();
            _cmd = new DeviceCommands();
            _customerCmd = new CustomerCommands();

            // Set default page size to 100
            kryptonComboBox1.Items.AddRange(new object[] { "10", "25", "50", "100", "250", "500" });
            kryptonComboBox1.SelectedIndex = 3; // Select 100 by default
            _pageSize = 100;

            // Set up event handlers
            kryptonComboBox1.SelectedIndexChanged += CmbPageView_SelectedIndexChanged;
            TXTSearch.TextChanged += TXTSearch_TextChanged;

            // Set pagination button text
            btnFirst.Text = "Premier";
            btnPrev.Text = "Précédent";
            btnNext.Text = "Suivant";
            btnLast.Text = "Dernier";

            // Set up button click handlers
            btnFirst.Click += BtnFirst_Click;
            btnPrev.Click += BtnPrev_Click;
            btnNext.Click += BtnNext_Click;
            btnLast.Click += BtnLast_Click;
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnSearch.Click += BtnSearch_Click;
            btnClearSearch.Click += BtnClearSearch_Click;
        }

        public async Task LoadDevices()
        {
            try
            {
                // Get page size from combo box
                if (kryptonComboBox1.SelectedItem != null)
                {
                    _pageSize = Convert.ToInt32(kryptonComboBox1.Text);
                }

                // Calculate offset based on current page and page size
                int offset = (_currentPage - 1) * _pageSize;

                // Get devices with pagination and filters
                var result = await _cmd.GetAll(_searchTerm, offset, _pageSize,
                    _selectedDeviceType, _selectedBrand, _selectedCustomerId, _startDate, _endDate);

                _totalRows = await _cmd.GetCount(_searchTerm,
                    _selectedDeviceType, _selectedBrand, _selectedCustomerId, _startDate, _endDate);

                // Set data source
                kryptonDataGridView1.DataSource = result;

                // Update pagination info
                kryptonHeaderGroup1.ValuesSecondary.Heading = $"Total: {_totalRows} - Page: {_currentPage}/{Math.Ceiling((double)_totalRows / _pageSize)}";

                // Enable/disable pagination buttons based on current page
                UpdatePaginationButtons();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des appareils: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void FRM_DEVICES_LIST_Load(object sender, EventArgs e)
        {
            try
            {
                // Set row height
                kryptonDataGridView1.RowTemplate.Height = 32;

                // Initialize filter controls
                await InitializeFilterControls();

                // Load devices data
                await LoadDevices();

                // Apply enhanced styling
                DGV_STYLE.DesignDGV(kryptonDataGridView1);

                // Set up context menu
                SetupContextMenu();

                // Set up date pickers
                dtpStartDate.Value = DateTime.Now.AddMonths(-1);
                dtpEndDate.Value = DateTime.Now;

                // Initially hide advanced filters panel
                panelAdvancedSearch.Visible = false;

                // Adjust header group position
                AdjustHeaderGroupPosition();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du formulaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async Task InitializeFilterControls()
        {
            try
            {
                // Load device types
                var deviceTypes = await _cmd.GetDeviceTypesAsync();
                cmbDeviceType.Items.Clear();
                cmbDeviceType.Items.Add("Tous");
                foreach (var type in deviceTypes)
                {
                    cmbDeviceType.Items.Add(type);
                }
                cmbDeviceType.SelectedIndex = 0;

                // Load device brands
                var deviceBrands = await _cmd.GetDeviceBrandsAsync();
                cmbBrand.Items.Clear();
                cmbBrand.Items.Add("Toutes");
                foreach (var brand in deviceBrands)
                {
                    cmbBrand.Items.Add(brand);
                }
                cmbBrand.SelectedIndex = 0;

                // Load customers
                var customers = await _customerCmd.GetALL("", 0, 1000);
                cmbClient.Items.Clear();
                cmbClient.DisplayMember = "name";
                cmbClient.ValueMember = "id";

                // Add a dummy item for "All"
                var allCustomers = new { id = 0, name = "Tous" };
                cmbClient.Items.Add(allCustomers);

                foreach (var customer in customers)
                {
                    cmbClient.Items.Add(customer);
                }
                cmbClient.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'initialisation des filtres: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void AdjustHeaderGroupPosition()
        {
            if (_advancedFiltersVisible)
            {
                kryptonHeaderGroup1.Location = new Point(12, 123);
                kryptonHeaderGroup1.Size = new Size(1109, 526);
            }
            else
            {
                kryptonHeaderGroup1.Location = new Point(12, 43);
                kryptonHeaderGroup1.Size = new Size(1109, 606);
            }
        }

        private void SetupContextMenu()
        {
            try
            {
                // Create a standard context menu strip
                ContextMenuStrip contextMenuStrip = new ContextMenuStrip();

                // Add items to the standard context menu
                ToolStripMenuItem refreshItem = new ToolStripMenuItem("Rafraîchir", Properties.Resources.refresh, (s, e) => { _ = LoadDevices(); });
                ToolStripMenuItem addItem = new ToolStripMenuItem("Ajouter un appareil", Properties.Resources.button_circle_add, BtnAdd_Click);
                ToolStripMenuItem editItem = new ToolStripMenuItem("Modifier", Properties.Resources.tool_pencil, BtnEdit_Click);
                ToolStripMenuItem deleteItem = new ToolStripMenuItem("Supprimer", Properties.Resources.trash, BtnDelete_Click);
                ToolStripSeparator separator = new ToolStripSeparator();
                ToolStripMenuItem printItem = new ToolStripMenuItem("Imprimer", Properties.Resources.tablet_iphone, PrintDataGridView);

                // Add items to the menu
                contextMenuStrip.Items.Add(refreshItem);
                contextMenuStrip.Items.Add(addItem);
                contextMenuStrip.Items.Add(editItem);
                contextMenuStrip.Items.Add(deleteItem);
                contextMenuStrip.Items.Add(separator);
                contextMenuStrip.Items.Add(printItem);

                // Attach the context menu to the DataGridView
                kryptonDataGridView1.ContextMenuStrip = contextMenuStrip;

                // Style the context menu
                contextMenuStrip.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
                contextMenuStrip.ShowImageMargin = true;
                contextMenuStrip.ShowCheckMargin = false;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la configuration du menu contextuel: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void UpdatePaginationButtons()
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);

            // For Krypton ButtonSpecHeaderGroup, we need to use ButtonEnabled enum instead of bool
            btnFirst.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnPrev.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnNext.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
            btnLast.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
        }

        private async void CmbPageView_SelectedIndexChanged(object sender, EventArgs e)
        {
            _currentPage = 1; // Reset to first page when changing page size
            await LoadDevices();
        }

        private async void TXTSearch_TextChanged(object sender, EventArgs e)
        {
            _searchTerm = TXTSearch.Text.Trim();
            _currentPage = 1; // Reset to first page when searching
            await LoadDevices();
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            _searchTerm = TXTSearch.Text.Trim();
            _currentPage = 1; // Reset to first page when searching
            await LoadDevices();
        }

        private async void BtnClearSearch_Click(object sender, EventArgs e)
        {
            TXTSearch.Text = "";
            _searchTerm = "";
            _currentPage = 1; // Reset to first page when clearing search
            await LoadDevices();
        }

        private async void BtnFirst_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage = 1;
                await LoadDevices();
            }
        }

        private async void BtnPrev_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadDevices();
            }
        }

        private async void BtnNext_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage++;
                await LoadDevices();
            }
        }

        private async void BtnLast_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage = totalPages;
                await LoadDevices();
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var frm = new FRM_DEVICE();
            frm.FormClosed += (s, args) =>
            {
                if (frm.HasChanges)
                {
                    _ = LoadDevices();
                }
            };
            frm.ShowDialog();
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (kryptonDataGridView1.CurrentRow != null)
            {
                int deviceId = Convert.ToInt32(kryptonDataGridView1.CurrentRow.Cells["id"].Value);
                var frm = new FRM_DEVICE(deviceId);
                frm.FormClosed += (s, args) =>
                {
                    if (frm.HasChanges)
                    {
                        _ = LoadDevices();
                    }
                };
                frm.ShowDialog();
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un appareil à modifier", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (kryptonDataGridView1.CurrentRow != null)
            {
                int deviceId = Convert.ToInt32(kryptonDataGridView1.CurrentRow.Cells["id"].Value);
                string deviceInfo = $"{kryptonDataGridView1.CurrentRow.Cells["brand"].Value} {kryptonDataGridView1.CurrentRow.Cells["model"].Value}";

                var result = KryptonMessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer l'appareil '{deviceInfo}'?",
                    "Confirmation de suppression",
                    KryptonMessageBoxButtons.YesNo,
                    KryptonMessageBoxIcon.Question
                );

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        bool success = await _cmd.DeleteAsync(deviceId);
                        if (success)
                        {
                            await LoadDevices();
                            KryptonMessageBox.Show("L'appareil a été supprimé avec succès", "Succès",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        }
                        else
                        {
                            KryptonMessageBox.Show("Erreur lors de la suppression de l'appareil", "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        KryptonMessageBox.Show($"Erreur lors de la suppression de l'appareil: {ex.Message}", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un appareil à supprimer", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private void BtnAdvancedSearch_Click(object sender, EventArgs e)
        {
            _advancedFiltersVisible = !_advancedFiltersVisible;
            panelAdvancedSearch.Visible = _advancedFiltersVisible;

            // Update button text
            btnAdvancedSearch.Values.Text = _advancedFiltersVisible ? "Masquer les filtres" : "Filtres avancés";

            // Adjust header group position
            AdjustHeaderGroupPosition();
        }

        private async void BtnApplyFilters_Click(object sender, EventArgs e)
        {
            try
            {
                // Get selected device type
                _selectedDeviceType = cmbDeviceType.SelectedIndex > 0 ? cmbDeviceType.SelectedItem.ToString() : null;

                // Get selected brand
                _selectedBrand = cmbBrand.SelectedIndex > 0 ? cmbBrand.SelectedItem.ToString() : null;

                // Get selected customer
                if (cmbClient.SelectedIndex > 0)
                {
                    dynamic selectedCustomer = cmbClient.SelectedItem;
                    _selectedCustomerId = selectedCustomer.id;
                }
                else
                {
                    _selectedCustomerId = null;
                }

                // Get date range
                _startDate = dtpStartDate.Checked ? dtpStartDate.Value : (DateTime?)null;
                _endDate = dtpEndDate.Checked ? dtpEndDate.Value : (DateTime?)null;

                // Reset to first page
                _currentPage = 1;

                // Reload devices
                await LoadDevices();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'application des filtres: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void BtnResetFilters_Click(object sender, EventArgs e)
        {
            try
            {
                // Reset filter controls
                cmbDeviceType.SelectedIndex = 0;
                cmbBrand.SelectedIndex = 0;
                cmbClient.SelectedIndex = 0;
                dtpStartDate.Value = DateTime.Now.AddMonths(-1);
                dtpEndDate.Value = DateTime.Now;

                // Reset filter values
                _selectedDeviceType = null;
                _selectedBrand = null;
                _selectedCustomerId = null;
                _startDate = null;
                _endDate = null;

                // Reset to first page
                _currentPage = 1;

                // Reload devices
                await LoadDevices();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la réinitialisation des filtres: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void PrintDataGridView(object sender, EventArgs e)
        {
            try
            {
                // Create a simple report design
                string reportXml = @"<?xml version=""1.0"" encoding=""utf-8""?>
<Report ScriptLanguage=""CSharp"" ReportInfo.Created=""01/01/2023 00:00:00"" ReportInfo.Modified=""01/01/2023 00:00:00"" ReportInfo.CreatorVersion=""*******"">
  <Dictionary>
    <TableDataSource Name=""Devices"" ReferenceName=""Devices"">
      <Column Name=""Client"" DataType=""System.String""/>
      <Column Name=""Type"" DataType=""System.String""/>
      <Column Name=""Marque"" DataType=""System.String""/>
      <Column Name=""Modele"" DataType=""System.String""/>
      <Column Name=""DateReception"" DataType=""System.DateTime""/>
    </TableDataSource>
  </Dictionary>
  <ReportPage Name=""Page1"" Watermark.Font=""Arial, 60pt"">
    <ReportTitleBand Name=""ReportTitle1"" Width=""718.2"" Height=""37.8"">
      <TextObject Name=""Title"" Width=""718.2"" Height=""37.8"" Dock=""Fill"" Text=""Liste des Appareils"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 14pt, style=Bold""/>
    </ReportTitleBand>
    <PageHeaderBand Name=""PageHeader1"" Top=""41.8"" Width=""718.2"" Height=""28.35"">
      <TextObject Name=""Header1"" Width=""151.2"" Height=""28.35"" Text=""Client"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
      <TextObject Name=""Header2"" Left=""151.2"" Width=""113.4"" Height=""28.35"" Text=""Type"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
      <TextObject Name=""Header3"" Left=""264.6"" Width=""113.4"" Height=""28.35"" Text=""Marque"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
      <TextObject Name=""Header4"" Left=""378"" Width=""151.2"" Height=""28.35"" Text=""Modèle"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
      <TextObject Name=""Header5"" Left=""529.2"" Width=""189"" Height=""28.35"" Text=""Date de Réception"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
    </PageHeaderBand>
    <DataBand Name=""Data1"" Top=""74.15"" Width=""718.2"" Height=""18.9"" DataSource=""Devices"">
      <TextObject Name=""Data1"" Width=""151.2"" Height=""18.9"" Text=""[Devices.Client]"" VertAlign=""Center"" Font=""Arial, 10pt""/>
      <TextObject Name=""Data2"" Left=""151.2"" Width=""113.4"" Height=""18.9"" Text=""[Devices.Type]"" VertAlign=""Center"" Font=""Arial, 10pt""/>
      <TextObject Name=""Data3"" Left=""264.6"" Width=""113.4"" Height=""18.9"" Text=""[Devices.Marque]"" VertAlign=""Center"" Font=""Arial, 10pt""/>
      <TextObject Name=""Data4"" Left=""378"" Width=""151.2"" Height=""18.9"" Text=""[Devices.Modele]"" VertAlign=""Center"" Font=""Arial, 10pt""/>
      <TextObject Name=""Data5"" Left=""529.2"" Width=""189"" Height=""18.9"" Text=""[Devices.DateReception]"" Format=""Date"" Format.Format=""d"" VertAlign=""Center"" Font=""Arial, 10pt""/>
    </DataBand>
    <PageFooterBand Name=""PageFooter1"" Top=""97.05"" Width=""718.2"" Height=""18.9"">
      <TextObject Name=""Footer"" Width=""718.2"" Height=""18.9"" Dock=""Fill"" Text=""Page [Page] de [TotalPages]"" HorzAlign=""Right"" VertAlign=""Center"" Font=""Arial, 10pt""/>
    </PageFooterBand>
  </ReportPage>
</Report>";

                // Create a new report
                Report report = new Report();
                report.LoadFromString(reportXml);

                // Create a data table for the report
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("Client", typeof(string));
                dataTable.Columns.Add("Type", typeof(string));
                dataTable.Columns.Add("Marque", typeof(string));
                dataTable.Columns.Add("Modele", typeof(string));
                dataTable.Columns.Add("DateReception", typeof(DateTime));

                // Add data from the DataGridView
                foreach (DataGridViewRow row in kryptonDataGridView1.Rows)
                {
                    if (row.Cells["client"].Value != null)
                    {
                        dataTable.Rows.Add(
                            row.Cells["client"].Value.ToString(),
                            row.Cells["type"].Value.ToString(),
                            row.Cells["brand"].Value.ToString(),
                            row.Cells["model"].Value.ToString(),
                            Convert.ToDateTime(row.Cells["date_de_reception"].Value)
                        );
                    }
                }

                // Register the data table
                report.RegisterData(dataTable, "Devices");

                // Show the report
                report.Show();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'impression: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
    }
}
