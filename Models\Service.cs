﻿using System;
using System.ComponentModel;

namespace IRepairIT.Models
{
    public class Service
    {
        [DisplayName("#")]
        public int id { get; set; }

        [DisplayName("Nom")]
        public string name { get; set; }

        [DisplayName("Description")]
        public string description { get; set; }

        [Disp<PERSON><PERSON><PERSON>("Prix")]
        public decimal price { get; set; }

        [Display<PERSON>ame("Durée (minutes)")]
        public int? duration { get; set; } = 0;

        [DisplayName("Date de création")]
        public DateTime created_at { get; set; }

        [DisplayName("Date de mise à jour")]
        public DateTime? updated_at { get; set; }
    }
}
