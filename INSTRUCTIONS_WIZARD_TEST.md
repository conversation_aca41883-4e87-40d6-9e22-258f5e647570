# Instructions de Test - Assistant de Création de Client

## ✅ **PROBLÈME RÉSOLU - Designer Corrigé!**

Le problème de design a été corrigé. Le wizard affiche maintenant correctement:
- ✅ Header avec titre et description de l'étape
- ✅ Zone de contenu principale avec les formulaires
- ✅ Footer avec boutons de navigation et barre de progression
- ✅ Design responsive et professionnel

## 🚀 Comment tester le nouveau wizard

### 1. Compilation du projet
```bash
# Assurez-vous que le projet compile sans erreurs
Build -> Build Solution (Ctrl+Shift+B)
```

### 2. Lancement de l'application
```bash
# Démarrez l'application principale
Debug -> Start Debugging (F5)
```

### 3. Accès au wizard
1. Dans le menu principal, cliquez sur **"Clients"**
2. Sélectionnez **"Nouveau client (Assistant)"**
3. Le wizard s'ouvrira avec 3 étapes

### 4. Test des étapes

#### Étape 1: Informations du Client
**Champs obligatoires à tester:**
- ✅ **Nom**: Sai<PERSON><PERSON>z "Test Client"
- ✅ **Téléphone**: Saisissez "0123456789"
- ⚠️ **Email** (optionnel): Testez avec "<EMAIL>"
- ⚠️ **Adresse** (optionnel): "123 Rue de Test"

**Tests de validation:**
- Essayez de passer à l'étape suivante sans nom → Erreur attendue
- Essayez de passer à l'étape suivante sans téléphone → Erreur attendue
- Testez un email invalide → Erreur attendue
- Testez un téléphone déjà existant → Erreur attendue

#### Étape 2: Informations de l'Appareil
**Champs obligatoires à tester:**
- ✅ **Type**: Sélectionnez "Smartphone"
- ✅ **Marque**: Saisissez "Samsung"
- ✅ **Modèle**: Saisissez "Galaxy S21"
- ✅ **Problème**: Saisissez "Écran cassé"

**Champs optionnels:**
- **N° série**: "ABC123456"
- **IMEI**: "123456789012345"
- **État**: "Bon état général"
- **Mot de passe**: "1234"
- **Accessoires**: "Chargeur, écouteurs"

**Tests de validation:**
- Essayez de passer sans type → Erreur attendue
- Essayez de passer sans marque → Erreur attendue
- Essayez de passer sans modèle → Erreur attendue
- Essayez de passer sans problème → Erreur attendue

#### Étape 3: Ordre de Réparation
**Configuration:**
- **N° commande**: Généré automatiquement
- **Technicien**: Sélectionnez un technicien (si disponible)
- **Garantie**: Laissez "30" jours
- **Notes de réparation**: "Remplacement écran"
- **Notes techniques**: "Vérifier tactile après réparation"

**Test des services:**
1. Cliquez sur **"Ajouter service"**
2. Sélectionnez un service dans la liste
3. Vérifiez que le prix s'ajoute au total

**Test des pièces:**
1. Cliquez sur **"Ajouter pièce"**
2. Sélectionnez une pièce avec quantité
3. Vérifiez que le prix s'ajoute au total

### 5. Finalisation
1. Cliquez sur **"Terminer"**
2. Vérifiez le message de succès
3. Confirmez que toutes les données sont sauvegardées

### 6. Vérification des données
Après création réussie, vérifiez dans l'application:
- **Clients**: Le nouveau client apparaît dans la liste
- **Appareils**: L'appareil est lié au client
- **Ordres de réparation**: L'ordre est créé avec les services et pièces

## 🐛 Tests d'erreurs à effectuer

### Navigation
- ✅ Test bouton "Retour" à chaque étape
- ✅ Test bouton "Annuler" avec confirmation
- ✅ Test fermeture de la fenêtre avec confirmation

### Validation des données
- ✅ Champs obligatoires vides
- ✅ Format email invalide
- ✅ Téléphone/email déjà existant
- ✅ Période de garantie invalide

### Gestion des erreurs
- ✅ Perte de connexion base de données
- ✅ Erreurs de sauvegarde
- ✅ Sélection de services/pièces inexistants

## 📊 Résultats attendus

### Succès
- ✅ Client créé avec ID unique
- ✅ Appareil lié au client
- ✅ Ordre de réparation avec numéro unique
- ✅ Services et pièces associés
- ✅ Calcul correct du total
- ✅ Message de confirmation affiché

### Échec
- ❌ Messages d'erreur clairs
- ❌ Pas de données partielles en base
- ❌ Retour à l'étape problématique
- ❌ Possibilité de corriger et continuer

## 🔧 Dépannage

### Si le wizard ne s'ouvre pas
1. Vérifiez que le menu a été mis à jour
2. Recompilez le projet
3. Vérifiez les références aux formulaires

### Si les validations ne fonctionnent pas
1. Vérifiez la connexion à la base de données
2. Testez les méthodes de validation individuellement
3. Vérifiez les messages d'erreur dans la console

### Si la sauvegarde échoue
1. Vérifiez les permissions de base de données
2. Testez chaque étape de sauvegarde séparément
3. Vérifiez les logs d'erreur
