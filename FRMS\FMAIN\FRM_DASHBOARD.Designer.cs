﻿namespace IRepairIT.FRMS.FMAIN
{
    partial class FRM_DASHBOARD
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FRM_DASHBOARD));
            this.kryptonPanel1 = new Krypton.Toolkit.KryptonPanel();
            this.kryptonTableLayoutPanel3 = new Krypton.Toolkit.KryptonTableLayoutPanel();
            this.kryptonHeaderGroup5 = new Krypton.Toolkit.KryptonHeaderGroup();
            this.kryptonDataGridView2 = new Krypton.Toolkit.KryptonDataGridView();
            this.kryptonHeaderGroup6 = new Krypton.Toolkit.KryptonHeaderGroup();
            this.kryptonDataGridView1 = new Krypton.Toolkit.KryptonDataGridView();
            this.kryptonTableLayoutPanel2 = new Krypton.Toolkit.KryptonTableLayoutPanel();
            this.kryptonHeaderGroup3 = new Krypton.Toolkit.KryptonHeaderGroup();
            this.kryptonHeaderGroup2 = new Krypton.Toolkit.KryptonHeaderGroup();
            this.kryptonLabel13 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel14 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel11 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel12 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonHeaderGroup1 = new Krypton.Toolkit.KryptonHeaderGroup();
            this.kryptonLabel9 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel10 = new Krypton.Toolkit.KryptonLabel();
            this.LblWaitORders = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel8 = new Krypton.Toolkit.KryptonLabel();
            this.LblActiveOrders = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel5 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonPanel6 = new Krypton.Toolkit.KryptonPanel();
            this.kryptonLabel1 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonTableLayoutPanel1 = new Krypton.Toolkit.KryptonTableLayoutPanel();
            this.kryptonPanel5 = new Krypton.Toolkit.KryptonPanel();
            this.pictureBox4 = new System.Windows.Forms.PictureBox();
            this.LblDebts = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel6 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonPanel4 = new Krypton.Toolkit.KryptonPanel();
            this.pictureBox3 = new System.Windows.Forms.PictureBox();
            this.LblOrderCount = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel4 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonPanel3 = new Krypton.Toolkit.KryptonPanel();
            this.pictureBox2 = new System.Windows.Forms.PictureBox();
            this.lblDevicesCount = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel2 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonPanel2 = new Krypton.Toolkit.KryptonPanel();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.lblClientCount = new Krypton.Toolkit.KryptonLabel();
            this.lblTotalTitle = new Krypton.Toolkit.KryptonLabel();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).BeginInit();
            this.kryptonPanel1.SuspendLayout();
            this.kryptonTableLayoutPanel3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup5.Panel)).BeginInit();
            this.kryptonHeaderGroup5.Panel.SuspendLayout();
            this.kryptonHeaderGroup5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup6.Panel)).BeginInit();
            this.kryptonHeaderGroup6.Panel.SuspendLayout();
            this.kryptonHeaderGroup6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView1)).BeginInit();
            this.kryptonTableLayoutPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup3.Panel)).BeginInit();
            this.kryptonHeaderGroup3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup2.Panel)).BeginInit();
            this.kryptonHeaderGroup2.Panel.SuspendLayout();
            this.kryptonHeaderGroup2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1.Panel)).BeginInit();
            this.kryptonHeaderGroup1.Panel.SuspendLayout();
            this.kryptonHeaderGroup1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel6)).BeginInit();
            this.kryptonTableLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel5)).BeginInit();
            this.kryptonPanel5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel4)).BeginInit();
            this.kryptonPanel4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel3)).BeginInit();
            this.kryptonPanel3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel2)).BeginInit();
            this.kryptonPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.SuspendLayout();
            // 
            // kryptonPanel1
            // 
            this.kryptonPanel1.Controls.Add(this.kryptonTableLayoutPanel3);
            this.kryptonPanel1.Controls.Add(this.kryptonTableLayoutPanel2);
            this.kryptonPanel1.Controls.Add(this.kryptonPanel6);
            this.kryptonPanel1.Controls.Add(this.kryptonLabel1);
            this.kryptonPanel1.Controls.Add(this.kryptonTableLayoutPanel1);
            this.kryptonPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel1.Location = new System.Drawing.Point(0, 0);
            this.kryptonPanel1.Name = "kryptonPanel1";
            this.kryptonPanel1.Size = new System.Drawing.Size(808, 450);
            this.kryptonPanel1.TabIndex = 0;
            // 
            // kryptonTableLayoutPanel3
            // 
            this.kryptonTableLayoutPanel3.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonTableLayoutPanel3.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("kryptonTableLayoutPanel3.BackgroundImage")));
            this.kryptonTableLayoutPanel3.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.kryptonTableLayoutPanel3.ColumnCount = 2;
            this.kryptonTableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel3.Controls.Add(this.kryptonHeaderGroup5, 1, 0);
            this.kryptonTableLayoutPanel3.Controls.Add(this.kryptonHeaderGroup6, 0, 0);
            this.kryptonTableLayoutPanel3.Location = new System.Drawing.Point(12, 299);
            this.kryptonTableLayoutPanel3.Name = "kryptonTableLayoutPanel3";
            this.kryptonTableLayoutPanel3.RowCount = 1;
            this.kryptonTableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.kryptonTableLayoutPanel3.Size = new System.Drawing.Size(784, 139);
            this.kryptonTableLayoutPanel3.TabIndex = 10;
            // 
            // kryptonHeaderGroup5
            // 
            this.kryptonHeaderGroup5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonHeaderGroup5.HeaderStylePrimary = Krypton.Toolkit.HeaderStyle.DockInactive;
            this.kryptonHeaderGroup5.HeaderVisibleSecondary = false;
            this.kryptonHeaderGroup5.Location = new System.Drawing.Point(395, 3);
            this.kryptonHeaderGroup5.Name = "kryptonHeaderGroup5";
            // 
            // kryptonHeaderGroup5.Panel
            // 
            this.kryptonHeaderGroup5.Panel.Controls.Add(this.kryptonDataGridView2);
            this.kryptonHeaderGroup5.Size = new System.Drawing.Size(386, 133);
            this.kryptonHeaderGroup5.StateCommon.HeaderPrimary.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold);
            this.kryptonHeaderGroup5.TabIndex = 1;
            this.kryptonHeaderGroup5.ValuesPrimary.Heading = "Clients (latest)";
            // 
            // kryptonDataGridView2
            // 
            this.kryptonDataGridView2.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.kryptonDataGridView2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonDataGridView2.Location = new System.Drawing.Point(0, 0);
            this.kryptonDataGridView2.Name = "kryptonDataGridView2";
            this.kryptonDataGridView2.Size = new System.Drawing.Size(384, 105);
            this.kryptonDataGridView2.TabIndex = 1;
            // 
            // kryptonHeaderGroup6
            // 
            this.kryptonHeaderGroup6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonHeaderGroup6.HeaderStylePrimary = Krypton.Toolkit.HeaderStyle.DockInactive;
            this.kryptonHeaderGroup6.HeaderVisibleSecondary = false;
            this.kryptonHeaderGroup6.Location = new System.Drawing.Point(3, 3);
            this.kryptonHeaderGroup6.Name = "kryptonHeaderGroup6";
            // 
            // kryptonHeaderGroup6.Panel
            // 
            this.kryptonHeaderGroup6.Panel.Controls.Add(this.kryptonDataGridView1);
            this.kryptonHeaderGroup6.Size = new System.Drawing.Size(386, 133);
            this.kryptonHeaderGroup6.StateCommon.HeaderPrimary.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold);
            this.kryptonHeaderGroup6.TabIndex = 0;
            this.kryptonHeaderGroup6.ValuesPrimary.Heading = "Ordres de réparation (latest)";
            // 
            // kryptonDataGridView1
            // 
            this.kryptonDataGridView1.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.kryptonDataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonDataGridView1.Location = new System.Drawing.Point(0, 0);
            this.kryptonDataGridView1.Name = "kryptonDataGridView1";
            this.kryptonDataGridView1.Size = new System.Drawing.Size(384, 105);
            this.kryptonDataGridView1.TabIndex = 0;
            // 
            // kryptonTableLayoutPanel2
            // 
            this.kryptonTableLayoutPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonTableLayoutPanel2.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("kryptonTableLayoutPanel2.BackgroundImage")));
            this.kryptonTableLayoutPanel2.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.kryptonTableLayoutPanel2.ColumnCount = 3;
            this.kryptonTableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel2.Controls.Add(this.kryptonHeaderGroup3, 2, 0);
            this.kryptonTableLayoutPanel2.Controls.Add(this.kryptonHeaderGroup2, 1, 0);
            this.kryptonTableLayoutPanel2.Controls.Add(this.kryptonHeaderGroup1, 0, 0);
            this.kryptonTableLayoutPanel2.Location = new System.Drawing.Point(12, 186);
            this.kryptonTableLayoutPanel2.Name = "kryptonTableLayoutPanel2";
            this.kryptonTableLayoutPanel2.RowCount = 1;
            this.kryptonTableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.kryptonTableLayoutPanel2.Size = new System.Drawing.Size(784, 110);
            this.kryptonTableLayoutPanel2.TabIndex = 8;
            // 
            // kryptonHeaderGroup3
            // 
            this.kryptonHeaderGroup3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonHeaderGroup3.HeaderVisibleSecondary = false;
            this.kryptonHeaderGroup3.Location = new System.Drawing.Point(525, 3);
            this.kryptonHeaderGroup3.Name = "kryptonHeaderGroup3";
            this.kryptonHeaderGroup3.Size = new System.Drawing.Size(256, 104);
            this.kryptonHeaderGroup3.StateCommon.HeaderPrimary.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold);
            this.kryptonHeaderGroup3.TabIndex = 2;
            this.kryptonHeaderGroup3.ValuesPrimary.Heading = " Inventaire";
            // 
            // kryptonHeaderGroup2
            // 
            this.kryptonHeaderGroup2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonHeaderGroup2.HeaderVisibleSecondary = false;
            this.kryptonHeaderGroup2.Location = new System.Drawing.Point(264, 3);
            this.kryptonHeaderGroup2.Name = "kryptonHeaderGroup2";
            // 
            // kryptonHeaderGroup2.Panel
            // 
            this.kryptonHeaderGroup2.Panel.Controls.Add(this.kryptonLabel13);
            this.kryptonHeaderGroup2.Panel.Controls.Add(this.kryptonLabel14);
            this.kryptonHeaderGroup2.Panel.Controls.Add(this.kryptonLabel11);
            this.kryptonHeaderGroup2.Panel.Controls.Add(this.kryptonLabel12);
            this.kryptonHeaderGroup2.Size = new System.Drawing.Size(255, 104);
            this.kryptonHeaderGroup2.StateCommon.HeaderPrimary.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold);
            this.kryptonHeaderGroup2.TabIndex = 1;
            this.kryptonHeaderGroup2.ValuesPrimary.Heading = "sales";
            // 
            // kryptonLabel13
            // 
            this.kryptonLabel13.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.kryptonLabel13.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.kryptonLabel13.Location = new System.Drawing.Point(130, 12);
            this.kryptonLabel13.Name = "kryptonLabel13";
            this.kryptonLabel13.Size = new System.Drawing.Size(73, 26);
            this.kryptonLabel13.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.kryptonLabel13.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel13.TabIndex = 10;
            this.kryptonLabel13.Values.Text = "0.00 DA";
            // 
            // kryptonLabel14
            // 
            this.kryptonLabel14.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.kryptonLabel14.Location = new System.Drawing.Point(10, 12);
            this.kryptonLabel14.Name = "kryptonLabel14";
            this.kryptonLabel14.Size = new System.Drawing.Size(73, 26);
            this.kryptonLabel14.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.kryptonLabel14.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel14.TabIndex = 9;
            this.kryptonLabel14.Values.Text = "0.00 DA";
            // 
            // kryptonLabel11
            // 
            this.kryptonLabel11.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.kryptonLabel11.Location = new System.Drawing.Point(130, 46);
            this.kryptonLabel11.Name = "kryptonLabel11";
            this.kryptonLabel11.Size = new System.Drawing.Size(96, 22);
            this.kryptonLabel11.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.kryptonLabel11.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel11.TabIndex = 8;
            this.kryptonLabel11.Values.Text = "Montant payé";
            // 
            // kryptonLabel12
            // 
            this.kryptonLabel12.Location = new System.Drawing.Point(10, 46);
            this.kryptonLabel12.Name = "kryptonLabel12";
            this.kryptonLabel12.Size = new System.Drawing.Size(71, 22);
            this.kryptonLabel12.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.kryptonLabel12.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel12.TabIndex = 7;
            this.kryptonLabel12.Values.Text = "Coût total";
            // 
            // kryptonHeaderGroup1
            // 
            this.kryptonHeaderGroup1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonHeaderGroup1.HeaderVisibleSecondary = false;
            this.kryptonHeaderGroup1.Location = new System.Drawing.Point(3, 3);
            this.kryptonHeaderGroup1.Name = "kryptonHeaderGroup1";
            // 
            // kryptonHeaderGroup1.Panel
            // 
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.kryptonLabel9);
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.kryptonLabel10);
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.LblWaitORders);
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.kryptonLabel8);
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.LblActiveOrders);
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.kryptonLabel5);
            this.kryptonHeaderGroup1.Size = new System.Drawing.Size(255, 104);
            this.kryptonHeaderGroup1.StateCommon.HeaderPrimary.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold);
            this.kryptonHeaderGroup1.TabIndex = 0;
            this.kryptonHeaderGroup1.ValuesPrimary.Heading = " Statut de la commande";
            // 
            // kryptonLabel9
            // 
            this.kryptonLabel9.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonLabel9.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.kryptonLabel9.Location = new System.Drawing.Point(199, 8);
            this.kryptonLabel9.Name = "kryptonLabel9";
            this.kryptonLabel9.Size = new System.Drawing.Size(24, 30);
            this.kryptonLabel9.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold);
            this.kryptonLabel9.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel9.TabIndex = 9;
            this.kryptonLabel9.Values.Text = "0";
            // 
            // kryptonLabel10
            // 
            this.kryptonLabel10.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonLabel10.Location = new System.Drawing.Point(183, 46);
            this.kryptonLabel10.Name = "kryptonLabel10";
            this.kryptonLabel10.Size = new System.Drawing.Size(54, 22);
            this.kryptonLabel10.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.kryptonLabel10.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel10.TabIndex = 8;
            this.kryptonLabel10.Values.Text = "Annulé";
            // 
            // LblWaitORders
            // 
            this.LblWaitORders.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.LblWaitORders.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.LblWaitORders.Location = new System.Drawing.Point(114, 8);
            this.LblWaitORders.Name = "LblWaitORders";
            this.LblWaitORders.Size = new System.Drawing.Size(24, 30);
            this.LblWaitORders.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold);
            this.LblWaitORders.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.LblWaitORders.TabIndex = 7;
            this.LblWaitORders.Values.Text = "0";
            // 
            // kryptonLabel8
            // 
            this.kryptonLabel8.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.kryptonLabel8.Location = new System.Drawing.Point(100, 46);
            this.kryptonLabel8.Name = "kryptonLabel8";
            this.kryptonLabel8.Size = new System.Drawing.Size(53, 22);
            this.kryptonLabel8.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.kryptonLabel8.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel8.TabIndex = 6;
            this.kryptonLabel8.Values.Text = "Réparé";
            // 
            // LblActiveOrders
            // 
            this.LblActiveOrders.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.LblActiveOrders.Location = new System.Drawing.Point(29, 8);
            this.LblActiveOrders.Name = "LblActiveOrders";
            this.LblActiveOrders.Size = new System.Drawing.Size(24, 30);
            this.LblActiveOrders.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold);
            this.LblActiveOrders.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.LblActiveOrders.TabIndex = 5;
            this.LblActiveOrders.Values.Text = "0";
            // 
            // kryptonLabel5
            // 
            this.kryptonLabel5.Location = new System.Drawing.Point(15, 44);
            this.kryptonLabel5.Name = "kryptonLabel5";
            this.kryptonLabel5.Size = new System.Drawing.Size(62, 22);
            this.kryptonLabel5.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.kryptonLabel5.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel5.TabIndex = 4;
            this.kryptonLabel5.Values.Text = "En cours";
            // 
            // kryptonPanel6
            // 
            this.kryptonPanel6.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonPanel6.Location = new System.Drawing.Point(12, 50);
            this.kryptonPanel6.Name = "kryptonPanel6";
            this.kryptonPanel6.Size = new System.Drawing.Size(784, 2);
            this.kryptonPanel6.StateCommon.Color1 = System.Drawing.Color.Silver;
            this.kryptonPanel6.TabIndex = 6;
            // 
            // kryptonLabel1
            // 
            this.kryptonLabel1.Location = new System.Drawing.Point(12, 12);
            this.kryptonLabel1.Name = "kryptonLabel1";
            this.kryptonLabel1.Size = new System.Drawing.Size(100, 20);
            this.kryptonLabel1.TabIndex = 5;
            this.kryptonLabel1.Values.Text = "Tableau de bord";
            // 
            // kryptonTableLayoutPanel1
            // 
            this.kryptonTableLayoutPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonTableLayoutPanel1.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("kryptonTableLayoutPanel1.BackgroundImage")));
            this.kryptonTableLayoutPanel1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.kryptonTableLayoutPanel1.ColumnCount = 4;
            this.kryptonTableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.kryptonTableLayoutPanel1.Controls.Add(this.kryptonPanel5, 3, 0);
            this.kryptonTableLayoutPanel1.Controls.Add(this.kryptonPanel4, 2, 0);
            this.kryptonTableLayoutPanel1.Controls.Add(this.kryptonPanel3, 1, 0);
            this.kryptonTableLayoutPanel1.Controls.Add(this.kryptonPanel2, 0, 0);
            this.kryptonTableLayoutPanel1.Location = new System.Drawing.Point(12, 68);
            this.kryptonTableLayoutPanel1.Name = "kryptonTableLayoutPanel1";
            this.kryptonTableLayoutPanel1.RowCount = 1;
            this.kryptonTableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.kryptonTableLayoutPanel1.Size = new System.Drawing.Size(784, 115);
            this.kryptonTableLayoutPanel1.TabIndex = 3;
            // 
            // kryptonPanel5
            // 
            this.kryptonPanel5.Controls.Add(this.pictureBox4);
            this.kryptonPanel5.Controls.Add(this.LblDebts);
            this.kryptonPanel5.Controls.Add(this.kryptonLabel6);
            this.kryptonPanel5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel5.Location = new System.Drawing.Point(591, 3);
            this.kryptonPanel5.Name = "kryptonPanel5";
            this.kryptonPanel5.Size = new System.Drawing.Size(190, 109);
            this.kryptonPanel5.StateNormal.Color2 = System.Drawing.Color.Gray;
            this.kryptonPanel5.StateNormal.ColorStyle = Krypton.Toolkit.PaletteColorStyle.SolidAllLine;
            this.kryptonPanel5.TabIndex = 3;
            // 
            // pictureBox4
            // 
            this.pictureBox4.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox4.BackColor = System.Drawing.Color.Transparent;
            this.pictureBox4.Image = global::IRepairIT.Properties.Resources.give_money_2;
            this.pictureBox4.Location = new System.Drawing.Point(76, 5);
            this.pictureBox4.Name = "pictureBox4";
            this.pictureBox4.Size = new System.Drawing.Size(38, 35);
            this.pictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.pictureBox4.TabIndex = 6;
            this.pictureBox4.TabStop = false;
            // 
            // LblDebts
            // 
            this.LblDebts.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.LblDebts.AutoSize = false;
            this.LblDebts.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.LblDebts.Location = new System.Drawing.Point(3, 74);
            this.LblDebts.Name = "LblDebts";
            this.LblDebts.Size = new System.Drawing.Size(184, 30);
            this.LblDebts.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold);
            this.LblDebts.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.LblDebts.TabIndex = 3;
            this.LblDebts.Values.Text = "0";
            // 
            // kryptonLabel6
            // 
            this.kryptonLabel6.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.kryptonLabel6.Location = new System.Drawing.Point(70, 46);
            this.kryptonLabel6.Name = "kryptonLabel6";
            this.kryptonLabel6.Size = new System.Drawing.Size(50, 22);
            this.kryptonLabel6.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.kryptonLabel6.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel6.TabIndex = 2;
            this.kryptonLabel6.Values.Text = "Dettes";
            // 
            // kryptonPanel4
            // 
            this.kryptonPanel4.Controls.Add(this.pictureBox3);
            this.kryptonPanel4.Controls.Add(this.LblOrderCount);
            this.kryptonPanel4.Controls.Add(this.kryptonLabel4);
            this.kryptonPanel4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel4.Location = new System.Drawing.Point(395, 3);
            this.kryptonPanel4.Name = "kryptonPanel4";
            this.kryptonPanel4.Size = new System.Drawing.Size(190, 109);
            this.kryptonPanel4.StateNormal.Color2 = System.Drawing.Color.Gray;
            this.kryptonPanel4.StateNormal.ColorStyle = Krypton.Toolkit.PaletteColorStyle.SolidAllLine;
            this.kryptonPanel4.TabIndex = 2;
            // 
            // pictureBox3
            // 
            this.pictureBox3.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox3.BackColor = System.Drawing.Color.Transparent;
            this.pictureBox3.Image = global::IRepairIT.Properties.Resources.tools;
            this.pictureBox3.Location = new System.Drawing.Point(76, 5);
            this.pictureBox3.Name = "pictureBox3";
            this.pictureBox3.Size = new System.Drawing.Size(38, 35);
            this.pictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.pictureBox3.TabIndex = 6;
            this.pictureBox3.TabStop = false;
            // 
            // LblOrderCount
            // 
            this.LblOrderCount.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.LblOrderCount.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.LblOrderCount.Location = new System.Drawing.Point(83, 74);
            this.LblOrderCount.Name = "LblOrderCount";
            this.LblOrderCount.Size = new System.Drawing.Size(24, 30);
            this.LblOrderCount.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold);
            this.LblOrderCount.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.LblOrderCount.TabIndex = 3;
            this.LblOrderCount.Values.Text = "0";
            // 
            // kryptonLabel4
            // 
            this.kryptonLabel4.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.kryptonLabel4.Location = new System.Drawing.Point(27, 46);
            this.kryptonLabel4.Name = "kryptonLabel4";
            this.kryptonLabel4.Size = new System.Drawing.Size(137, 22);
            this.kryptonLabel4.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.kryptonLabel4.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel4.TabIndex = 2;
            this.kryptonLabel4.Values.Text = "Ordres de réparation";
            // 
            // kryptonPanel3
            // 
            this.kryptonPanel3.Controls.Add(this.pictureBox2);
            this.kryptonPanel3.Controls.Add(this.lblDevicesCount);
            this.kryptonPanel3.Controls.Add(this.kryptonLabel2);
            this.kryptonPanel3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel3.Location = new System.Drawing.Point(199, 3);
            this.kryptonPanel3.Name = "kryptonPanel3";
            this.kryptonPanel3.Size = new System.Drawing.Size(190, 109);
            this.kryptonPanel3.StateNormal.Color2 = System.Drawing.Color.Gray;
            this.kryptonPanel3.StateNormal.ColorStyle = Krypton.Toolkit.PaletteColorStyle.SolidAllLine;
            this.kryptonPanel3.TabIndex = 1;
            // 
            // pictureBox2
            // 
            this.pictureBox2.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox2.BackColor = System.Drawing.Color.Transparent;
            this.pictureBox2.Image = global::IRepairIT.Properties.Resources.tablet_iphone;
            this.pictureBox2.Location = new System.Drawing.Point(76, 5);
            this.pictureBox2.Name = "pictureBox2";
            this.pictureBox2.Size = new System.Drawing.Size(38, 35);
            this.pictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.pictureBox2.TabIndex = 5;
            this.pictureBox2.TabStop = false;
            // 
            // lblDevicesCount
            // 
            this.lblDevicesCount.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblDevicesCount.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.lblDevicesCount.Location = new System.Drawing.Point(83, 74);
            this.lblDevicesCount.Name = "lblDevicesCount";
            this.lblDevicesCount.Size = new System.Drawing.Size(24, 30);
            this.lblDevicesCount.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold);
            this.lblDevicesCount.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.lblDevicesCount.TabIndex = 3;
            this.lblDevicesCount.Values.Text = "0";
            // 
            // kryptonLabel2
            // 
            this.kryptonLabel2.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.kryptonLabel2.Location = new System.Drawing.Point(65, 46);
            this.kryptonLabel2.Name = "kryptonLabel2";
            this.kryptonLabel2.Size = new System.Drawing.Size(67, 22);
            this.kryptonLabel2.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.kryptonLabel2.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.kryptonLabel2.TabIndex = 2;
            this.kryptonLabel2.Values.Text = "Appareils";
            // 
            // kryptonPanel2
            // 
            this.kryptonPanel2.Controls.Add(this.pictureBox1);
            this.kryptonPanel2.Controls.Add(this.lblClientCount);
            this.kryptonPanel2.Controls.Add(this.lblTotalTitle);
            this.kryptonPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel2.Location = new System.Drawing.Point(3, 3);
            this.kryptonPanel2.Name = "kryptonPanel2";
            this.kryptonPanel2.Size = new System.Drawing.Size(190, 109);
            this.kryptonPanel2.StateNormal.Color2 = System.Drawing.Color.Gray;
            this.kryptonPanel2.StateNormal.ColorStyle = Krypton.Toolkit.PaletteColorStyle.SolidAllLine;
            this.kryptonPanel2.TabIndex = 0;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox1.BackColor = System.Drawing.Color.Transparent;
            this.pictureBox1.Image = global::IRepairIT.Properties.Resources.people_customers;
            this.pictureBox1.Location = new System.Drawing.Point(76, 5);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(38, 35);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.pictureBox1.TabIndex = 4;
            this.pictureBox1.TabStop = false;
            // 
            // lblClientCount
            // 
            this.lblClientCount.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblClientCount.LabelStyle = Krypton.Toolkit.LabelStyle.BoldPanel;
            this.lblClientCount.Location = new System.Drawing.Point(83, 74);
            this.lblClientCount.Name = "lblClientCount";
            this.lblClientCount.Size = new System.Drawing.Size(24, 30);
            this.lblClientCount.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold);
            this.lblClientCount.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.lblClientCount.TabIndex = 3;
            this.lblClientCount.Values.Text = "0";
            // 
            // lblTotalTitle
            // 
            this.lblTotalTitle.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblTotalTitle.Location = new System.Drawing.Point(69, 46);
            this.lblTotalTitle.Name = "lblTotalTitle";
            this.lblTotalTitle.Size = new System.Drawing.Size(52, 22);
            this.lblTotalTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.lblTotalTitle.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.lblTotalTitle.TabIndex = 2;
            this.lblTotalTitle.Values.Text = "Clients";
            // 
            // FRM_DASHBOARD
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(808, 450);
            this.Controls.Add(this.kryptonPanel1);
            this.Name = "FRM_DASHBOARD";
            this.Text = "Tableau de bord";
            this.Load += new System.EventHandler(this.FRM_DASHBOARD_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).EndInit();
            this.kryptonPanel1.ResumeLayout(false);
            this.kryptonPanel1.PerformLayout();
            this.kryptonTableLayoutPanel3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup5.Panel)).EndInit();
            this.kryptonHeaderGroup5.Panel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup5)).EndInit();
            this.kryptonHeaderGroup5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup6.Panel)).EndInit();
            this.kryptonHeaderGroup6.Panel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup6)).EndInit();
            this.kryptonHeaderGroup6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView1)).EndInit();
            this.kryptonTableLayoutPanel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup3.Panel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup3)).EndInit();
            this.kryptonHeaderGroup3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup2.Panel)).EndInit();
            this.kryptonHeaderGroup2.Panel.ResumeLayout(false);
            this.kryptonHeaderGroup2.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup2)).EndInit();
            this.kryptonHeaderGroup2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1.Panel)).EndInit();
            this.kryptonHeaderGroup1.Panel.ResumeLayout(false);
            this.kryptonHeaderGroup1.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1)).EndInit();
            this.kryptonHeaderGroup1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel6)).EndInit();
            this.kryptonTableLayoutPanel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel5)).EndInit();
            this.kryptonPanel5.ResumeLayout(false);
            this.kryptonPanel5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel4)).EndInit();
            this.kryptonPanel4.ResumeLayout(false);
            this.kryptonPanel4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel3)).EndInit();
            this.kryptonPanel3.ResumeLayout(false);
            this.kryptonPanel3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel2)).EndInit();
            this.kryptonPanel2.ResumeLayout(false);
            this.kryptonPanel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonPanel kryptonPanel1;
        private Krypton.Toolkit.KryptonTableLayoutPanel kryptonTableLayoutPanel1;
        private Krypton.Toolkit.KryptonPanel kryptonPanel5;
        private Krypton.Toolkit.KryptonLabel LblDebts;
        private Krypton.Toolkit.KryptonLabel kryptonLabel6;
        private Krypton.Toolkit.KryptonPanel kryptonPanel4;
        private Krypton.Toolkit.KryptonLabel LblOrderCount;
        private Krypton.Toolkit.KryptonLabel kryptonLabel4;
        private Krypton.Toolkit.KryptonPanel kryptonPanel3;
        private Krypton.Toolkit.KryptonLabel lblDevicesCount;
        private Krypton.Toolkit.KryptonLabel kryptonLabel2;
        private Krypton.Toolkit.KryptonPanel kryptonPanel2;
        private Krypton.Toolkit.KryptonLabel lblClientCount;
        private Krypton.Toolkit.KryptonLabel lblTotalTitle;
        private Krypton.Toolkit.KryptonPanel kryptonPanel6;
        private Krypton.Toolkit.KryptonLabel kryptonLabel1;
        private Krypton.Toolkit.KryptonTableLayoutPanel kryptonTableLayoutPanel2;
        private Krypton.Toolkit.KryptonHeaderGroup kryptonHeaderGroup3;
        private Krypton.Toolkit.KryptonHeaderGroup kryptonHeaderGroup2;
        private Krypton.Toolkit.KryptonHeaderGroup kryptonHeaderGroup1;
        private Krypton.Toolkit.KryptonLabel LblActiveOrders;
        private Krypton.Toolkit.KryptonLabel kryptonLabel5;
        private Krypton.Toolkit.KryptonLabel kryptonLabel13;
        private Krypton.Toolkit.KryptonLabel kryptonLabel14;
        private Krypton.Toolkit.KryptonLabel kryptonLabel11;
        private Krypton.Toolkit.KryptonLabel kryptonLabel12;
        private Krypton.Toolkit.KryptonTableLayoutPanel kryptonTableLayoutPanel3;
        private Krypton.Toolkit.KryptonHeaderGroup kryptonHeaderGroup5;
        private Krypton.Toolkit.KryptonHeaderGroup kryptonHeaderGroup6;
        private Krypton.Toolkit.KryptonDataGridView kryptonDataGridView2;
        private Krypton.Toolkit.KryptonDataGridView kryptonDataGridView1;
        private Krypton.Toolkit.KryptonLabel kryptonLabel9;
        private Krypton.Toolkit.KryptonLabel kryptonLabel10;
        private Krypton.Toolkit.KryptonLabel LblWaitORders;
        private Krypton.Toolkit.KryptonLabel kryptonLabel8;
        private System.Windows.Forms.PictureBox pictureBox4;
        private System.Windows.Forms.PictureBox pictureBox3;
        private System.Windows.Forms.PictureBox pictureBox2;
        private System.Windows.Forms.PictureBox pictureBox1;
    }
}