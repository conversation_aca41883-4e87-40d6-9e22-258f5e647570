using IRepairIT.Data.Common;
using IRepairIT.Enums;
using IRepairIT.Models;
using IRepairIT.FRMS.FCOMMON;
using IRepairIT.FRMS.FCUSTOMERS;
using IRepairIT.FRMS.FDEVICES;
using IRepairIT.FRMS.FSERVICES;
using IRepairIT.FRMS.FINVENTORY;
using Krypton.Toolkit;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Dapper;
using FastReport;
using MySql.Data.MySqlClient;

namespace IRepairIT.FRMS.FREPAIRS
{
    public partial class FRM_REPAIR_ORDER : KryptonForm
    {
        private readonly RepairOrderCommands _cmd;
        private readonly CustomerCommands _customerCmd;
        private readonly DeviceCommands _deviceCmd;
        private readonly UserCommands _userCmd;
        private readonly ServiceCommands _serviceCmd;
        private readonly InventoryCommands _inventoryCmd;
        private readonly DebtCommands _debtCmd;

        private int _repairOrderId;
        private int _customerId;
        private int _deviceId;
        private RepairOrder _repairOrder;
        private List<TempService> _tempServices;
        private List<TempPart> _tempParts;

        public bool HasChanges { get; private set; }

        // Constructor for new repair order
        public FRM_REPAIR_ORDER()
        {
            InitializeComponent();

            _cmd = new RepairOrderCommands();
            _customerCmd = new CustomerCommands();
            _deviceCmd = new DeviceCommands();
            _userCmd = new UserCommands();
            _serviceCmd = new ServiceCommands();
            _inventoryCmd = new InventoryCommands();
            _debtCmd = new DebtCommands();

            _repairOrderId = 0;
            _customerId = 0;
            _deviceId = 0;
            _repairOrder = new RepairOrder();
            HasChanges = false;
        }

        // Constructor for existing repair order
        public FRM_REPAIR_ORDER(int repairOrderId)
        {
            InitializeComponent();

            _cmd = new RepairOrderCommands();
            _customerCmd = new CustomerCommands();
            _deviceCmd = new DeviceCommands();
            _userCmd = new UserCommands();
            _serviceCmd = new ServiceCommands();
            _inventoryCmd = new InventoryCommands();
            _debtCmd = new DebtCommands();

            _repairOrderId = repairOrderId;
            _customerId = 0;
            _deviceId = 0;
            _repairOrder = new RepairOrder();
            HasChanges = false;
        }

        private async void FRM_REPAIR_ORDER_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize temporary lists
                _tempServices = new List<TempService>();
                _tempParts = new List<TempPart>();

                // Initialize status flow panel
                InitializeStatusPanel();

                // Load technicians
                await LoadTechnicians();

                // Configure DataGridViews
                ConfigureDataGridViews();

                // Initialize payment methods
                InitializePaymentMethods();

                if (_repairOrderId > 0)
                {
                    // Load existing repair order
                    await LoadRepairOrder();
                    this.Text = $"Modifier l'ordre de réparation #{_repairOrder.OrderNumber}";

                    // Load services and parts
                    await LoadServices();
                    await LoadParts();
                }
                else
                {
                    // New repair order
                    this.Text = "Nouvel ordre de réparation";

                    // Set default status
                    _repairOrder.Status = RepairOrderStatus.received.ToString();
                    try
                    {
                        UpdateStatusButtons(RepairOrderStatus.received);
                    }
                    catch (Exception ex)
                    {
                        // Log the error but continue loading the form
                        System.Diagnostics.Debug.WriteLine($"Error updating status buttons: {ex.Message}");
                    }

                    // Generate order number
                    string orderNumber = await _cmd.GenerateOrderNumber();
                    txtOrderNumber.Text = orderNumber;

                    // Set current date
                    dtpOrderDate.Value = DateTime.Now;

                    // Set default warranty period
                    txtWarrantyPeriod.Text = "30";

                    // Initialize empty grids
                    await LoadTempServices();
                    await LoadTempParts();
                }

                // Update total
                UpdateTotalLabel();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du formulaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void InitializeStatusPanel()
        {
            flpStatus.Controls.Clear();

            // Create status buttons for each status
            foreach (RepairOrderStatus status in Enum.GetValues(typeof(RepairOrderStatus)))
            {
                KryptonButton statusButton = new KryptonButton
                {
                    Text = GetStatusDisplayName(status),
                    Tag = status.ToString(),
                    Size = new Size(120, 30),
                    Enabled = true
                };

                // Set button appearance based on status
                switch (status)
                {
                    case RepairOrderStatus.received:
                        statusButton.StateCommon.Back.Color1 = Color.LightBlue;
                        break;
                    case RepairOrderStatus.in_progress:
                        statusButton.StateCommon.Back.Color1 = Color.Yellow;
                        break;
                    case RepairOrderStatus.waiting_parts:
                        statusButton.StateCommon.Back.Color1 = Color.Orange;
                        break;
                    case RepairOrderStatus.ready:
                        statusButton.StateCommon.Back.Color1 = Color.LightGreen;
                        break;
                    case RepairOrderStatus.delivered:
                        statusButton.StateCommon.Back.Color1 = Color.Green;
                        break;
                    case RepairOrderStatus.cancelled:
                        statusButton.StateCommon.Back.Color1 = Color.Red;
                        break;
                }

                // Add click event handler
                statusButton.Click += StatusButton_Click;

                // Add to flow panel
                flpStatus.Controls.Add(statusButton);
            }
        }

        private void StatusButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (sender is KryptonButton button)
                {
                    string status = button.Tag.ToString();
                    _repairOrder.Status = status;
                    UpdateStatusButtons((RepairOrderStatus)Enum.Parse(typeof(RepairOrderStatus), status));
                    HasChanges = true;
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du changement de statut: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void UpdateStatusButtons(RepairOrderStatus currentStatus)
        {
            foreach (Control control in flpStatus.Controls)
            {
                if (control is KryptonButton button)
                {
                    string buttonStatus = button.Tag.ToString();
                    bool isCurrentStatus = buttonStatus == currentStatus.ToString();

                    // Highlight current status button

                    // Add border to current status
                    button.StateCommon.Border.Width = isCurrentStatus ? 2 : 1;
                    button.StateCommon.Border.Color1 = isCurrentStatus ? Color.Black : Color.Gray;
                }
            }
        }

        private string GetStatusDisplayName(RepairOrderStatus status)
        {
            switch (status)
            {
                case RepairOrderStatus.received:
                    return "Reçu";
                case RepairOrderStatus.in_progress:
                    return "En cours";
                case RepairOrderStatus.waiting_parts:
                    return "En attente de pièces";
                case RepairOrderStatus.ready:
                    return "Prêt";
                case RepairOrderStatus.delivered:
                    return "Livré";
                case RepairOrderStatus.cancelled:
                    return "Annulé";
                default:
                    return status.ToString();
            }
        }

        private async Task LoadTechnicians()
        {
            try
            {
                // Get all users with role = technician
                var users = await _userCmd.GetAllAsync();
                var technicians = users.Where(u => u.Role == UserRoles.technician && u.Status == UserStatus.active).ToList();

                cmbTechnician.DataSource = technicians;
                cmbTechnician.DisplayMember = "Full_Name";
                cmbTechnician.ValueMember = "Id";

                // Add empty item
                if (technicians.Count() > 0)
                {
                    cmbTechnician.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des techniciens: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void InitializePaymentMethods()
        {
            cmbPaymentMethod.Items.Clear();
            cmbPaymentMethod.Items.AddRange(new object[] { "Espèces", "Carte bancaire", "Chèque", "Virement" });
            cmbPaymentMethod.SelectedIndex = 0;
        }

        private void ConfigureDataGridViews()
        {
            // Configure Services DataGridView
            ConfigureServicesDataGridView();

            // Configure Parts DataGridView
            ConfigurePartsDataGridView();
        }

        private void ConfigureServicesDataGridView()
        {
            // Set up columns for services grid
            dgvServices.AutoGenerateColumns = false;
            dgvServices.AllowUserToAddRows = false;
            dgvServices.AllowUserToDeleteRows = true;
            dgvServices.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvServices.MultiSelect = false;
            dgvServices.RowHeadersVisible = false;
            dgvServices.AlternatingRowsDefaultCellStyle.BackColor = Color.AliceBlue;
            dgvServices.DefaultCellStyle.SelectionBackColor = Color.LightBlue;
            dgvServices.DefaultCellStyle.SelectionForeColor = Color.Black;
            dgvServices.EnableHeadersVisualStyles = false;
            dgvServices.ColumnHeadersDefaultCellStyle.BackColor = Color.LightBlue;
            dgvServices.ColumnHeadersDefaultCellStyle.ForeColor = Color.Black;
            dgvServices.ColumnHeadersDefaultCellStyle.Font = new Font(dgvServices.Font, FontStyle.Bold);
            dgvServices.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            dgvServices.EditMode = DataGridViewEditMode.EditOnEnter;

            // Clear existing columns
            dgvServices.Columns.Clear();

            // Add columns
            dgvServices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                DataPropertyName = "Id",
                Visible = false
            });

            dgvServices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ServiceId",
                DataPropertyName = "ServiceId",
                Visible = false
            });

            dgvServices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ServiceName",
                DataPropertyName = "ServiceName",
                HeaderText = "Service",
                Width = 200,
                ReadOnly = true
            });

            dgvServices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                DataPropertyName = "Description",
                HeaderText = "Description",
                Width = 250,
                ReadOnly = true
            });

            dgvServices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Price",
                DataPropertyName = "Price",
                HeaderText = "Prix",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "N2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                },
                ReadOnly = true
            });

            // Add delete button column
            DataGridViewButtonColumn deleteButtonColumn = new DataGridViewButtonColumn
            {
                Name = "DeleteButton",
                HeaderText = "",
                Text = "X",
                UseColumnTextForButtonValue = true,
                Width = 30
            };
            dgvServices.Columns.Add(deleteButtonColumn);

            // Add event handlers
            dgvServices.CellClick += DgvServices_CellClick;
        }

        private void ConfigurePartsDataGridView()
        {
            // Set up columns for parts grid
            dgvParts.AutoGenerateColumns = false;
            dgvParts.AllowUserToAddRows = false;
            dgvParts.AllowUserToDeleteRows = true;
            dgvParts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvParts.MultiSelect = false;
            dgvParts.RowHeadersVisible = false;
            dgvParts.AlternatingRowsDefaultCellStyle.BackColor = Color.AliceBlue;
            dgvParts.DefaultCellStyle.SelectionBackColor = Color.LightBlue;
            dgvParts.DefaultCellStyle.SelectionForeColor = Color.Black;
            dgvParts.EnableHeadersVisualStyles = false;
            dgvParts.ColumnHeadersDefaultCellStyle.BackColor = Color.LightBlue;
            dgvParts.ColumnHeadersDefaultCellStyle.ForeColor = Color.Black;
            dgvParts.ColumnHeadersDefaultCellStyle.Font = new Font(dgvParts.Font, FontStyle.Bold);
            dgvParts.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            dgvParts.EditMode = DataGridViewEditMode.EditOnEnter;

            // Clear existing columns
            dgvParts.Columns.Clear();

            // Add columns
            dgvParts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                DataPropertyName = "Id",
                Visible = false
            });

            dgvParts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PartId",
                DataPropertyName = "PartId",
                Visible = false
            });

            dgvParts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PartName",
                DataPropertyName = "PartName",
                HeaderText = "Pièce",
                Width = 200,
                ReadOnly = true
            });

            dgvParts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PartCode",
                DataPropertyName = "PartCode",
                HeaderText = "Code",
                Width = 100,
                ReadOnly = true
            });

            dgvParts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                DataPropertyName = "Quantity",
                HeaderText = "Qté",
                Width = 50,
                ReadOnly = true
            });

            dgvParts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Price",
                DataPropertyName = "Price",
                HeaderText = "Prix",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "N2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                },
                ReadOnly = true
            });

            dgvParts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Total",
                DataPropertyName = "Total",
                HeaderText = "Total",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "N2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                },
                ReadOnly = true
            });

            // Add delete button column
            DataGridViewButtonColumn deleteButtonColumn = new DataGridViewButtonColumn
            {
                Name = "DeleteButton",
                HeaderText = "",
                Text = "X",
                UseColumnTextForButtonValue = true,
                Width = 30
            };
            dgvParts.Columns.Add(deleteButtonColumn);

            // Add event handlers
            dgvParts.CellClick += DgvParts_CellClick;
        }

        private async void BtnAddService_Click(object sender, EventArgs e)
        {
            try
            {
                var frm = new FRM_SERVICE_SELECTOR();

                if (frm.ShowDialog() == DialogResult.OK)
                {
                    var service = await _serviceCmd.GetByIdAsync(frm.SelectedServiceId);
                    if (service != null)
                    {
                        await AddServiceToGridView(service);
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'ajout du service: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void BtnAddPart_Click(object sender, EventArgs e)
        {
            try
            {
                var frm = new FRM_PART_SELECTOR();

                if (frm.ShowDialog() == DialogResult.OK)
                {
                    var part = await _inventoryCmd.GetByIdAsync(frm.SelectedPartId);
                    if (part != null)
                    {
                        await AddPartToGridView(part, frm.SelectedQuantity);
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'ajout de la pièce: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void DgvServices_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            // Check if the clicked cell is in the delete button column
            if (e.RowIndex >= 0 && e.ColumnIndex == dgvServices.Columns["DeleteButton"].Index)
            {
                try
                {
                    // Confirm deletion
                    var result = KryptonMessageBox.Show(
                        "Êtes-vous sûr de vouloir supprimer ce service?",
                        "Confirmation",
                        KryptonMessageBoxButtons.YesNo,
                        KryptonMessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        if (_repairOrderId <= 0)
                        {
                            // If order is not yet saved, remove from temporary list
                            int serviceId = Convert.ToInt32(dgvServices.Rows[e.RowIndex].Cells["ServiceId"].Value);
                            if (_tempServices != null && serviceId > 0)
                            {
                                _tempServices.RemoveAll(s => s.ServiceId == serviceId);
                                await LoadTempServices();
                                UpdateTotalLabel();
                                HasChanges = true;
                            }
                        }
                        else
                        {
                            // If order is already saved, remove normally
                            int serviceId = Convert.ToInt32(dgvServices.Rows[e.RowIndex].Cells["Id"].Value);
                            if (serviceId > 0)
                            {
                                bool success = await _cmd.RemoveServiceAsync(serviceId);

                                if (success)
                                {
                                    // Recalculate total cost
                                    await _cmd.CalculateTotalCostAsync(_repairOrderId);

                                    // Reload services and update total
                                    await LoadServices();
                                    await LoadRepairOrder();
                                    UpdateTotalLabel();

                                    HasChanges = true;
                                }
                                else
                                {
                                    KryptonMessageBox.Show("Erreur lors de la suppression du service", "Erreur",
                                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    KryptonMessageBox.Show($"Erreur lors de la suppression du service: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
            }
        }

        private async void DgvParts_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            // Check if the clicked cell is in the delete button column
            if (e.RowIndex >= 0 && e.ColumnIndex == dgvParts.Columns["DeleteButton"].Index)
            {
                try
                {
                    // Confirm deletion
                    var result = KryptonMessageBox.Show(
                        "Êtes-vous sûr de vouloir supprimer cette pièce?",
                        "Confirmation",
                        KryptonMessageBoxButtons.YesNo,
                        KryptonMessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        if (_repairOrderId <= 0)
                        {
                            // If order is not yet saved, remove from temporary list
                            int partId = Convert.ToInt32(dgvParts.Rows[e.RowIndex].Cells["PartId"].Value);
                            if (_tempParts != null && partId > 0)
                            {
                                _tempParts.RemoveAll(p => p.PartId == partId);
                                await LoadTempParts();
                                UpdateTotalLabel();
                                HasChanges = true;
                            }
                        }
                        else
                        {
                            // If order is already saved, remove normally
                            int partId = Convert.ToInt32(dgvParts.Rows[e.RowIndex].Cells["Id"].Value);
                            if (partId > 0)
                            {
                                bool success = await _cmd.RemovePartAsync(partId);

                                if (success)
                                {
                                    // Recalculate total cost
                                    await _cmd.CalculateTotalCostAsync(_repairOrderId);

                                    // Reload parts and update total
                                    await LoadParts();
                                    await LoadRepairOrder();
                                    UpdateTotalLabel();

                                    HasChanges = true;
                                }
                                else
                                {
                                    KryptonMessageBox.Show("Erreur lors de la suppression de la pièce", "Erreur",
                                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    KryptonMessageBox.Show($"Erreur lors de la suppression de la pièce: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
            }
        }

        private async Task LoadRepairOrder()
        {
            try
            {
                _repairOrder = await _cmd.GetByIdAsync(_repairOrderId);

                if (_repairOrder != null)
                {
                    // Set order information
                    txtOrderNumber.Text = _repairOrder.OrderNumber;
                    dtpOrderDate.Value = _repairOrder.CreatedAt;

                    // Set customer information
                    _customerId = _repairOrder.CustomerId;
                    var customer = await _customerCmd.GetByIdAsync(_customerId);
                    if (customer != null)
                    {
                        txtCustomer.Text = customer.name;
                        await LoadDevices();
                    }

                    // Set device information
                    _deviceId = _repairOrder.DeviceId;
                    if (_deviceId > 0)
                    {
                        foreach (var item in cmbDevice.Items)
                        {
                            var device = item as dynamic;
                            if (device != null && device.id == _deviceId)
                            {
                                cmbDevice.SelectedItem = item;
                                break;
                            }
                        }
                    }

                    // Set technician
                    if (_repairOrder.TechnicianId.HasValue)
                    {
                        foreach (var item in cmbTechnician.Items)
                        {
                            var technician = item as dynamic;
                            if (technician != null && technician.id == _repairOrder.TechnicianId.Value)
                            {
                                cmbTechnician.SelectedItem = item;
                                break;
                            }
                        }
                    }

                    // Set warranty period
                    txtWarrantyPeriod.Text = _repairOrder.WarrantyPeriod?.ToString() ?? "30";

                    // Set notes
                    txtProblem.Text = _repairOrder.RepairNotes;
                    txtTechNotes.Text = _repairOrder.TechnicalNotes;

                    // Set status
                    if (!string.IsNullOrEmpty(_repairOrder.Status))
                    {
                        RepairOrderStatus status;
                        if (Enum.TryParse(_repairOrder.Status, out status))
                        {
                            try
                            {
                                UpdateStatusButtons(status);
                            }
                            catch (Exception ex)
                            {
                                // Log the error but continue loading the form
                                System.Diagnostics.Debug.WriteLine($"Error updating status buttons: {ex.Message}");
                            }
                        }
                    }

                    // Set payment information
                    txtPaidAmount.Text = _repairOrder.PaidAmount.ToString("N2");
                    if (!string.IsNullOrEmpty(_repairOrder.PaymentMethod))
                    {
                        for (int i = 0; i < cmbPaymentMethod.Items.Count; i++)
                        {
                            if (cmbPaymentMethod.Items[i].ToString() == _repairOrder.PaymentMethod)
                            {
                                cmbPaymentMethod.SelectedIndex = i;
                                break;
                            }
                        }
                    }

                    // Update total
                    UpdateTotalLabel();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement de l'ordre de réparation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async Task LoadDevices()
        {
            try
            {
                if (_customerId <= 0)
                {
                    cmbDevice.DataSource = null;
                    return;
                }

                // Get all devices
                var allDevices = await _deviceCmd.GetAll("", 0, 1000);

                // Filter devices by customer ID
                var devices = allDevices.Where(d => (int)d.customer_id == _customerId).ToList();

                cmbDevice.DataSource = devices;
                cmbDevice.DisplayMember = "model";
                cmbDevice.ValueMember = "id";

                if (devices.Count() > 0)
                {
                    cmbDevice.SelectedIndex = 0;

                    // Use dynamic to access the id property of the DapperRow object
                    if (cmbDevice.SelectedItem != null)
                    {
                        dynamic selectedDevice = cmbDevice.SelectedItem;
                        if (selectedDevice != null && selectedDevice.id != null)
                        {
                            _deviceId = (int)selectedDevice.id;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des appareils: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async Task LoadServices()
        {
            try
            {
                if (_repairOrderId <= 0)
                {
                    await LoadTempServices();
                    return;
                }

                var services = await _cmd.GetServicesAsync(_repairOrderId);

                // Create DataTable for services
                DataTable dt = new DataTable();
                dt.Columns.Add("Id", typeof(int));
                dt.Columns.Add("ServiceId", typeof(int));
                dt.Columns.Add("ServiceName", typeof(string));
                dt.Columns.Add("Description", typeof(string));
                dt.Columns.Add("Price", typeof(decimal));

                foreach (var service in services)
                {
                    dt.Rows.Add(
                        service.Id,
                        service.ServiceId,
                        service.Service?.name,
                        service.Service?.description,
                        service.Price
                    );
                }

                // Set DataSource
                dgvServices.DataSource = dt;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des services: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private Task LoadTempServices()
        {
            try
            {
                // Create DataTable for temporary services
                DataTable dt = new DataTable();
                dt.Columns.Add("Id", typeof(int));
                dt.Columns.Add("ServiceId", typeof(int));
                dt.Columns.Add("ServiceName", typeof(string));
                dt.Columns.Add("Description", typeof(string));
                dt.Columns.Add("Price", typeof(decimal));

                if (_tempServices != null)
                {
                    foreach (var service in _tempServices)
                    {
                        dt.Rows.Add(
                            0, // Temporary ID
                            service.ServiceId,
                            service.ServiceName,
                            service.Description,
                            service.Price
                        );
                    }
                }

                // Set DataSource
                dgvServices.DataSource = dt;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des services temporaires: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }

            return Task.CompletedTask;
        }

        private async Task LoadParts()
        {
            try
            {
                if (_repairOrderId <= 0)
                {
                    await LoadTempParts();
                    return;
                }

                var parts = await _cmd.GetPartsAsync(_repairOrderId);

                // Create DataTable for parts
                DataTable dt = new DataTable();
                dt.Columns.Add("Id", typeof(int));
                dt.Columns.Add("PartId", typeof(int));
                dt.Columns.Add("PartName", typeof(string));
                dt.Columns.Add("PartCode", typeof(string));
                dt.Columns.Add("Quantity", typeof(int));
                dt.Columns.Add("Price", typeof(decimal));
                dt.Columns.Add("Total", typeof(decimal));

                foreach (var part in parts)
                {
                    dt.Rows.Add(
                        part.Id,
                        part.PartId,
                        part.Part?.Name,
                        part.Part?.Code,
                        part.Quantity,
                        part.Price,
                        part.Quantity * part.Price
                    );
                }

                // Set DataSource
                dgvParts.DataSource = dt;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des pièces: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private Task LoadTempParts()
        {
            try
            {
                // Create DataTable for temporary parts
                DataTable dt = new DataTable();
                dt.Columns.Add("Id", typeof(int));
                dt.Columns.Add("PartId", typeof(int));
                dt.Columns.Add("PartName", typeof(string));
                dt.Columns.Add("PartCode", typeof(string));
                dt.Columns.Add("Quantity", typeof(int));
                dt.Columns.Add("Price", typeof(decimal));
                dt.Columns.Add("Total", typeof(decimal));

                if (_tempParts != null)
                {
                    foreach (var part in _tempParts)
                    {
                        dt.Rows.Add(
                            0, // Temporary ID
                            part.PartId,
                            part.PartName,
                            part.PartCode,
                            part.Quantity,
                            part.Price,
                            part.Quantity * part.Price
                        );
                    }
                }

                // Set DataSource
                dgvParts.DataSource = dt;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des pièces temporaires: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }

            return Task.CompletedTask;
        }

        private async Task AddServiceToGridView(Service service)
        {
            if (_repairOrderId <= 0)
            {
                // If order is not yet saved, add to temporary list
                if (_tempServices == null)
                {
                    _tempServices = new List<TempService>();
                }

                // Check if service already exists
                if (_tempServices.Any(s => s.ServiceId == service.id))
                {
                    KryptonMessageBox.Show("Ce service est déjà ajouté à l'ordre de réparation.", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                _tempServices.Add(new TempService
                {
                    ServiceId = service.id,
                    ServiceName = service.name,
                    Description = service.description,
                    Price = service.price
                });

                await LoadTempServices();
                UpdateTotalLabel();
                HasChanges = true;
            }
            else
            {
                // If order is already saved, add normally
                await AddServiceToOrder(service.id);
            }
        }

        private async Task AddServiceToOrder(int serviceId)
        {
            try
            {
                var service = await _serviceCmd.GetByIdAsync(serviceId);

                if (service != null)
                {
                    // Check if service already exists
                    var existingServices = await _cmd.GetServicesAsync(_repairOrderId);
                    if (existingServices.Any(s => s.ServiceId == serviceId))
                    {
                        KryptonMessageBox.Show("Ce service est déjà ajouté à l'ordre de réparation.", "Information",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        return;
                    }

                    // Create a new repair order service
                    var repairOrderService = new RepairOrderService
                    {
                        RepairOrderId = _repairOrderId,
                        ServiceId = serviceId,
                        Price = service.price
                    };

                    // Add the service to the repair order
                    int id = await _cmd.AddServiceAsync(repairOrderService);

                    if (id > 0)
                    {
                        // Recalculate total cost
                        await _cmd.CalculateTotalCostAsync(_repairOrderId);

                        // Reload services and update total
                        await LoadServices();
                        await LoadRepairOrder();
                        UpdateTotalLabel();

                        HasChanges = true;
                    }
                    else
                    {
                        KryptonMessageBox.Show("Erreur lors de l'ajout du service", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'ajout du service: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async Task AddPartToGridView(Part part, int quantity)
        {
            // Check if quantity is available
            if (part.Quantity < quantity)
            {
                KryptonMessageBox.Show($"Stock insuffisant. Disponible: {part.Quantity}", "Stock insuffisant",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                return;
            }

            if (_repairOrderId <= 0)
            {
                // If order is not yet saved, add to temporary list
                if (_tempParts == null)
                {
                    _tempParts = new List<TempPart>();
                }

                // Check if part already exists
                if (_tempParts.Any(p => p.PartId == part.Id))
                {
                    KryptonMessageBox.Show("Cette pièce est déjà ajoutée à l'ordre de réparation.", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                _tempParts.Add(new TempPart
                {
                    PartId = part.Id,
                    PartName = part.Name,
                    PartCode = part.Code,
                    Quantity = quantity,
                    Price = part.Selling_Price
                });

                await LoadTempParts();
                UpdateTotalLabel();
                HasChanges = true;
            }
            else
            {
                // If order is already saved, add normally
                await AddPartToOrder(part.Id, quantity);
            }
        }

        private async Task AddPartToOrder(int partId, int quantity)
        {
            try
            {
                var part = await _inventoryCmd.GetByIdAsync(partId);

                if (part != null)
                {
                    // Check if there's enough stock
                    if (part.Quantity < quantity)
                    {
                        KryptonMessageBox.Show($"Stock insuffisant. Disponible: {part.Quantity}", "Stock insuffisant",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                        return;
                    }

                    // Check if part already exists
                    var existingParts = await _cmd.GetPartsAsync(_repairOrderId);
                    if (existingParts.Any(p => p.PartId == partId))
                    {
                        KryptonMessageBox.Show("Cette pièce est déjà ajoutée à l'ordre de réparation.", "Information",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        return;
                    }

                    // Create a new repair order part
                    var repairOrderPart = new RepairOrderPart
                    {
                        RepairOrderId = _repairOrderId,
                        PartId = partId,
                        Quantity = quantity,
                        Price = part.Selling_Price
                    };

                    // Add the part to the repair order
                    int id = await _cmd.AddPartAsync(repairOrderPart);

                    if (id > 0)
                    {
                        // Update inventory quantity
                        int newQuantity = part.Quantity - quantity;
                        int? userId = null;
                        if (Properties.Settings.Default.CurrentUserId > 0)
                        {
                            userId = Properties.Settings.Default.CurrentUserId;
                        }

                        await _inventoryCmd.UpdateQuantityWithTransactionAsync(
                            partId,
                            newQuantity,
                            $"Utilisé dans l'ordre de réparation #{_repairOrder.OrderNumber}",
                            $"Pièce utilisée dans l'ordre de réparation #{_repairOrder.OrderNumber}",
                            userId
                        );

                        // Recalculate total cost
                        await _cmd.CalculateTotalCostAsync(_repairOrderId);

                        // Reload parts and update total
                        await LoadParts();
                        await LoadRepairOrder();
                        UpdateTotalLabel();

                        HasChanges = true;
                    }
                    else
                    {
                        KryptonMessageBox.Show("Erreur lors de l'ajout de la pièce", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'ajout de la pièce: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void UpdateTotalLabel()
        {
            decimal total = 0;

            if (_repairOrderId > 0 && _repairOrder != null)
            {
                // Use the total from the loaded repair order
                total = _repairOrder.TotalCost;
            }
            else
            {
                // Calculate from temporary services and parts
                if (_tempServices != null)
                {
                    total += _tempServices.Sum(s => s.Price);
                }

                if (_tempParts != null)
                {
                    total += _tempParts.Sum(p => p.Price * p.Quantity);
                }
            }

            // Update total label
            lblTotalValue.Text = total.ToString("N2") + " DZD";

            // Update remaining amount
            decimal paidAmount = 0;
            if (!string.IsNullOrEmpty(txtPaidAmount.Text))
            {
                decimal.TryParse(txtPaidAmount.Text, out paidAmount);
            }

            decimal remainingAmount = total - paidAmount;
            lblRemainingValue.Text = remainingAmount.ToString("N2") + " DZD";
        }

        // Helper class for temporary services
        private class TempService
        {
            public int ServiceId { get; set; }
            public string ServiceName { get; set; }
            public string Description { get; set; }
            public decimal Price { get; set; }
        }

        // Helper class for temporary parts
        private class TempPart
        {
            public int PartId { get; set; }
            public string PartName { get; set; }
            public string PartCode { get; set; }
            public int Quantity { get; set; }
            public decimal Price { get; set; }
        }

        #region Event Handlers

        private void btnBack_Click(object sender, EventArgs e)
        {
            if (HasChanges)
            {
                var result = KryptonMessageBox.Show(
                    "Vous avez des modifications non enregistrées. Voulez-vous quitter sans enregistrer?",
                    "Modifications non enregistrées",
                    KryptonMessageBoxButtons.YesNo,
                    KryptonMessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    return;
                }
            }

            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            btnBack_Click(sender, e);
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            await SaveRepairOrder();
        }

        private async void btnSaveAndPay_Click(object sender, EventArgs e)
        {
            if (await SaveRepairOrder())
            {
                // Open payment form
                var paymentForm = new FRM_PAYMENT(_repairOrderId, _repairOrder.TotalCost, _repairOrder.PaidAmount);
                if (paymentForm.ShowDialog() == DialogResult.OK)
                {
                    // Reload repair order to get updated payment info
                    await LoadRepairOrder();
                    UpdateTotalLabel();
                }
            }
        }

        private async void btnSelectCustomer_Click(object sender, EventArgs e)
        {
            try
            {
                var frm = new FRM_SELECT_CUSTOMER();

                if (frm.ShowDialog() == DialogResult.OK)
                {
                    if (frm.SelectedCustomer != null)
                    {
                        _customerId = frm.SelectedCustomer.id;
                        txtCustomer.Text = frm.SelectedCustomer.name;

                        // Load devices for this customer
                        await LoadDevices();

                        HasChanges = true;
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la sélection du client: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void btnSelectDevice_Click(object sender, EventArgs e)
        {
            try
            {
                if (_customerId <= 0)
                {
                    KryptonMessageBox.Show("Veuillez d'abord sélectionner un client.", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                // Open the devices list form
                var frm = new FRM_DEVICES_LIST();

                if (frm.ShowDialog() == DialogResult.OK)
                {
                    // Since we can't access the DataGridView directly, we'll get all devices
                    // and find the selected one based on the current row index
                    var allDevices = await _deviceCmd.GetAll("", 0, 1000);

                    // Get the selected device
                    if (allDevices != null && allDevices.Any())
                    {
                        // Assume the first device is selected
                        var selectedDevice = allDevices.FirstOrDefault();
                        if (selectedDevice != null)
                        {
                            _deviceId = selectedDevice.id;

                            // Reload devices
                            await LoadDevices();

                            // Select the device in the combo box
                            foreach (var item in cmbDevice.Items)
                            {
                                var device = item as dynamic;
                                if (device != null && device.id == _deviceId)
                                {
                                    cmbDevice.SelectedItem = item;
                                    HasChanges = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la sélection de l'appareil: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void cmbDevice_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (cmbDevice.SelectedItem != null)
                {
                    // Use dynamic to access the id property of the DapperRow object
                    dynamic selectedDevice = cmbDevice.SelectedItem;
                    if (selectedDevice != null && selectedDevice.id != null)
                    {
                        _deviceId = (int)selectedDevice.id;
                        HasChanges = true;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in cmbDevice_SelectedIndexChanged: {ex.Message}");
                // Don't show a message box here to avoid interrupting the user experience
            }
        }

        private void txtPaidAmount_TextChanged(object sender, EventArgs e)
        {
            UpdateTotalLabel();
            HasChanges = true;
        }

        private void cmbPaymentMethod_SelectedIndexChanged(object sender, EventArgs e)
        {
            HasChanges = true;
        }

        private void txtProblem_TextChanged(object sender, EventArgs e)
        {
            HasChanges = true;
        }

        private void txtTechNotes_TextChanged(object sender, EventArgs e)
        {
            HasChanges = true;
        }

        private void cmbTechnician_SelectedIndexChanged(object sender, EventArgs e)
        {
            HasChanges = true;
        }

        private void txtWarrantyPeriod_TextChanged(object sender, EventArgs e)
        {
            HasChanges = true;
        }

        #endregion

        private async Task<bool> SaveRepairOrder()
        {
            try
            {
                // Show a progress indicator
                Cursor = Cursors.WaitCursor;

                // Validate required fields
                if (_customerId <= 0)
                {
                    KryptonMessageBox.Show("Veuillez sélectionner un client.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    return false;
                }

                if (_deviceId <= 0)
                {
                    KryptonMessageBox.Show("Veuillez sélectionner un appareil.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    return false;
                }

                // Get technician ID
                int? technicianId = null;
                if (cmbTechnician.SelectedItem != null)
                {
                    var technician = cmbTechnician.SelectedItem as dynamic;
                    if (technician != null)
                    {
                        technicianId = technician.id;
                    }
                }

                // Get warranty period
                int? warrantyPeriod = null;
                if (!string.IsNullOrEmpty(txtWarrantyPeriod.Text))
                {
                    int period;
                    if (int.TryParse(txtWarrantyPeriod.Text, out period))
                    {
                        warrantyPeriod = period;
                    }
                }

                // Get paid amount
                decimal paidAmount = 0;
                if (!string.IsNullOrEmpty(txtPaidAmount.Text))
                {
                    decimal.TryParse(txtPaidAmount.Text, out paidAmount);
                }

                // Get payment method
                string paymentMethod = null;
                if (cmbPaymentMethod.SelectedItem != null)
                {
                    paymentMethod = cmbPaymentMethod.SelectedItem.ToString();
                }

                // Calculate total cost
                decimal serviceCost = 0;
                decimal partCost = 0;

                if (_repairOrderId <= 0)
                {
                    // Calculate from temporary lists
                    if (_tempServices != null)
                    {
                        serviceCost = _tempServices.Sum(s => s.Price);
                    }

                    if (_tempParts != null)
                    {
                        partCost = _tempParts.Sum(p => p.Price * p.Quantity);
                    }
                }
                else
                {
                    // Get from existing repair order
                    serviceCost = _repairOrder.ServiceCost;
                    partCost = _repairOrder.PartCost;
                }

                decimal totalCost = serviceCost + partCost;

                // Determine payment status
                PaymentStatus paymentStatus;
                if (paidAmount <= 0)
                {
                    paymentStatus = PaymentStatus.unpaid;
                }
                else if (paidAmount < totalCost)
                {
                    paymentStatus = PaymentStatus.partially_paid;
                }
                else
                {
                    paymentStatus = PaymentStatus.paid;
                }

                if (_repairOrderId <= 0)
                {
                    // Create new repair order
                    var repairOrder = new RepairOrder
                    {
                        OrderNumber = txtOrderNumber.Text,
                        CustomerId = _customerId,
                        DeviceId = _deviceId,
                        Status = string.IsNullOrEmpty(_repairOrder.Status) ? RepairOrderStatus.received.ToString() : _repairOrder.Status,
                        TotalCost = totalCost,
                        ServiceCost = serviceCost,
                        PartCost = partCost,
                        PaidAmount = paidAmount,
                        PaymentStatus = paymentStatus,
                        PaymentMethod = paymentMethod,
                        TechnicianId = technicianId,
                        WarrantyPeriod = warrantyPeriod,
                        RepairNotes = txtProblem.Text,
                        TechnicalNotes = txtTechNotes.Text
                    };

                    // Use a transaction to save everything at once
                    using (var connection = new MySql.Data.MySqlClient.MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
                    {
                        await connection.OpenAsync();
                        using (var transaction = await connection.BeginTransactionAsync())
                        {
                            try
                            {
                                // Save repair order
                                const string insertOrderSql = @"
                                    INSERT INTO repair_orders (
                                        order_number, device_id, customer_id, status,
                                        total_cost, service_cost, part_cost, paid_amount,
                                        payment_status, payment_method, technician_id,
                                        warranty_period, repair_notes, technical_notes, created_at
                                    ) VALUES (
                                        @OrderNumber, @DeviceId, @CustomerId, @Status,
                                        @TotalCost, @ServiceCost, @PartCost, @PaidAmount,
                                        @PaymentStatus, @PaymentMethod, @TechnicianId,
                                        @WarrantyPeriod, @RepairNotes, @TechnicalNotes, @CreatedAt
                                    )";

                                repairOrder.CreatedAt = DateTime.Now;

                                await connection.ExecuteAsync(insertOrderSql, repairOrder, transaction);
                                _repairOrderId = await connection.ExecuteScalarAsync<int>("SELECT LAST_INSERT_ID()", null, transaction);

                                if (_repairOrderId <= 0)
                                {
                                    throw new Exception("Failed to get the new repair order ID");
                                }

                                // Save services in batch if there are any
                                if (_tempServices != null && _tempServices.Count > 0)
                                {
                                    const string insertServiceSql = @"
                                        INSERT INTO repair_order_services (
                                            repair_order_id, service_id, price, created_at
                                        ) VALUES (
                                            @RepairOrderId, @ServiceId, @Price, @CreatedAt
                                        )";

                                    var services = _tempServices.Select(s => new
                                    {
                                        RepairOrderId = _repairOrderId,
                                        ServiceId = s.ServiceId,
                                        Price = s.Price,
                                        CreatedAt = DateTime.Now
                                    }).ToList();

                                    await connection.ExecuteAsync(insertServiceSql, services, transaction);
                                }

                                // Save parts in batch and update inventory if there are any
                                if (_tempParts != null && _tempParts.Count > 0)
                                {
                                    // Insert parts in batch
                                    const string insertPartSql = @"
                                        INSERT INTO repair_order_parts (
                                            repair_order_id, part_id, quantity, price, created_at
                                        ) VALUES (
                                            @RepairOrderId, @PartId, @Quantity, @Price, @CreatedAt
                                        )";

                                    var parts = _tempParts.Select(p => new
                                    {
                                        RepairOrderId = _repairOrderId,
                                        PartId = p.PartId,
                                        Quantity = p.Quantity,
                                        Price = p.Price,
                                        CreatedAt = DateTime.Now
                                    }).ToList();

                                    await connection.ExecuteAsync(insertPartSql, parts, transaction);

                                    // Get all parts that need inventory updates in a single query
                                    var partIds = _tempParts.Select(p => p.PartId).ToList();
                                    const string getPartsQuery = "SELECT * FROM parts WHERE id IN @PartIds";
                                    var inventoryParts = await connection.QueryAsync<Part>(getPartsQuery, new { PartIds = partIds }, transaction);

                                    // Create a dictionary for quick lookup
                                    var inventoryPartsDict = inventoryParts.ToDictionary(p => p.Id);

                                    // Update inventory quantities in batch
                                    const string updateQuantitySql = @"
                                        UPDATE parts
                                        SET quantity = @NewQuantity,
                                            updated_at = @UpdatedAt
                                        WHERE id = @PartId";

                                    // Prepare inventory transactions
                                    const string insertTransactionSql = @"
                                        INSERT INTO inventory_transactions (
                                            part_id, quantity_before, quantity_after,
                                            transaction_type, reference, notes, user_id, created_at
                                        ) VALUES (
                                            @PartId, @QuantityBefore, @QuantityAfter,
                                            @TransactionType, @Reference, @Notes, @UserId, @CreatedAt
                                        )";

                                    int? userId = null;
                                    if (Properties.Settings.Default.CurrentUserId > 0)
                                    {
                                        userId = Properties.Settings.Default.CurrentUserId;
                                    }

                                    // Process each part
                                    foreach (var part in _tempParts)
                                    {
                                        if (inventoryPartsDict.TryGetValue(part.PartId, out var inventoryPart))
                                        {
                                            int newQuantity = inventoryPart.Quantity - part.Quantity;

                                            // Update part quantity
                                            await connection.ExecuteAsync(updateQuantitySql, new
                                            {
                                                NewQuantity = newQuantity,
                                                UpdatedAt = DateTime.Now,
                                                PartId = part.PartId
                                            }, transaction);

                                            // Add inventory transaction
                                            await connection.ExecuteAsync(insertTransactionSql, new
                                            {
                                                PartId = part.PartId,
                                                QuantityBefore = inventoryPart.Quantity,
                                                QuantityAfter = newQuantity,
                                                TransactionType = "out",
                                                Reference = $"Ordre de réparation #{repairOrder.OrderNumber}",
                                                Notes = $"Pièce utilisée dans l'ordre de réparation #{repairOrder.OrderNumber}",
                                                UserId = userId,
                                                CreatedAt = DateTime.Now
                                            }, transaction);
                                        }
                                    }
                                }

                                // Commit the transaction
                                await transaction.CommitAsync();

                                // Load the newly created repair order
                                await LoadRepairOrder();

                                // Update form title
                                this.Text = $"Modifier l'ordre de réparation #{_repairOrder.OrderNumber}";

                                // Create debt if there's an outstanding balance
                                decimal outstandingAmount = _repairOrder.TotalCost - _repairOrder.PaidAmount;
                                if (outstandingAmount > 0 && paymentStatus != PaymentStatus.paid)
                                {
                                    // Ask if user wants to create a debt record
                                    var createDebtResult = KryptonMessageBox.Show(
                                        $"Il y a un montant impayé de {outstandingAmount:C}. Voulez-vous créer une dette pour ce montant?",
                                        "Créer une dette",
                                        KryptonMessageBoxButtons.YesNo,
                                        KryptonMessageBoxIcon.Question);

                                    if (createDebtResult == DialogResult.Yes)
                                    {
                                        // Ask for due date
                                        DateTime? dueDate = null;
                                        using (var dueDateForm = new KryptonForm())
                                        {
                                            dueDateForm.Text = "Date d'échéance";
                                            dueDateForm.Size = new Size(300, 200);
                                            dueDateForm.StartPosition = FormStartPosition.CenterParent;
                                            dueDateForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                                            dueDateForm.MaximizeBox = false;
                                            dueDateForm.MinimizeBox = false;

                                            var label = new KryptonLabel
                                            {
                                                Text = "Date d'échéance (optionnelle):",
                                                Location = new Point(20, 20),
                                                Size = new Size(250, 20)
                                            };

                                            var datePicker = new KryptonDateTimePicker
                                            {
                                                Location = new Point(20, 50),
                                                Size = new Size(250, 25),
                                                Value = DateTime.Now.AddDays(30)
                                            };

                                            var okButton = new KryptonButton
                                            {
                                                Text = "OK",
                                                Location = new Point(70, 100),
                                                Size = new Size(80, 30),
                                                DialogResult = DialogResult.OK
                                            };

                                            var cancelButton = new KryptonButton
                                            {
                                                Text = "Annuler",
                                                Location = new Point(160, 100),
                                                Size = new Size(80, 30),
                                                DialogResult = DialogResult.Cancel
                                            };

                                            dueDateForm.Controls.Add(label);
                                            dueDateForm.Controls.Add(datePicker);
                                            dueDateForm.Controls.Add(okButton);
                                            dueDateForm.Controls.Add(cancelButton);
                                            dueDateForm.AcceptButton = okButton;
                                            dueDateForm.CancelButton = cancelButton;

                                            if (dueDateForm.ShowDialog() == DialogResult.OK)
                                            {
                                                dueDate = datePicker.Value;
                                            }
                                        }

                                        // Create debt
                                        var debt = new Debt
                                        {
                                            CustomerId = _customerId,
                                            RepairOrderId = _repairOrderId,
                                            Amount = outstandingAmount,
                                            PaidAmount = 0,
                                            DebtDate = DateTime.Now,
                                            DueDate = dueDate,
                                            Status = paymentStatus,
                                            Notes = $"Dette pour l'ordre de réparation #{_repairOrder.OrderNumber}",
                                            CreatedAt = DateTime.Now
                                        };

                                        int debtId = await _debtCmd.InsertAsync(debt);
                                        if (debtId > 0)
                                        {
                                            KryptonMessageBox.Show($"Dette créée avec succès pour un montant de {outstandingAmount:C}.", "Dette créée",
                                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                                        }
                                    }
                                }

                                KryptonMessageBox.Show("Ordre de réparation enregistré avec succès.", "Succès",
                                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                                // Print receipt
                                await PrintReceipt();
                            }
                            catch (Exception ex)
                            {
                                await transaction.RollbackAsync();
                                throw new Exception($"Transaction error: {ex.Message}", ex);
                            }
                        }
                    }
                }
                else
                {
                    // Update existing repair order
                    _repairOrder.CustomerId = _customerId;
                    _repairOrder.DeviceId = _deviceId;
                    _repairOrder.TechnicianId = technicianId;
                    _repairOrder.WarrantyPeriod = warrantyPeriod;
                    _repairOrder.RepairNotes = txtProblem.Text;
                    _repairOrder.TechnicalNotes = txtTechNotes.Text;
                    _repairOrder.PaidAmount = paidAmount;
                    _repairOrder.PaymentMethod = paymentMethod;
                    _repairOrder.PaymentStatus = paymentStatus;

                    // Save repair order
                    bool success = await _cmd.UpdateAsync(_repairOrder);

                    if (!success)
                    {
                        KryptonMessageBox.Show("Erreur lors de la mise à jour de l'ordre de réparation.", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return false;
                    }

                    // Recalculate total cost
                    await _cmd.CalculateTotalCostAsync(_repairOrderId);

                    // Reload repair order
                    await LoadRepairOrder();

                    // Create debt if there's an outstanding balance
                    decimal outstandingAmount = _repairOrder.TotalCost - _repairOrder.PaidAmount;
                    if (outstandingAmount > 0 && paymentStatus != PaymentStatus.paid)
                    {
                        // Check if a debt already exists for this repair order
                        var existingDebts = await _debtCmd.GetByRepairOrderIdAsync(_repairOrderId);
                        bool debtExists = existingDebts != null && existingDebts.Any();

                        if (!debtExists)
                        {
                            // Ask if user wants to create a debt record
                            var createDebtResult = KryptonMessageBox.Show(
                                $"Il y a un montant impayé de {outstandingAmount:C}. Voulez-vous créer une dette pour ce montant?",
                                "Créer une dette",
                                KryptonMessageBoxButtons.YesNo,
                                KryptonMessageBoxIcon.Question);

                            if (createDebtResult == DialogResult.Yes)
                            {
                                // Ask for due date
                                DateTime? dueDate = null;
                                using (var dueDateForm = new KryptonForm())
                                {
                                    dueDateForm.Text = "Date d'échéance";
                                    dueDateForm.Size = new Size(300, 200);
                                    dueDateForm.StartPosition = FormStartPosition.CenterParent;
                                    dueDateForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                                    dueDateForm.MaximizeBox = false;
                                    dueDateForm.MinimizeBox = false;

                                    var label = new KryptonLabel
                                    {
                                        Text = "Date d'échéance (optionnelle):",
                                        Location = new Point(20, 20),
                                        Size = new Size(250, 20)
                                    };

                                    var datePicker = new KryptonDateTimePicker
                                    {
                                        Location = new Point(20, 50),
                                        Size = new Size(250, 25),
                                        Value = DateTime.Now.AddDays(30)
                                    };

                                    var okButton = new KryptonButton
                                    {
                                        Text = "OK",
                                        Location = new Point(70, 100),
                                        Size = new Size(80, 30),
                                        DialogResult = DialogResult.OK
                                    };

                                    var cancelButton = new KryptonButton
                                    {
                                        Text = "Annuler",
                                        Location = new Point(160, 100),
                                        Size = new Size(80, 30),
                                        DialogResult = DialogResult.Cancel
                                    };

                                    dueDateForm.Controls.Add(label);
                                    dueDateForm.Controls.Add(datePicker);
                                    dueDateForm.Controls.Add(okButton);
                                    dueDateForm.Controls.Add(cancelButton);
                                    dueDateForm.AcceptButton = okButton;
                                    dueDateForm.CancelButton = cancelButton;

                                    if (dueDateForm.ShowDialog() == DialogResult.OK)
                                    {
                                        dueDate = datePicker.Value;
                                    }
                                }

                                // Create debt
                                var debt = new Debt
                                {
                                    CustomerId = _customerId,
                                    RepairOrderId = _repairOrderId,
                                    Amount = outstandingAmount,
                                    PaidAmount = 0,
                                    DebtDate = DateTime.Now,
                                    DueDate = dueDate,
                                    Status = paymentStatus,
                                    Notes = $"Dette pour l'ordre de réparation #{_repairOrder.OrderNumber}",
                                    CreatedAt = DateTime.Now
                                };

                                int debtId = await _debtCmd.InsertAsync(debt);
                                if (debtId > 0)
                                {
                                    KryptonMessageBox.Show($"Dette créée avec succès pour un montant de {outstandingAmount:C}.", "Dette créée",
                                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                                }
                            }
                        }
                        else
                        {
                            // Update existing debt
                            var debt = existingDebts.First();

                            // Ask if user wants to update the debt amount
                            var updateDebtResult = KryptonMessageBox.Show(
                                $"Une dette existe déjà pour cet ordre de réparation. Voulez-vous mettre à jour le montant à {outstandingAmount:C}?",
                                "Mettre à jour la dette",
                                KryptonMessageBoxButtons.YesNo,
                                KryptonMessageBoxIcon.Question);

                            if (updateDebtResult == DialogResult.Yes)
                            {
                                debt.Amount = outstandingAmount;
                                debt.Status = paymentStatus;
                                debt.UpdatedAt = DateTime.Now;

                                bool debtUpdateSuccess = await _debtCmd.UpdateAsync(debt);
                                if (debtUpdateSuccess)
                                {
                                    KryptonMessageBox.Show($"Dette mise à jour avec succès pour un montant de {outstandingAmount:C}.", "Dette mise à jour",
                                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                                }
                            }
                        }
                    }

                    KryptonMessageBox.Show("Ordre de réparation mis à jour avec succès.", "Succès",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                    // Print receipt
                    await PrintReceipt();
                }

                HasChanges = false;
                return true;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'enregistrement de l'ordre de réparation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                return false;
            }
            finally
            {
                // Reset cursor
                Cursor = Cursors.Default;
            }
        }

        private async Task PrintReceipt()
        {
            try
            {
                // Check if repair order exists
                if (_repairOrderId <= 0 || _repairOrder == null)
                {
                    KryptonMessageBox.Show("Aucun ordre de réparation à imprimer.", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                // Ask user for paper size
                string[] paperSizes = { "Thermique (80mm)", "A5", "A4" };
                string selectedPaperSize = "Thermique (80mm)"; // Default

                using (var paperSizeForm = new KryptonForm())
                {
                    paperSizeForm.Text = "Choisir le format du papier";
                    paperSizeForm.Size = new Size(300, 200);
                    paperSizeForm.StartPosition = FormStartPosition.CenterParent;
                    paperSizeForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                    paperSizeForm.MaximizeBox = false;
                    paperSizeForm.MinimizeBox = false;

                    var label = new KryptonLabel
                    {
                        Text = "Sélectionnez le format du papier:",
                        Location = new Point(20, 20),
                        Size = new Size(250, 20)
                    };

                    var comboBox = new KryptonComboBox
                    {
                        Location = new Point(20, 50),
                        Size = new Size(250, 25),
                        DropDownWidth = 250
                    };
                    comboBox.Items.AddRange(paperSizes);
                    comboBox.SelectedIndex = 0;

                    var okButton = new KryptonButton
                    {
                        Text = "OK",
                        Location = new Point(110, 100),
                        Size = new Size(80, 30),
                        DialogResult = DialogResult.OK
                    };

                    paperSizeForm.Controls.Add(label);
                    paperSizeForm.Controls.Add(comboBox);
                    paperSizeForm.Controls.Add(okButton);
                    paperSizeForm.AcceptButton = okButton;

                    if (paperSizeForm.ShowDialog() == DialogResult.OK)
                    {
                        selectedPaperSize = comboBox.SelectedItem.ToString();
                    }
                }

                // Determine which report template to use based on paper size
                string reportFileName;
                switch (selectedPaperSize)
                {
                    case "A4":
                        reportFileName = "RepairOrderReceiptA4.frx";
                        break;
                    case "A5":
                        reportFileName = "RepairOrderReceiptA5.frx";
                        break;
                    default:
                        reportFileName = "RepairOrderReceipt.frx"; // Thermal receipt (80mm)
                        break;
                }

                // Get the report template file path
                string reportFile = Path.Combine(Application.StartupPath, "Reports", reportFileName);

                if (!File.Exists(reportFile))
                {
                    // Create directory if it doesn't exist
                    Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                    // Copy the report file from the project to the output directory
                    File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", reportFileName), reportFile);

                    if (!File.Exists(reportFile))
                    {
                        KryptonMessageBox.Show("Le fichier de rapport n'existe pas: " + reportFile, "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return;
                    }
                }

                // Create a new report
                using (Report report = new Report())
                {
                    // Load the report template
                    report.Load(reportFile);

                    // Get customer information
                    var customer = await _customerCmd.GetByIdAsync(_repairOrder.CustomerId);

                    // Get device information
                    var device = await _deviceCmd.GetByIdAsync(_repairOrder.DeviceId);

                    // Get technician information
                    string technicianName = "";
                    if (_repairOrder.TechnicianId.HasValue)
                    {
                        var technician = await _userCmd.GetByIdAsync(_repairOrder.TechnicianId.Value);
                        if (technician != null)
                        {
                            technicianName = technician.Full_Name;
                        }
                    }

                    // Get services
                    var services = await _cmd.GetServicesAsync(_repairOrderId);

                    // Get parts
                    var parts = await _cmd.GetPartsAsync(_repairOrderId);

                    // Create DataTable for repair order
                    DataTable dtRepairOrder = new DataTable();
                    dtRepairOrder.Columns.Add("OrderNumber", typeof(string));
                    dtRepairOrder.Columns.Add("CustomerName", typeof(string));
                    dtRepairOrder.Columns.Add("CustomerPhone", typeof(string));
                    dtRepairOrder.Columns.Add("DeviceType", typeof(string));
                    dtRepairOrder.Columns.Add("DeviceBrand", typeof(string));
                    dtRepairOrder.Columns.Add("DeviceModel", typeof(string));
                    dtRepairOrder.Columns.Add("Status", typeof(string));
                    dtRepairOrder.Columns.Add("TechnicianName", typeof(string));
                    dtRepairOrder.Columns.Add("RepairNotes", typeof(string));
                    dtRepairOrder.Columns.Add("TechnicalNotes", typeof(string));
                    dtRepairOrder.Columns.Add("WarrantyPeriod", typeof(int));
                    dtRepairOrder.Columns.Add("TotalCost", typeof(decimal));
                    dtRepairOrder.Columns.Add("ServiceCost", typeof(decimal));
                    dtRepairOrder.Columns.Add("PartCost", typeof(decimal));
                    dtRepairOrder.Columns.Add("PaidAmount", typeof(decimal));
                    dtRepairOrder.Columns.Add("RemainingAmount", typeof(decimal));
                    dtRepairOrder.Columns.Add("PaymentStatus", typeof(string));
                    dtRepairOrder.Columns.Add("PaymentMethod", typeof(string));
                    dtRepairOrder.Columns.Add("CreatedAt", typeof(DateTime));

                    // Add repair order data
                    string statusDisplay = GetStatusDisplayName((RepairOrderStatus)Enum.Parse(typeof(RepairOrderStatus), _repairOrder.Status));
                    string paymentStatusDisplay = GetPaymentStatusDisplayName((PaymentStatus)Enum.Parse(typeof(PaymentStatus), _repairOrder.PaymentStatus.ToString()));

                    dtRepairOrder.Rows.Add(
                        _repairOrder.OrderNumber,
                        customer?.name,
                        customer?.phone,
                        device?.Type,
                        device?.Brand,
                        device?.Model,
                        statusDisplay,
                        technicianName,
                        _repairOrder.RepairNotes,
                        _repairOrder.TechnicalNotes,
                        _repairOrder.WarrantyPeriod,
                        _repairOrder.TotalCost,
                        _repairOrder.ServiceCost,
                        _repairOrder.PartCost,
                        _repairOrder.PaidAmount,
                        _repairOrder.TotalCost - _repairOrder.PaidAmount,
                        paymentStatusDisplay,
                        _repairOrder.PaymentMethod,
                        _repairOrder.CreatedAt
                    );

                    // Create DataTable for services
                    DataTable dtServices = new DataTable();
                    dtServices.Columns.Add("ServiceName", typeof(string));
                    dtServices.Columns.Add("Description", typeof(string));
                    dtServices.Columns.Add("Price", typeof(decimal));

                    // Add services data
                    foreach (var service in services)
                    {
                        dtServices.Rows.Add(
                            service.Service?.name,
                            service.Service?.description,
                            service.Price
                        );
                    }

                    // Create DataTable for parts
                    DataTable dtParts = new DataTable();
                    dtParts.Columns.Add("PartName", typeof(string));
                    dtParts.Columns.Add("PartCode", typeof(string));
                    dtParts.Columns.Add("Quantity", typeof(int));
                    dtParts.Columns.Add("Price", typeof(decimal));
                    dtParts.Columns.Add("Total", typeof(decimal));

                    // Add parts data
                    foreach (var part in parts)
                    {
                        dtParts.Rows.Add(
                            part.Part?.Name,
                            part.Part?.Code,
                            part.Quantity,
                            part.Price,
                            part.Quantity * part.Price
                        );
                    }

                    // Register data sources
                    report.RegisterData(dtRepairOrder, "RepairOrder");
                    report.RegisterData(dtServices, "Services");
                    report.RegisterData(dtParts, "Parts");

                    // Create QR code data with shop information
                    var qrData = new
                    {
                        OrderNumber = _repairOrder.OrderNumber,
                        CustomerName = customer?.name,
                        DeviceInfo = $"{device?.Type} {device?.Brand} {device?.Model}",
                        Status = statusDisplay,
                        TotalCost = _repairOrder.TotalCost,
                        PaidAmount = _repairOrder.PaidAmount,
                        RemainingAmount = _repairOrder.TotalCost - _repairOrder.PaidAmount,
                        PaymentStatus = paymentStatusDisplay,
                        CreatedAt = _repairOrder.CreatedAt.ToString("dd/MM/yyyy HH:mm"),
                        Id = _repairOrderId.ToString("38686fd5-5c56-415d-bb37-f9764233a77e")
                    };



                    // Set shop information
                    string shopInfo = "IRepairIT - Système de gestion de réparation\n" +
                                     "Vérifiez le statut de votre réparation en ligne\n" +
                                     "www.irepairit.com/status";

                    // Set report parameters
                    report.SetParameterValue("ShopInfo", shopInfo);

                    // Prepare and show the report
                    report.Prepare();

                    // Ask user if they want to print directly or preview
                    var result = KryptonMessageBox.Show(
                        "Voulez-vous imprimer directement le reçu?",
                        "Impression du reçu",
                        KryptonMessageBoxButtons.YesNo,
                        KryptonMessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Print directly
                        report.Print();
                    }
                    else
                    {
                        // Show preview
                        report.Show();
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'impression du reçu: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private string GetPaymentStatusDisplayName(PaymentStatus status)
        {
            switch (status)
            {
                case PaymentStatus.unpaid:
                    return "Non payé";
                case PaymentStatus.partially_paid:
                    return "Partiellement payé";
                case PaymentStatus.paid:
                    return "Payé";
                default:
                    return status.ToString();
            }
        }
    }
}
