﻿﻿using IRepairIT.Data.Common;
using IRepairIT.Models;
using IRepairIT.utilities;
using Krypton.Toolkit;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FDEVICES
{
    public partial class FRM_DEVICE : KryptonForm
    {
        private readonly DeviceCommands _deviceCmd;
        private readonly CustomerCommands _customerCmd;
        private Device _device;
        private bool _isEditMode = false;
        private List<string> _deviceTypes = new List<string>();
        private List<Customer> _customers = new List<Customer>();
        private string _imagesDirectory;
        private List<string> _uploadedImages = new List<string>();
        private List<DeviceImage> _existingImages = new List<DeviceImage>();

        public bool HasChanges { get; private set; }

        private int _deviceId = 0;

        public FRM_DEVICE(int deviceId = 0)
        {
            InitializeComponent();
            _deviceCmd = new DeviceCommands();
            _customerCmd = new CustomerCommands();

            if (deviceId > 0)
            {
                _isEditMode = true;
                _deviceId = deviceId;
            }
            else
            {
                _device = new Device();
            }

            // Create images directory if it doesn't exist
            _imagesDirectory = Path.Combine(Application.StartupPath, "Images", "Devices");
            if (!Directory.Exists(_imagesDirectory))
            {
                Directory.CreateDirectory(_imagesDirectory);
            }
        }

        private async void FRM_DEVICE_Load(object sender, EventArgs e)
        {
            try
            {
                // Set form title based on mode
                if (_isEditMode)
                {
                    this.Text = "Modifier un appareil";

                    // Load device data in edit mode
                    if (_deviceId > 0)
                    {
                        await LoadDevice(_deviceId);
                    }
                }
                else
                {
                    this.Text = "Ajouter un appareil";
                }

                // Load device types
                await LoadDeviceTypes();

                // Load customers
                await LoadCustomers();

                // Set date format for date picker
                dtpExpectedDelivery.Format = DateTimePickerFormat.Custom;
                dtpExpectedDelivery.CustomFormat = "dd/MM/yyyy";

                // Populate form with device data if in edit mode
                if (_isEditMode && _device != null)
                {
                    PopulateFormWithDeviceData();

                    // Load and display device images
                    await LoadDeviceImages();
                }
                else
                {
                    // Set default receipt date to today
                    dtpExpectedDelivery.Value = DateTime.Now.AddDays(3);
                }

                // Set up file upload control
                btnUploadImages.Click += BtnUploadImages_Click;
                upload_images_help.Text = "Formats supportés: JPG, PNG, GIF - Taille max: 5MB";
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du formulaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async Task LoadDeviceTypes()
        {
            try
            {
                // Get device types from database
                var types = await _deviceCmd.GetDeviceTypesAsync();
                _deviceTypes = types.ToList();

                // Add common device types if not already in the list
                var commonTypes = new[] { "Smartphone", "Tablette", "Ordinateur portable", "Ordinateur de bureau", "Console de jeu", "Autre" };
                foreach (var type in commonTypes)
                {
                    if (!_deviceTypes.Contains(type))
                    {
                        _deviceTypes.Add(type);
                    }
                }

                // Sort the list
                _deviceTypes.Sort();

                // Populate the combo box
                cmbDeviceType.Items.Clear();
                cmbDeviceType.Items.AddRange(_deviceTypes.ToArray());

                // Allow custom values
                cmbDeviceType.DropDownStyle = ComboBoxStyle.DropDown;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des types d'appareils: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async Task LoadCustomers()
        {
            try
            {
                // Get all customers
                var customers = await _customerCmd.GetALL("", 0, 1000);
                _customers = customers.ToList();

                // Populate the combo box
                cmbCustomer.Items.Clear();
                cmbCustomer.DisplayMember = "name";
                cmbCustomer.ValueMember = "id";
                cmbCustomer.DataSource = _customers;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des clients: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async Task LoadDevice(int deviceId)
        {
            try
            {
                _device = await _deviceCmd.GetByIdAsync(deviceId);
                if (_device == null)
                {
                    KryptonMessageBox.Show("Appareil non trouvé", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement de l'appareil: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                this.Close();
            }
        }

        private void PopulateFormWithDeviceData()
        {
            // Set customer
            for (int i = 0; i < cmbCustomer.Items.Count; i++)
            {
                var customer = (Customer)cmbCustomer.Items[i];
                if (customer.id == _device.CustomerId)
                {
                    cmbCustomer.SelectedIndex = i;
                    break;
                }
            }

            // Set device type
            if (!string.IsNullOrEmpty(_device.Type))
            {
                if (cmbDeviceType.Items.Contains(_device.Type))
                {
                    cmbDeviceType.SelectedItem = _device.Type;
                }
                else
                {
                    cmbDeviceType.Text = _device.Type;
                }
            }

            // Set other fields
            txtBrand.Text = _device.Brand;
            txtModel.Text = _device.Model;
            txtSerialNumber.Text = _device.SerialNumber;
            txtIMEI.Text = _device.IMEI;
            txtProblem.Text = _device.Problem;
            txtCondition.Text = _device.Condition;
            txtPassword.Text = _device.Password;
            txtAccessories.Text = _device.Accessories;

            // Set dates
            if (_device.ExpectedDeliveryDate.HasValue)
            {
                dtpExpectedDelivery.Value = _device.ExpectedDeliveryDate.Value;
            }
        }

        private async Task LoadDeviceImages()
        {
            try
            {
                Console.WriteLine($"Loading images for device ID: {_device.Id}");

                // Check if device ID is valid
                if (_device == null || _device.Id <= 0)
                {
                    Console.WriteLine("Invalid device ID");
                    return;
                }

                // Get device images
                var images = await _deviceCmd.GetDeviceImagesAsync(_device.Id);
                _existingImages = images.ToList();

                Console.WriteLine($"Found {_existingImages.Count} images for device ID: {_device.Id}");

                // Fix image paths if needed
                foreach (var img in _existingImages)
                {
                    Console.WriteLine($"Original Image ID: {img.Id}, Path: {img.ImagePath}");

                    // If path is empty or null, try to find the image by pattern
                    if (string.IsNullOrEmpty(img.ImagePath))
                    {
                        // Try to find image by pattern device_[deviceId]_*
                        string pattern = $"device_{_device.Id}_*";
                        string[] files = Directory.GetFiles(_imagesDirectory, pattern);

                        if (files.Length > 0)
                        {
                            // Use the first matching file
                            string absolutePath = files[0];
                            string fileName = Path.GetFileName(absolutePath);
                            string relativePath = Path.Combine("Images", "Devices", fileName);

                            img.ImagePath = relativePath;
                            Console.WriteLine($"Found image by pattern: {img.ImagePath}");

                            // Update the path in the database
                            await _deviceCmd.UpdateDeviceImagePathAsync(img.Id, relativePath);
                        }
                    }

                    // If path is not rooted (relative path), make sure it's properly formatted
                    if (!string.IsNullOrEmpty(img.ImagePath) && !Path.IsPathRooted(img.ImagePath))
                    {
                        // Check if path already contains Images/Devices
                        if (!img.ImagePath.Contains("Images" + Path.DirectorySeparatorChar + "Devices"))
                        {
                            // Extract filename only
                            string fileName = Path.GetFileName(img.ImagePath);
                            string newRelativePath = Path.Combine("Images", "Devices", fileName);

                            // Update path in memory
                            string oldPath = img.ImagePath;
                            img.ImagePath = newRelativePath;
                            Console.WriteLine($"Fixed relative path from {oldPath} to: {img.ImagePath}");

                            // Update the path in the database
                            await _deviceCmd.UpdateDeviceImagePathAsync(img.Id, newRelativePath);
                        }
                    }

                    // If path is absolute but file doesn't exist, try to find it in the images directory
                    if (!string.IsNullOrEmpty(img.ImagePath) && Path.IsPathRooted(img.ImagePath) && !File.Exists(img.ImagePath))
                    {
                        string fileName = Path.GetFileName(img.ImagePath);
                        string newAbsolutePath = Path.Combine(_imagesDirectory, fileName);

                        if (File.Exists(newAbsolutePath))
                        {
                            // Create relative path for database
                            string newRelativePath = Path.Combine("Images", "Devices", fileName);

                            // Update path in memory
                            string oldPath = img.ImagePath;
                            img.ImagePath = newRelativePath;
                            Console.WriteLine($"Found image at new location, updating from {oldPath} to: {newRelativePath}");

                            // Update the path in the database
                            await _deviceCmd.UpdateDeviceImagePathAsync(img.Id, newRelativePath);
                        }
                        else
                        {
                            // Try to find by pattern as a last resort
                            string pattern = $"device_{_device.Id}_*";
                            string[] files = Directory.GetFiles(_imagesDirectory, pattern);

                            if (files.Length > 0)
                            {
                                string foundAbsolutePath = files[0];
                                string foundFileName = Path.GetFileName(foundAbsolutePath);
                                string newRelativePath = Path.Combine("Images", "Devices", foundFileName);

                                // Update path in memory
                                string oldPath = img.ImagePath;
                                img.ImagePath = newRelativePath;
                                Console.WriteLine($"Found image by pattern after path fix, updating from {oldPath} to: {newRelativePath}");

                                // Update the path in the database
                                await _deviceCmd.UpdateDeviceImagePathAsync(img.Id, newRelativePath);
                            }
                        }
                    }

                    Console.WriteLine($"Final Image ID: {img.Id}, Path: {img.ImagePath}");
                }

                // Display images
                DisplayImages();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading device images: {ex.Message}");
                KryptonMessageBox.Show($"Erreur lors du chargement des images: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void DisplayImages()
        {
            try
            {
                // Clear the flow layout panel
                flowLayoutPanel1.Controls.Clear();

                // Add existing images
                foreach (var image in _existingImages)
                {
                    AddImageToPanel(image.ImagePath, image.Id);
                }

                // Add newly uploaded images
                foreach (var imagePath in _uploadedImages)
                {
                    AddImageToPanel(imagePath);
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'affichage des images: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void AddImageToPanel(string imagePath, int imageId = 0)
        {
            try
            {
                // Log image path for debugging
                Console.WriteLine($"Adding image: {imagePath}, ID: {imageId}");

                // Check if image path is valid
                if (string.IsNullOrEmpty(imagePath))
                {
                    Console.WriteLine("Image path is null or empty");
                    return;
                }

                // Convert relative path to absolute path if needed
                string absolutePath = imagePath;
                if (!Path.IsPathRooted(imagePath))
                {
                    absolutePath = Path.Combine(Application.StartupPath, imagePath);
                    Console.WriteLine($"Converted relative path to absolute path: {absolutePath}");
                }

                // Create a panel to hold the image and delete button
                Panel panel = new Panel
                {
                    Width = 150,
                    Height = 150,
                    Margin = new Padding(5),
                    BorderStyle = BorderStyle.FixedSingle
                };

                // Create a picture box for the image
                PictureBox pictureBox = new PictureBox
                {
                    Width = 140,
                    Height = 120,
                    SizeMode = PictureBoxSizeMode.Zoom,
                    Location = new Point(5, 5),
                    Tag = imagePath // Keep original path in tag
                };

                // Load the image
                if (File.Exists(absolutePath))
                {
                    try
                    {
                        using (var stream = new FileStream(absolutePath, FileMode.Open, FileAccess.Read))
                        {
                            pictureBox.Image = Image.FromStream(stream);
                        }
                    }
                    catch (IOException ioEx)
                    {
                        Console.WriteLine($"Error loading image: {ioEx.Message}");
                        // Use a placeholder image or show an error icon
                        pictureBox.Image = Properties.Resources.close_window;
                    }
                }
                else
                {
                    Console.WriteLine($"Image file not found: {absolutePath}");
                    // Use a placeholder image or show an error icon
                    pictureBox.Image = Properties.Resources.close_window;
                }

                // Create a delete button
                Button deleteButton = new Button
                {
                    Text = "Supprimer",
                    Width = 140,
                    Height = 25,
                    Location = new Point(5, 125),
                    Tag = imageId > 0 ? (object)imageId : (object)imagePath
                };

                // Add click event to delete button
                deleteButton.Click += (s, e) =>
                {
                    if (imageId > 0)
                    {
                        // Existing image
                        DeleteExistingImage(imageId, panel);
                    }
                    else
                    {
                        // Newly uploaded image
                        DeleteUploadedImage(imagePath, panel);
                    }
                };

                // Add controls to panel
                panel.Controls.Add(pictureBox);
                panel.Controls.Add(deleteButton);

                // Add panel to flow layout panel
                flowLayoutPanel1.Controls.Add(panel);
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'ajout de l'image: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void DeleteExistingImage(int imageId, Panel panel)
        {
            try
            {
                var result = KryptonMessageBox.Show("Êtes-vous sûr de vouloir supprimer cette image?", "Confirmation",
                    KryptonMessageBoxButtons.YesNo, KryptonMessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Find the image path before deleting from database
                    var imageToDelete = _existingImages.FirstOrDefault(i => i.Id == imageId);
                    string imagePath = imageToDelete?.ImagePath;

                    // Delete from database
                    bool success = await _deviceCmd.DeleteDeviceImageAsync(imageId);
                    if (success)
                    {
                        // Try to delete the file from disk
                        if (!string.IsNullOrEmpty(imagePath))
                        {
                            try
                            {
                                // Convert to absolute path if needed
                                string absolutePath = imagePath;
                                if (!Path.IsPathRooted(imagePath))
                                {
                                    absolutePath = Path.Combine(Application.StartupPath, imagePath);
                                }

                                if (File.Exists(absolutePath))
                                {
                                    Console.WriteLine($"Deleting image file: {absolutePath}");
                                    File.Delete(absolutePath);
                                }
                                else
                                {
                                    Console.WriteLine($"Image file not found: {absolutePath}");
                                }
                            }
                            catch (Exception fileEx)
                            {
                                Console.WriteLine($"Error deleting image file: {fileEx.Message}");
                                // Continue even if file deletion fails
                            }
                        }

                        // Remove from list
                        _existingImages.RemoveAll(i => i.Id == imageId);

                        // Remove from panel
                        flowLayoutPanel1.Controls.Remove(panel);
                        panel.Dispose();

                        HasChanges = true;
                    }
                    else
                    {
                        KryptonMessageBox.Show("Erreur lors de la suppression de l'image", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting image: {ex.Message}");
                KryptonMessageBox.Show($"Erreur lors de la suppression de l'image: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void DeleteUploadedImage(string imagePath, Panel panel)
        {
            try
            {
                var result = KryptonMessageBox.Show("Êtes-vous sûr de vouloir supprimer cette image?", "Confirmation",
                    KryptonMessageBoxButtons.YesNo, KryptonMessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Remove from list
                    _uploadedImages.Remove(imagePath);

                    // Remove from panel
                    flowLayoutPanel1.Controls.Remove(panel);
                    panel.Dispose();

                    // Convert to absolute path if needed
                    string absolutePath = imagePath;
                    if (!Path.IsPathRooted(imagePath))
                    {
                        absolutePath = Path.Combine(Application.StartupPath, imagePath);
                    }

                    // Delete file if it's a temporary file
                    if (File.Exists(absolutePath) && (imagePath.Contains("temp_") || absolutePath.Contains("temp_")))
                    {
                        Console.WriteLine($"Deleting file: {absolutePath}");
                        File.Delete(absolutePath);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting image: {ex.Message}");
                KryptonMessageBox.Show($"Erreur lors de la suppression de l'image: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void BtnUploadImages_Click(object sender, EventArgs e)
        {
            try
            {
                // Create OpenFileDialog
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Filter = "Images (*.jpg;*.jpeg;*.png;*.gif)|*.jpg;*.jpeg;*.png;*.gif",
                    Multiselect = true,
                    Title = "Sélectionner des images"
                };

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    foreach (string fileName in openFileDialog.FileNames)
                    {
                        // Check file size (max 5MB)
                        FileInfo fileInfo = new FileInfo(fileName);
                        if (fileInfo.Length > 5 * 1024 * 1024)
                        {
                            KryptonMessageBox.Show($"L'image {fileInfo.Name} est trop grande (max 5MB)", "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                            continue;
                        }

                        // Create a temporary file
                        string tempFileName = $"temp_{Guid.NewGuid()}{fileInfo.Extension}";
                        string tempFilePath = Path.Combine(_imagesDirectory, tempFileName);

                        // Copy the file
                        File.Copy(fileName, tempFilePath);

                        // Add to list
                        _uploadedImages.Add(tempFilePath);
                    }

                    // Display images
                    DisplayImages();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du téléchargement des images: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void BtnWebcamCapture_Click(object sender, EventArgs e)
        {
            try
            {
                // Check if webcams are available using the static method
                if (!WebcamCapture.IsWebcamAvailable())
                {
                    KryptonMessageBox.Show("Aucune webcam n'a été détectée sur cet ordinateur.", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                // Open webcam capture form
                var webcamForm = new FRM_WEBCAM_CAPTURE();

                if (webcamForm.ShowDialog() == DialogResult.OK)
                {
                    if (webcamForm.HasCapturedImage && !string.IsNullOrEmpty(webcamForm.CapturedImagePath))
                    {
                        // Add captured image to list
                        _uploadedImages.Add(webcamForm.CapturedImagePath);

                        // Display images
                        DisplayImages();
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la capture d'image: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void BtnAddCustomer_Click(object sender, EventArgs e)
        {
            try
            {
                // Open customer form
                var frm = new FCUSTOMERS.FRM_CUSTOMER();
                frm.FormClosed += (s, args) =>
                {
                    if (frm.HasChanges)
                    {
                        // Reload customers
                        _ = LoadCustomers();
                    }
                };
                frm.ShowDialog();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'ouverture du formulaire client: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate form
                if (!ValidateForm())
                {
                    return;
                }

                // Get data from form
                GetDataFromForm();

                // Save device
                if (_isEditMode)
                {
                    bool success = await _deviceCmd.UpdateAsync(_device);
                    if (success)
                    {
                        HasChanges = true;
                    }
                    else
                    {
                        KryptonMessageBox.Show("Erreur lors de la mise à jour de l'appareil", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return;
                    }
                }
                else
                {
                    int deviceId = await _deviceCmd.InsertAsync(_device);
                    if (deviceId > 0)
                    {
                        _device.Id = deviceId;
                        _isEditMode = true;
                        HasChanges = true;
                    }
                    else
                    {
                        KryptonMessageBox.Show("Erreur lors de l'ajout de l'appareil", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return;
                    }
                }

                // Save uploaded images
                await SaveUploadedImages();

                KryptonMessageBox.Show("Appareil enregistré avec succès", "Succès",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                // Close form if not in edit mode
                if (!_isEditMode)
                {
                    this.Close();
                }
                else
                {
                    // Reload device data
                    await LoadDevice(_device.Id);
                    PopulateFormWithDeviceData();
                    await LoadDeviceImages();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'enregistrement de l'appareil: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private bool ValidateForm()
        {
            // Check required fields
            if (cmbCustomer.SelectedItem == null)
            {
                KryptonMessageBox.Show("Veuillez sélectionner un client", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                cmbCustomer.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(cmbDeviceType.Text))
            {
                KryptonMessageBox.Show("Veuillez sélectionner ou saisir un type d'appareil", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                cmbDeviceType.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtBrand.Text))
            {
                KryptonMessageBox.Show("Veuillez saisir la marque de l'appareil", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                txtBrand.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtModel.Text))
            {
                KryptonMessageBox.Show("Veuillez saisir le modèle de l'appareil", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                txtModel.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtProblem.Text))
            {
                KryptonMessageBox.Show("Veuillez décrire le problème de l'appareil", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                txtProblem.Focus();
                return false;
            }

            return true;
        }

        private void GetDataFromForm()
        {
            // Get customer ID
            if (cmbCustomer.SelectedItem != null)
            {
                _device.CustomerId = ((Customer)cmbCustomer.SelectedItem).id;
            }

            // Get device type
            _device.Type = cmbDeviceType.Text;

            // Get other fields
            _device.Brand = txtBrand.Text;
            _device.Model = txtModel.Text;
            _device.SerialNumber = txtSerialNumber.Text;
            _device.IMEI = txtIMEI.Text;
            _device.Problem = txtProblem.Text;
            _device.Condition = txtCondition.Text;
            _device.Password = txtPassword.Text;
            _device.Accessories = txtAccessories.Text;

            // Get dates
            _device.ExpectedDeliveryDate = dtpExpectedDelivery.Value;
        }

        private async Task SaveUploadedImages()
        {
            try
            {
                Console.WriteLine($"Saving {_uploadedImages.Count} uploaded images for device ID: {_device.Id}");

                foreach (string tempFilePath in _uploadedImages)
                {
                    // Create a permanent file name
                    string fileName = $"device_{_device.Id}_{Guid.NewGuid()}{Path.GetExtension(tempFilePath)}";
                    string filePath = Path.Combine(_imagesDirectory, fileName);

                    Console.WriteLine($"Moving image from {tempFilePath} to {filePath}");

                    // Move the file
                    if (File.Exists(tempFilePath))
                    {
                        File.Move(tempFilePath, filePath);

                        // Save to database - use relative path for storage
                        string relativePath = Path.Combine("Images", "Devices", fileName);
                        Console.WriteLine($"Saving image with relative path: {relativePath}");

                        var deviceImage = new DeviceImage
                        {
                            DeviceId = _device.Id,
                            ImagePath = relativePath
                        };

                        await _deviceCmd.SaveDeviceImageAsync(deviceImage);
                    }
                    else
                    {
                        Console.WriteLine($"Temp file not found: {tempFilePath}");
                    }
                }

                // Clear the list
                _uploadedImages.Clear();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving uploaded images: {ex.Message}");
                KryptonMessageBox.Show($"Erreur lors de l'enregistrement des images: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            // Delete temporary files
            foreach (string tempFilePath in _uploadedImages)
            {
                if (File.Exists(tempFilePath) && tempFilePath.Contains("temp_"))
                {
                    try
                    {
                        File.Delete(tempFilePath);
                    }
                    catch
                    {
                        // Ignore errors
                    }
                }
            }

            this.Close();
        }
    }
}
