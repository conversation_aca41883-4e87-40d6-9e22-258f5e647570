﻿namespace IRepairIT.FRMS.FDB
{
    partial class FRM_SETUPDB
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.kryptonHeader1 = new Krypton.Toolkit.KryptonHeader();
            this.btnClose = new Krypton.Toolkit.ButtonSpecAny();
            this.panelHeader = new Krypton.Toolkit.KryptonPanel();
            this.lblStepDescription = new Krypton.Toolkit.KryptonLabel();
            this.lblStepTitle = new Krypton.Toolkit.KryptonLabel();
            this.panelFooter = new Krypton.Toolkit.KryptonPanel();
            this.kryptonProgressBar1 = new Krypton.Toolkit.KryptonProgressBar();
            this.btnCancel = new Krypton.Toolkit.KryptonButton();
            this.btnFinish = new Krypton.Toolkit.KryptonButton();
            this.btnNext = new Krypton.Toolkit.KryptonButton();
            this.btnBack = new Krypton.Toolkit.KryptonButton();
            this.panelContent = new Krypton.Toolkit.KryptonPanel();
            this.panelStep2 = new Krypton.Toolkit.KryptonPanel();
            this.kryptonGroupBox1 = new Krypton.Toolkit.KryptonGroupBox();
            this.pictureBoxLogo = new Krypton.Toolkit.KryptonPictureBox();
            this.kryptonButton1 = new Krypton.Toolkit.KryptonButton();
            this.txtWebsite = new Krypton.Toolkit.KryptonTextBox();
            this.txtEmail = new Krypton.Toolkit.KryptonTextBox();
            this.txtPhone = new Krypton.Toolkit.KryptonTextBox();
            this.txtAddress = new Krypton.Toolkit.KryptonTextBox();
            this.txtActivity = new Krypton.Toolkit.KryptonTextBox();
            this.txtShopName = new Krypton.Toolkit.KryptonTextBox();
            this.kryptonLabel7 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel6 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel5 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel4 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel2 = new Krypton.Toolkit.KryptonLabel();
            this.kryptonLabel1 = new Krypton.Toolkit.KryptonLabel();
            this.panelStep1 = new Krypton.Toolkit.KryptonPanel();
            this.numYear = new Krypton.Toolkit.KryptonNumericUpDown();
            this.kryptonLabel3 = new Krypton.Toolkit.KryptonLabel();
            this.errorProvider1 = new System.Windows.Forms.ErrorProvider(this.components);
            ((System.ComponentModel.ISupportInitialize)(this.panelHeader)).BeginInit();
            this.panelHeader.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelFooter)).BeginInit();
            this.panelFooter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelContent)).BeginInit();
            this.panelContent.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelStep2)).BeginInit();
            this.panelStep2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1.Panel)).BeginInit();
            this.kryptonGroupBox1.Panel.SuspendLayout();
            this.kryptonGroupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxLogo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelStep1)).BeginInit();
            this.panelStep1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.errorProvider1)).BeginInit();
            this.SuspendLayout();
            // 
            // kryptonHeader1
            // 
            this.kryptonHeader1.ButtonSpecs.Add(this.btnClose);
            this.kryptonHeader1.Dock = System.Windows.Forms.DockStyle.Top;
            this.kryptonHeader1.Location = new System.Drawing.Point(0, 0);
            this.kryptonHeader1.Name = "kryptonHeader1";
            this.kryptonHeader1.Size = new System.Drawing.Size(639, 34);
            this.kryptonHeader1.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 14.25F, System.Drawing.FontStyle.Bold);
            this.kryptonHeader1.TabIndex = 1;
            this.kryptonHeader1.Values.Description = "";
            this.kryptonHeader1.Values.Heading = "Gestionnaire de Dossiers";
            this.kryptonHeader1.Values.Image = global::IRepairIT.Properties.Resources.folder;
            // 
            // btnClose
            // 
            this.btnClose.Image = global::IRepairIT.Properties.Resources.close_window;
            this.btnClose.Style = Krypton.Toolkit.PaletteButtonStyle.Alternate;
            this.btnClose.Text = "Fermer";
            this.btnClose.UniqueName = "58ebb0e87a444370b7b010bf7556bd39";
            this.btnClose.Click += new System.EventHandler(this.btnClose_Click);
            // 
            // panelHeader
            // 
            this.panelHeader.Controls.Add(this.lblStepDescription);
            this.panelHeader.Controls.Add(this.lblStepTitle);
            this.panelHeader.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelHeader.Location = new System.Drawing.Point(0, 34);
            this.panelHeader.Name = "panelHeader";
            this.panelHeader.Size = new System.Drawing.Size(639, 71);
            this.panelHeader.TabIndex = 0;
            // 
            // lblStepDescription
            // 
            this.lblStepDescription.Location = new System.Drawing.Point(12, 42);
            this.lblStepDescription.Name = "lblStepDescription";
            this.lblStepDescription.Size = new System.Drawing.Size(254, 20);
            this.lblStepDescription.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblStepDescription.TabIndex = 1;
            this.lblStepDescription.Values.Text = "Veuillez sélectionner l\'année pour ce dossier.";
            // 
            // lblStepTitle
            // 
            this.lblStepTitle.Location = new System.Drawing.Point(12, 6);
            this.lblStepTitle.Name = "lblStepTitle";
            this.lblStepTitle.Size = new System.Drawing.Size(252, 30);
            this.lblStepTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14.25F, System.Drawing.FontStyle.Bold);
            this.lblStepTitle.TabIndex = 0;
            this.lblStepTitle.Values.Text = "Étape 1: Année du Dossier";
            // 
            // panelFooter
            // 
            this.panelFooter.Controls.Add(this.kryptonProgressBar1);
            this.panelFooter.Controls.Add(this.btnCancel);
            this.panelFooter.Controls.Add(this.btnFinish);
            this.panelFooter.Controls.Add(this.btnNext);
            this.panelFooter.Controls.Add(this.btnBack);
            this.panelFooter.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelFooter.Location = new System.Drawing.Point(0, 332);
            this.panelFooter.Name = "panelFooter";
            this.panelFooter.Size = new System.Drawing.Size(639, 58);
            this.panelFooter.TabIndex = 3;
            // 
            // kryptonProgressBar1
            // 
            this.kryptonProgressBar1.Location = new System.Drawing.Point(140, 27);
            this.kryptonProgressBar1.Name = "kryptonProgressBar1";
            this.kryptonProgressBar1.Size = new System.Drawing.Size(246, 10);
            this.kryptonProgressBar1.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.kryptonProgressBar1.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.kryptonProgressBar1.StateNormal.Back.Color1 = System.Drawing.Color.Transparent;
            this.kryptonProgressBar1.StateNormal.Back.Color2 = System.Drawing.Color.Transparent;
            this.kryptonProgressBar1.StateNormal.Back.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.kryptonProgressBar1.TabIndex = 17;
            this.kryptonProgressBar1.Values.Text = "";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnCancel.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnCancel.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnCancel.Location = new System.Drawing.Point(12, 12);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(124, 35);
            this.btnCancel.StateCommon.Content.Image.ImageH = Krypton.Toolkit.PaletteRelativeAlign.Far;
            this.btnCancel.StateCommon.Content.Image.ImageV = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.btnCancel.TabIndex = 16;
            this.btnCancel.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnCancel.Values.Text = "Annuler";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnFinish
            // 
            this.btnFinish.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnFinish.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnFinish.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnFinish.Location = new System.Drawing.Point(519, 12);
            this.btnFinish.Name = "btnFinish";
            this.btnFinish.Size = new System.Drawing.Size(108, 35);
            this.btnFinish.StateCommon.Content.Image.ImageH = Krypton.Toolkit.PaletteRelativeAlign.Far;
            this.btnFinish.StateCommon.Content.Image.ImageV = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.btnFinish.TabIndex = 15;
            this.btnFinish.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnFinish.Values.Text = "Terminer";
            this.btnFinish.Click += new System.EventHandler(this.btnFinish_Click);
            // 
            // btnNext
            // 
            this.btnNext.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnNext.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnNext.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnNext.Location = new System.Drawing.Point(519, 12);
            this.btnNext.Name = "btnNext";
            this.btnNext.Size = new System.Drawing.Size(108, 35);
            this.btnNext.StateCommon.Content.Image.ImageH = Krypton.Toolkit.PaletteRelativeAlign.Far;
            this.btnNext.StateCommon.Content.Image.ImageV = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.btnNext.TabIndex = 14;
            this.btnNext.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnNext.Values.Text = "Suivant >";
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnBack
            // 
            this.btnBack.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnBack.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnBack.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnBack.Location = new System.Drawing.Point(405, 12);
            this.btnBack.Name = "btnBack";
            this.btnBack.Size = new System.Drawing.Size(108, 35);
            this.btnBack.StateCommon.Content.Image.ImageH = Krypton.Toolkit.PaletteRelativeAlign.Far;
            this.btnBack.StateCommon.Content.Image.ImageV = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.btnBack.TabIndex = 13;
            this.btnBack.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnBack.Values.Text = "< Retour";
            this.btnBack.Click += new System.EventHandler(this.btnBack_Click);
            // 
            // panelContent
            // 
            this.panelContent.Controls.Add(this.panelStep2);
            this.panelContent.Controls.Add(this.panelStep1);
            this.panelContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelContent.Location = new System.Drawing.Point(0, 105);
            this.panelContent.Name = "panelContent";
            this.panelContent.Size = new System.Drawing.Size(639, 227);
            this.panelContent.TabIndex = 4;
            // 
            // panelStep2
            // 
            this.panelStep2.Controls.Add(this.kryptonGroupBox1);
            this.panelStep2.Controls.Add(this.txtWebsite);
            this.panelStep2.Controls.Add(this.txtEmail);
            this.panelStep2.Controls.Add(this.txtPhone);
            this.panelStep2.Controls.Add(this.txtAddress);
            this.panelStep2.Controls.Add(this.txtActivity);
            this.panelStep2.Controls.Add(this.txtShopName);
            this.panelStep2.Controls.Add(this.kryptonLabel7);
            this.panelStep2.Controls.Add(this.kryptonLabel6);
            this.panelStep2.Controls.Add(this.kryptonLabel5);
            this.panelStep2.Controls.Add(this.kryptonLabel4);
            this.panelStep2.Controls.Add(this.kryptonLabel2);
            this.panelStep2.Controls.Add(this.kryptonLabel1);
            this.panelStep2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelStep2.Location = new System.Drawing.Point(0, 0);
            this.panelStep2.Name = "panelStep2";
            this.panelStep2.Size = new System.Drawing.Size(639, 227);
            this.panelStep2.StateNormal.Color2 = System.Drawing.Color.Black;
            this.panelStep2.TabIndex = 0;
            // 
            // kryptonGroupBox1
            // 
            this.kryptonGroupBox1.Location = new System.Drawing.Point(503, 17);
            this.kryptonGroupBox1.Name = "kryptonGroupBox1";
            // 
            // kryptonGroupBox1.Panel
            // 
            this.kryptonGroupBox1.Panel.Controls.Add(this.pictureBoxLogo);
            this.kryptonGroupBox1.Panel.Controls.Add(this.kryptonButton1);
            this.kryptonGroupBox1.Size = new System.Drawing.Size(124, 193);
            this.kryptonGroupBox1.TabIndex = 7;
            this.kryptonGroupBox1.Values.Heading = "";
            // 
            // pictureBoxLogo
            // 
            this.pictureBoxLogo.Location = new System.Drawing.Point(5, 3);
            this.pictureBoxLogo.Name = "pictureBoxLogo";
            this.pictureBoxLogo.Size = new System.Drawing.Size(111, 147);
            this.pictureBoxLogo.TabIndex = 6;
            this.pictureBoxLogo.TabStop = false;
            this.pictureBoxLogo.DragDrop += new System.Windows.Forms.DragEventHandler(this.pictureBoxLogo_DragDrop);
            this.pictureBoxLogo.DragEnter += new System.Windows.Forms.DragEventHandler(this.pictureBoxLogo_DragEnter);
            // 
            // kryptonButton1
            // 
            this.kryptonButton1.Location = new System.Drawing.Point(3, 158);
            this.kryptonButton1.Name = "kryptonButton1";
            this.kryptonButton1.Size = new System.Drawing.Size(111, 25);
            this.kryptonButton1.TabIndex = 6;
            this.kryptonButton1.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.kryptonButton1.Values.Text = "Parcourir...";
            this.kryptonButton1.Click += new System.EventHandler(this.kryptonButton1_Click);
            // 
            // txtWebsite
            // 
            this.txtWebsite.Location = new System.Drawing.Point(168, 179);
            this.txtWebsite.Name = "txtWebsite";
            this.txtWebsite.Size = new System.Drawing.Size(329, 25);
            this.txtWebsite.TabIndex = 5;
            // 
            // txtEmail
            // 
            this.txtEmail.Location = new System.Drawing.Point(168, 148);
            this.txtEmail.Name = "txtEmail";
            this.txtEmail.Size = new System.Drawing.Size(329, 25);
            this.txtEmail.TabIndex = 4;
            // 
            // txtPhone
            // 
            this.txtPhone.Location = new System.Drawing.Point(168, 117);
            this.txtPhone.Name = "txtPhone";
            this.txtPhone.Size = new System.Drawing.Size(329, 25);
            this.txtPhone.TabIndex = 3;
            // 
            // txtAddress
            // 
            this.txtAddress.Location = new System.Drawing.Point(168, 86);
            this.txtAddress.Name = "txtAddress";
            this.txtAddress.Size = new System.Drawing.Size(329, 25);
            this.txtAddress.TabIndex = 2;
            // 
            // txtActivity
            // 
            this.txtActivity.Location = new System.Drawing.Point(168, 55);
            this.txtActivity.Name = "txtActivity";
            this.txtActivity.Size = new System.Drawing.Size(329, 25);
            this.txtActivity.TabIndex = 1;
            // 
            // txtShopName
            // 
            this.txtShopName.Location = new System.Drawing.Point(168, 24);
            this.txtShopName.Name = "txtShopName";
            this.txtShopName.Size = new System.Drawing.Size(329, 25);
            this.txtShopName.TabIndex = 0;
            // 
            // kryptonLabel7
            // 
            this.kryptonLabel7.Location = new System.Drawing.Point(25, 181);
            this.kryptonLabel7.Name = "kryptonLabel7";
            this.kryptonLabel7.Size = new System.Drawing.Size(72, 21);
            this.kryptonLabel7.TabIndex = 0;
            this.kryptonLabel7.Values.Text = "Site Web :";
            // 
            // kryptonLabel6
            // 
            this.kryptonLabel6.Location = new System.Drawing.Point(25, 150);
            this.kryptonLabel6.Name = "kryptonLabel6";
            this.kryptonLabel6.Size = new System.Drawing.Size(51, 21);
            this.kryptonLabel6.TabIndex = 0;
            this.kryptonLabel6.Values.Text = "Email :";
            // 
            // kryptonLabel5
            // 
            this.kryptonLabel5.Location = new System.Drawing.Point(25, 119);
            this.kryptonLabel5.Name = "kryptonLabel5";
            this.kryptonLabel5.Size = new System.Drawing.Size(82, 21);
            this.kryptonLabel5.TabIndex = 0;
            this.kryptonLabel5.Values.Text = "Téléphone :";
            // 
            // kryptonLabel4
            // 
            this.kryptonLabel4.Location = new System.Drawing.Point(25, 88);
            this.kryptonLabel4.Name = "kryptonLabel4";
            this.kryptonLabel4.Size = new System.Drawing.Size(66, 21);
            this.kryptonLabel4.TabIndex = 0;
            this.kryptonLabel4.Values.Text = "Adresse :";
            // 
            // kryptonLabel2
            // 
            this.kryptonLabel2.Location = new System.Drawing.Point(25, 57);
            this.kryptonLabel2.Name = "kryptonLabel2";
            this.kryptonLabel2.Size = new System.Drawing.Size(64, 21);
            this.kryptonLabel2.TabIndex = 0;
            this.kryptonLabel2.Values.Text = "Activité :";
            // 
            // kryptonLabel1
            // 
            this.kryptonLabel1.Location = new System.Drawing.Point(25, 26);
            this.kryptonLabel1.Name = "kryptonLabel1";
            this.kryptonLabel1.Size = new System.Drawing.Size(139, 21);
            this.kryptonLabel1.TabIndex = 0;
            this.kryptonLabel1.Values.Text = "Nom de l\'entreprise :";
            // 
            // panelStep1
            // 
            this.panelStep1.Controls.Add(this.numYear);
            this.panelStep1.Controls.Add(this.kryptonLabel3);
            this.panelStep1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelStep1.Location = new System.Drawing.Point(0, 0);
            this.panelStep1.Name = "panelStep1";
            this.panelStep1.Size = new System.Drawing.Size(639, 227);
            this.panelStep1.TabIndex = 0;
            // 
            // numYear
            // 
            this.numYear.Increment = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numYear.Location = new System.Drawing.Point(322, 101);
            this.numYear.Maximum = new decimal(new int[] {
            2100,
            0,
            0,
            0});
            this.numYear.Minimum = new decimal(new int[] {
            2000,
            0,
            0,
            0});
            this.numYear.Name = "numYear";
            this.numYear.Size = new System.Drawing.Size(120, 24);
            this.numYear.TabIndex = 1;
            this.numYear.Value = new decimal(new int[] {
            2023,
            0,
            0,
            0});
            // 
            // kryptonLabel3
            // 
            this.kryptonLabel3.Location = new System.Drawing.Point(197, 103);
            this.kryptonLabel3.Name = "kryptonLabel3";
            this.kryptonLabel3.Size = new System.Drawing.Size(119, 21);
            this.kryptonLabel3.TabIndex = 0;
            this.kryptonLabel3.Values.Text = "Année du dossier:";
            // 
            // errorProvider1
            // 
            this.errorProvider1.ContainerControl = this;
            // 
            // FRM_SETUPDB
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(639, 390);
            this.Controls.Add(this.panelContent);
            this.Controls.Add(this.panelFooter);
            this.Controls.Add(this.panelHeader);
            this.Controls.Add(this.kryptonHeader1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Name = "FRM_SETUPDB";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Gestionnaire de Dossiers";
            ((System.ComponentModel.ISupportInitialize)(this.panelHeader)).EndInit();
            this.panelHeader.ResumeLayout(false);
            this.panelHeader.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelFooter)).EndInit();
            this.panelFooter.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelContent)).EndInit();
            this.panelContent.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelStep2)).EndInit();
            this.panelStep2.ResumeLayout(false);
            this.panelStep2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1.Panel)).EndInit();
            this.kryptonGroupBox1.Panel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonGroupBox1)).EndInit();
            this.kryptonGroupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxLogo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelStep1)).EndInit();
            this.panelStep1.ResumeLayout(false);
            this.panelStep1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.errorProvider1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Krypton.Toolkit.KryptonHeader kryptonHeader1;
        private Krypton.Toolkit.ButtonSpecAny btnClose;
        private Krypton.Toolkit.KryptonPanel panelHeader;
        private Krypton.Toolkit.KryptonPanel panelFooter;
        private Krypton.Toolkit.KryptonPanel panelContent;
        private Krypton.Toolkit.KryptonLabel lblStepDescription;
        private Krypton.Toolkit.KryptonLabel lblStepTitle;
        private Krypton.Toolkit.KryptonNumericUpDown numYear;
        private Krypton.Toolkit.KryptonLabel kryptonLabel3;
        private Krypton.Toolkit.KryptonPanel panelStep1;
        private Krypton.Toolkit.KryptonPanel panelStep2;
        private Krypton.Toolkit.KryptonButton btnCancel;
        private Krypton.Toolkit.KryptonButton btnFinish;
        private Krypton.Toolkit.KryptonButton btnNext;
        private Krypton.Toolkit.KryptonButton btnBack;
        private Krypton.Toolkit.KryptonProgressBar kryptonProgressBar1;
        private Krypton.Toolkit.KryptonLabel kryptonLabel7;
        private Krypton.Toolkit.KryptonLabel kryptonLabel6;
        private Krypton.Toolkit.KryptonLabel kryptonLabel5;
        private Krypton.Toolkit.KryptonLabel kryptonLabel4;
        private Krypton.Toolkit.KryptonLabel kryptonLabel2;
        private Krypton.Toolkit.KryptonLabel kryptonLabel1;
        private Krypton.Toolkit.KryptonTextBox txtWebsite;
        private Krypton.Toolkit.KryptonTextBox txtEmail;
        private Krypton.Toolkit.KryptonTextBox txtPhone;
        private Krypton.Toolkit.KryptonTextBox txtAddress;
        private Krypton.Toolkit.KryptonTextBox txtActivity;
        private Krypton.Toolkit.KryptonTextBox txtShopName;
        private Krypton.Toolkit.KryptonPictureBox pictureBoxLogo;
        private Krypton.Toolkit.KryptonButton kryptonButton1;
        private System.Windows.Forms.ErrorProvider errorProvider1;
        private Krypton.Toolkit.KryptonGroupBox kryptonGroupBox1;
    }
}