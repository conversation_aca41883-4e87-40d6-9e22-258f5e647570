﻿using IRepairIT.Models.DTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IRepairIT.Data.Common
{
    public class DashboardCommands
    {
        private readonly DataAccess _db;
        public DashboardCommands()
        {
            _db = new DataAccess();
        }

        public async Task<int> CustomersCount()
        {
            var count = await _db.CountQuery("customers");
            return count;
        }
        public async Task<int> DevicesCount()
        {
            var count = await _db.CountQuery("devices");
            return count;
        }
        public async Task<int> OrdersCount()
        {
            var count = await _db.CountQuery("repair_orders");
            return count;
        }
        public async Task<int> ActiveOrdersCount()
        {
            var count = await _db.CountQuery("repair_orders WHERE status IN ('received', 'diagnosed', 'waiting_parts', 'in_progress')");
            return count;
        }
        public async Task<int> CompletedOrdersCount()
        {
            var count = await _db.CountQuery("repair_orders WHERE status IN ('repaired', 'ready', 'delivered')");
            return count;
        }
        public async Task<decimal> TotalSales()
        {
            var count = await _db.CountCustomQuery("SELECT SUM(total_cost) as total FROM repair_orders");
            return count;
        }
        public async Task<decimal> TotalPaid()
        {
            var count = await _db.CountCustomQuery("SELECT SUM(paid_amount) as total FROM repair_orders");
            return count;
        }
        public async Task<decimal> TotalDebts()
        {
            var count = await _db.CountCustomQuery("SELECT SUM(amount - paid_amount) as total FROM debts WHERE status != 'paid'");
            return count;
        }
        public async Task<int> LowStock()
        {
            var count = await _db.CountQuery("parts WHERE quantity <= min_quantity");
            return count;
        }

        public async Task<IEnumerable<LatestDto>> LatestOrders()
        {
            const string sql = @"SELECT ro.id,ro.order_number as NOrder, c.name as Client, d.brand + ' '+ d.model as Appareil,ro.status 
            FROM repair_orders ro 
            JOIN customers c ON ro.customer_id = c.id 
            JOIN devices d ON ro.device_id = d.id 
            ORDER BY ro.created_at DESC 
            LIMIT 5";
            return await _db.QueryQuery<LatestDto>(sql);
        }
        public async Task<IEnumerable<LatestCustomerDto>> LatestCustomers()
        {
            const string sql = @"select id,name,phone,created_at from customers";
            return await _db.QueryQuery<LatestCustomerDto>(sql);
        }
    }
}
