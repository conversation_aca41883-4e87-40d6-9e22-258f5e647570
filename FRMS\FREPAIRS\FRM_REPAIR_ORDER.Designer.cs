namespace IRepairIT.FRMS.FREPAIRS
{
    partial class FRM_REPAIR_ORDER
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.kryptonPanel1 = new Krypton.Toolkit.KryptonPanel();
            this.btnCancel = new Krypton.Toolkit.KryptonButton();
            this.btnSaveAndPay = new Krypton.Toolkit.KryptonButton();
            this.btnSave = new Krypton.Toolkit.KryptonButton();
            this.grpPaymentInfo = new Krypton.Toolkit.KryptonGroupBox();
            this.lblRemainingValue = new Krypton.Toolkit.KryptonLabel();
            this.lblRemainingAmount = new Krypton.Toolkit.KryptonLabel();
            this.cmbPaymentMethod = new Krypton.Toolkit.KryptonComboBox();
            this.lblPaymentMethod = new Krypton.Toolkit.KryptonLabel();
            this.txtPaidAmount = new Krypton.Toolkit.KryptonTextBox();
            this.lblPaidAmount = new Krypton.Toolkit.KryptonLabel();
            this.lblTotalValue = new Krypton.Toolkit.KryptonLabel();
            this.lblTotalCost = new Krypton.Toolkit.KryptonLabel();
            this.grpServicesAndParts = new Krypton.Toolkit.KryptonGroupBox();
            this.dgvParts = new Krypton.Toolkit.KryptonDataGridView();
            this.btnAddPart = new Krypton.Toolkit.KryptonButton();
            this.lblParts = new Krypton.Toolkit.KryptonLabel();
            this.dgvServices = new Krypton.Toolkit.KryptonDataGridView();
            this.btnAddService = new Krypton.Toolkit.KryptonButton();
            this.lblServices = new Krypton.Toolkit.KryptonLabel();
            this.grpTechInfo = new Krypton.Toolkit.KryptonGroupBox();
            this.txtTechNotes = new Krypton.Toolkit.KryptonTextBox();
            this.lblTechNotes = new Krypton.Toolkit.KryptonLabel();
            this.txtWarrantyPeriod = new Krypton.Toolkit.KryptonTextBox();
            this.lblWarrantyPeriod = new Krypton.Toolkit.KryptonLabel();
            this.cmbTechnician = new Krypton.Toolkit.KryptonComboBox();
            this.lblTechnician = new Krypton.Toolkit.KryptonLabel();
            this.grpCustomerDevice = new Krypton.Toolkit.KryptonGroupBox();
            this.txtProblem = new Krypton.Toolkit.KryptonTextBox();
            this.lblProblem = new Krypton.Toolkit.KryptonLabel();
            this.btnSelectDevice = new Krypton.Toolkit.KryptonButton();
            this.cmbDevice = new Krypton.Toolkit.KryptonComboBox();
            this.lblDevice = new Krypton.Toolkit.KryptonLabel();
            this.btnSelectCustomer = new Krypton.Toolkit.KryptonButton();
            this.txtCustomer = new Krypton.Toolkit.KryptonTextBox();
            this.lblCustomer = new Krypton.Toolkit.KryptonLabel();
            this.grpOrderInfo = new Krypton.Toolkit.KryptonGroupBox();
            this.flpStatus = new System.Windows.Forms.FlowLayoutPanel();
            this.lblStatus = new Krypton.Toolkit.KryptonLabel();
            this.dtpOrderDate = new Krypton.Toolkit.KryptonDateTimePicker();
            this.lblOrderDate = new Krypton.Toolkit.KryptonLabel();
            this.txtOrderNumber = new Krypton.Toolkit.KryptonTextBox();
            this.lblOrderNumber = new Krypton.Toolkit.KryptonLabel();
            this.headerPanel = new Krypton.Toolkit.KryptonPanel();
            this.lblTitle = new Krypton.Toolkit.KryptonLabel();
            this.btnBack = new Krypton.Toolkit.KryptonButton();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).BeginInit();
            this.kryptonPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpPaymentInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpPaymentInfo.Panel)).BeginInit();
            this.grpPaymentInfo.Panel.SuspendLayout();
            this.grpPaymentInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPaymentMethod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpServicesAndParts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpServicesAndParts.Panel)).BeginInit();
            this.grpServicesAndParts.Panel.SuspendLayout();
            this.grpServicesAndParts.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvParts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvServices)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpTechInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpTechInfo.Panel)).BeginInit();
            this.grpTechInfo.Panel.SuspendLayout();
            this.grpTechInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbTechnician)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCustomerDevice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCustomerDevice.Panel)).BeginInit();
            this.grpCustomerDevice.Panel.SuspendLayout();
            this.grpCustomerDevice.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDevice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpOrderInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpOrderInfo.Panel)).BeginInit();
            this.grpOrderInfo.Panel.SuspendLayout();
            this.grpOrderInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.headerPanel)).BeginInit();
            this.headerPanel.SuspendLayout();
            this.SuspendLayout();
            // 
            // kryptonPanel1
            // 
            this.kryptonPanel1.Controls.Add(this.btnCancel);
            this.kryptonPanel1.Controls.Add(this.btnSaveAndPay);
            this.kryptonPanel1.Controls.Add(this.btnSave);
            this.kryptonPanel1.Controls.Add(this.grpPaymentInfo);
            this.kryptonPanel1.Controls.Add(this.grpServicesAndParts);
            this.kryptonPanel1.Controls.Add(this.grpTechInfo);
            this.kryptonPanel1.Controls.Add(this.grpCustomerDevice);
            this.kryptonPanel1.Controls.Add(this.grpOrderInfo);
            this.kryptonPanel1.Controls.Add(this.headerPanel);
            this.kryptonPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel1.Location = new System.Drawing.Point(0, 0);
            this.kryptonPanel1.Name = "kryptonPanel1";
            this.kryptonPanel1.Size = new System.Drawing.Size(1024, 768);
            this.kryptonPanel1.TabIndex = 0;
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(930, 720);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(90, 30);
            this.btnCancel.TabIndex = 8;
            this.btnCancel.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnCancel.Values.Text = "Annuler";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnSaveAndPay
            // 
            this.btnSaveAndPay.Location = new System.Drawing.Point(800, 720);
            this.btnSaveAndPay.Name = "btnSaveAndPay";
            this.btnSaveAndPay.Size = new System.Drawing.Size(120, 30);
            this.btnSaveAndPay.TabIndex = 7;
            this.btnSaveAndPay.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnSaveAndPay.Values.Text = "Enregistrer et payer";
            this.btnSaveAndPay.Click += new System.EventHandler(this.btnSaveAndPay_Click);
            // 
            // btnSave
            // 
            this.btnSave.Location = new System.Drawing.Point(700, 720);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(90, 30);
            this.btnSave.TabIndex = 6;
            this.btnSave.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnSave.Values.Text = "Enregistrer";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // grpPaymentInfo
            // 
            this.grpPaymentInfo.Location = new System.Drawing.Point(20, 640);
            this.grpPaymentInfo.Name = "grpPaymentInfo";
            // 
            // grpPaymentInfo.Panel
            // 
            this.grpPaymentInfo.Panel.Controls.Add(this.lblRemainingValue);
            this.grpPaymentInfo.Panel.Controls.Add(this.lblRemainingAmount);
            this.grpPaymentInfo.Panel.Controls.Add(this.cmbPaymentMethod);
            this.grpPaymentInfo.Panel.Controls.Add(this.lblPaymentMethod);
            this.grpPaymentInfo.Panel.Controls.Add(this.txtPaidAmount);
            this.grpPaymentInfo.Panel.Controls.Add(this.lblPaidAmount);
            this.grpPaymentInfo.Panel.Controls.Add(this.lblTotalValue);
            this.grpPaymentInfo.Panel.Controls.Add(this.lblTotalCost);
            this.grpPaymentInfo.Size = new System.Drawing.Size(984, 70);
            this.grpPaymentInfo.TabIndex = 5;
            this.grpPaymentInfo.Values.Heading = "Informations de paiement";
            // 
            // lblRemainingValue
            // 
            this.lblRemainingValue.Location = new System.Drawing.Point(830, 10);
            this.lblRemainingValue.Name = "lblRemainingValue";
            this.lblRemainingValue.Size = new System.Drawing.Size(67, 21);
            this.lblRemainingValue.TabIndex = 7;
            this.lblRemainingValue.Values.Text = "0.00 DZD";
            // 
            // lblRemainingAmount
            // 
            this.lblRemainingAmount.Location = new System.Drawing.Point(740, 10);
            this.lblRemainingAmount.Name = "lblRemainingAmount";
            this.lblRemainingAmount.Size = new System.Drawing.Size(96, 21);
            this.lblRemainingAmount.TabIndex = 6;
            this.lblRemainingAmount.Values.Text = "Reste à payer:";
            // 
            // cmbPaymentMethod
            // 
            this.cmbPaymentMethod.DropDownWidth = 150;
            this.cmbPaymentMethod.IntegralHeight = false;
            this.cmbPaymentMethod.Location = new System.Drawing.Point(605, 10);
            this.cmbPaymentMethod.Name = "cmbPaymentMethod";
            this.cmbPaymentMethod.Size = new System.Drawing.Size(125, 24);
            this.cmbPaymentMethod.TabIndex = 5;
            this.cmbPaymentMethod.SelectedIndexChanged += new System.EventHandler(this.cmbPaymentMethod_SelectedIndexChanged);
            // 
            // lblPaymentMethod
            // 
            this.lblPaymentMethod.Location = new System.Drawing.Point(450, 10);
            this.lblPaymentMethod.Name = "lblPaymentMethod";
            this.lblPaymentMethod.Size = new System.Drawing.Size(149, 21);
            this.lblPaymentMethod.TabIndex = 4;
            this.lblPaymentMethod.Values.Text = "Méthode de paiement:";
            // 
            // txtPaidAmount
            // 
            this.txtPaidAmount.Location = new System.Drawing.Point(340, 10);
            this.txtPaidAmount.Name = "txtPaidAmount";
            this.txtPaidAmount.Size = new System.Drawing.Size(100, 25);
            this.txtPaidAmount.TabIndex = 3;
            this.txtPaidAmount.Text = "0.00";
            this.txtPaidAmount.TextChanged += new System.EventHandler(this.txtPaidAmount_TextChanged);
            // 
            // lblPaidAmount
            // 
            this.lblPaidAmount.Location = new System.Drawing.Point(235, 10);
            this.lblPaidAmount.Name = "lblPaidAmount";
            this.lblPaidAmount.Size = new System.Drawing.Size(99, 21);
            this.lblPaidAmount.TabIndex = 2;
            this.lblPaidAmount.Values.Text = "Montant payé:";
            // 
            // lblTotalValue
            // 
            this.lblTotalValue.Location = new System.Drawing.Point(100, 10);
            this.lblTotalValue.Name = "lblTotalValue";
            this.lblTotalValue.Size = new System.Drawing.Size(67, 21);
            this.lblTotalValue.TabIndex = 1;
            this.lblTotalValue.Values.Text = "0.00 DZD";
            // 
            // lblTotalCost
            // 
            this.lblTotalCost.Location = new System.Drawing.Point(10, 10);
            this.lblTotalCost.Name = "lblTotalCost";
            this.lblTotalCost.Size = new System.Drawing.Size(75, 21);
            this.lblTotalCost.TabIndex = 0;
            this.lblTotalCost.Values.Text = "Coût total:";
            // 
            // grpServicesAndParts
            // 
            this.grpServicesAndParts.Location = new System.Drawing.Point(20, 430);
            this.grpServicesAndParts.Name = "grpServicesAndParts";
            // 
            // grpServicesAndParts.Panel
            // 
            this.grpServicesAndParts.Panel.Controls.Add(this.dgvParts);
            this.grpServicesAndParts.Panel.Controls.Add(this.btnAddPart);
            this.grpServicesAndParts.Panel.Controls.Add(this.lblParts);
            this.grpServicesAndParts.Panel.Controls.Add(this.dgvServices);
            this.grpServicesAndParts.Panel.Controls.Add(this.btnAddService);
            this.grpServicesAndParts.Panel.Controls.Add(this.lblServices);
            this.grpServicesAndParts.Size = new System.Drawing.Size(984, 200);
            this.grpServicesAndParts.TabIndex = 4;
            this.grpServicesAndParts.Values.Heading = "Services et pièces";
            // 
            // dgvParts
            // 
            this.dgvParts.AllowUserToAddRows = false;
            this.dgvParts.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvParts.Location = new System.Drawing.Point(500, 40);
            this.dgvParts.Name = "dgvParts";
            this.dgvParts.Size = new System.Drawing.Size(450, 114);
            this.dgvParts.TabIndex = 5;
            // 
            // btnAddPart
            // 
            this.btnAddPart.Location = new System.Drawing.Point(570, 10);
            this.btnAddPart.Name = "btnAddPart";
            this.btnAddPart.Size = new System.Drawing.Size(90, 25);
            this.btnAddPart.TabIndex = 4;
            this.btnAddPart.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnAddPart.Values.Text = "Ajouter";
            this.btnAddPart.Click += new System.EventHandler(this.BtnAddPart_Click);
            // 
            // lblParts
            // 
            this.lblParts.Location = new System.Drawing.Point(500, 10);
            this.lblParts.Name = "lblParts";
            this.lblParts.Size = new System.Drawing.Size(52, 21);
            this.lblParts.TabIndex = 3;
            this.lblParts.Values.Text = "Pièces:";
            // 
            // dgvServices
            // 
            this.dgvServices.AllowUserToAddRows = false;
            this.dgvServices.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvServices.Location = new System.Drawing.Point(10, 40);
            this.dgvServices.Name = "dgvServices";
            this.dgvServices.Size = new System.Drawing.Size(450, 114);
            this.dgvServices.TabIndex = 2;
            // 
            // btnAddService
            // 
            this.btnAddService.Location = new System.Drawing.Point(80, 10);
            this.btnAddService.Name = "btnAddService";
            this.btnAddService.Size = new System.Drawing.Size(90, 25);
            this.btnAddService.TabIndex = 1;
            this.btnAddService.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnAddService.Values.Text = "Ajouter";
            this.btnAddService.Click += new System.EventHandler(this.BtnAddService_Click);
            // 
            // lblServices
            // 
            this.lblServices.Location = new System.Drawing.Point(10, 10);
            this.lblServices.Name = "lblServices";
            this.lblServices.Size = new System.Drawing.Size(63, 21);
            this.lblServices.TabIndex = 0;
            this.lblServices.Values.Text = "Services:";
            // 
            // grpTechInfo
            // 
            this.grpTechInfo.Location = new System.Drawing.Point(20, 300);
            this.grpTechInfo.Name = "grpTechInfo";
            // 
            // grpTechInfo.Panel
            // 
            this.grpTechInfo.Panel.Controls.Add(this.txtTechNotes);
            this.grpTechInfo.Panel.Controls.Add(this.lblTechNotes);
            this.grpTechInfo.Panel.Controls.Add(this.txtWarrantyPeriod);
            this.grpTechInfo.Panel.Controls.Add(this.lblWarrantyPeriod);
            this.grpTechInfo.Panel.Controls.Add(this.cmbTechnician);
            this.grpTechInfo.Panel.Controls.Add(this.lblTechnician);
            this.grpTechInfo.Size = new System.Drawing.Size(984, 120);
            this.grpTechInfo.TabIndex = 3;
            this.grpTechInfo.Values.Heading = "Informations et notes du technicien";
            // 
            // txtTechNotes
            // 
            this.txtTechNotes.Location = new System.Drawing.Point(140, 40);
            this.txtTechNotes.Multiline = true;
            this.txtTechNotes.Name = "txtTechNotes";
            this.txtTechNotes.Size = new System.Drawing.Size(800, 40);
            this.txtTechNotes.TabIndex = 5;
            this.txtTechNotes.TextChanged += new System.EventHandler(this.txtTechNotes_TextChanged);
            // 
            // lblTechNotes
            // 
            this.lblTechNotes.Location = new System.Drawing.Point(10, 40);
            this.lblTechNotes.Name = "lblTechNotes";
            this.lblTechNotes.Size = new System.Drawing.Size(120, 21);
            this.lblTechNotes.TabIndex = 4;
            this.lblTechNotes.Values.Text = "Notes techniques:";
            // 
            // txtWarrantyPeriod
            // 
            this.txtWarrantyPeriod.Location = new System.Drawing.Point(580, 10);
            this.txtWarrantyPeriod.Name = "txtWarrantyPeriod";
            this.txtWarrantyPeriod.Size = new System.Drawing.Size(80, 25);
            this.txtWarrantyPeriod.TabIndex = 3;
            this.txtWarrantyPeriod.TextChanged += new System.EventHandler(this.txtWarrantyPeriod_TextChanged);
            // 
            // lblWarrantyPeriod
            // 
            this.lblWarrantyPeriod.Location = new System.Drawing.Point(450, 10);
            this.lblWarrantyPeriod.Name = "lblWarrantyPeriod";
            this.lblWarrantyPeriod.Size = new System.Drawing.Size(176, 21);
            this.lblWarrantyPeriod.TabIndex = 2;
            this.lblWarrantyPeriod.Values.Text = "Période de garantie (jours):";
            // 
            // cmbTechnician
            // 
            this.cmbTechnician.DropDownWidth = 250;
            this.cmbTechnician.IntegralHeight = false;
            this.cmbTechnician.Location = new System.Drawing.Point(140, 10);
            this.cmbTechnician.Name = "cmbTechnician";
            this.cmbTechnician.Size = new System.Drawing.Size(250, 24);
            this.cmbTechnician.TabIndex = 1;
            this.cmbTechnician.SelectedIndexChanged += new System.EventHandler(this.cmbTechnician_SelectedIndexChanged);
            // 
            // lblTechnician
            // 
            this.lblTechnician.Location = new System.Drawing.Point(10, 10);
            this.lblTechnician.Name = "lblTechnician";
            this.lblTechnician.Size = new System.Drawing.Size(79, 21);
            this.lblTechnician.TabIndex = 0;
            this.lblTechnician.Values.Text = "Technicien:";
            // 
            // grpCustomerDevice
            // 
            this.grpCustomerDevice.Location = new System.Drawing.Point(20, 170);
            this.grpCustomerDevice.Name = "grpCustomerDevice";
            // 
            // grpCustomerDevice.Panel
            // 
            this.grpCustomerDevice.Panel.Controls.Add(this.txtProblem);
            this.grpCustomerDevice.Panel.Controls.Add(this.lblProblem);
            this.grpCustomerDevice.Panel.Controls.Add(this.btnSelectDevice);
            this.grpCustomerDevice.Panel.Controls.Add(this.cmbDevice);
            this.grpCustomerDevice.Panel.Controls.Add(this.lblDevice);
            this.grpCustomerDevice.Panel.Controls.Add(this.btnSelectCustomer);
            this.grpCustomerDevice.Panel.Controls.Add(this.txtCustomer);
            this.grpCustomerDevice.Panel.Controls.Add(this.lblCustomer);
            this.grpCustomerDevice.Size = new System.Drawing.Size(984, 120);
            this.grpCustomerDevice.TabIndex = 2;
            this.grpCustomerDevice.Values.Heading = "Informations client et appareil";
            // 
            // txtProblem
            // 
            this.txtProblem.Location = new System.Drawing.Point(140, 70);
            this.txtProblem.Multiline = true;
            this.txtProblem.Name = "txtProblem";
            this.txtProblem.Size = new System.Drawing.Size(800, 40);
            this.txtProblem.TabIndex = 7;
            this.txtProblem.TextChanged += new System.EventHandler(this.txtProblem_TextChanged);
            // 
            // lblProblem
            // 
            this.lblProblem.Location = new System.Drawing.Point(10, 70);
            this.lblProblem.Name = "lblProblem";
            this.lblProblem.Size = new System.Drawing.Size(73, 21);
            this.lblProblem.TabIndex = 6;
            this.lblProblem.Values.Text = "Problème:";
            // 
            // btnSelectDevice
            // 
            this.btnSelectDevice.Location = new System.Drawing.Point(400, 40);
            this.btnSelectDevice.Name = "btnSelectDevice";
            this.btnSelectDevice.Size = new System.Drawing.Size(30, 21);
            this.btnSelectDevice.TabIndex = 5;
            this.btnSelectDevice.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnSelectDevice.Values.Text = "...";
            this.btnSelectDevice.Click += new System.EventHandler(this.btnSelectDevice_Click);
            // 
            // cmbDevice
            // 
            this.cmbDevice.DropDownWidth = 250;
            this.cmbDevice.IntegralHeight = false;
            this.cmbDevice.Location = new System.Drawing.Point(140, 40);
            this.cmbDevice.Name = "cmbDevice";
            this.cmbDevice.Size = new System.Drawing.Size(250, 24);
            this.cmbDevice.TabIndex = 4;
            this.cmbDevice.SelectedIndexChanged += new System.EventHandler(this.cmbDevice_SelectedIndexChanged);
            // 
            // lblDevice
            // 
            this.lblDevice.Location = new System.Drawing.Point(10, 40);
            this.lblDevice.Name = "lblDevice";
            this.lblDevice.Size = new System.Drawing.Size(65, 21);
            this.lblDevice.TabIndex = 3;
            this.lblDevice.Values.Text = "Appareil:";
            // 
            // btnSelectCustomer
            // 
            this.btnSelectCustomer.Location = new System.Drawing.Point(400, 10);
            this.btnSelectCustomer.Name = "btnSelectCustomer";
            this.btnSelectCustomer.Size = new System.Drawing.Size(30, 23);
            this.btnSelectCustomer.TabIndex = 2;
            this.btnSelectCustomer.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnSelectCustomer.Values.Text = "...";
            this.btnSelectCustomer.Click += new System.EventHandler(this.btnSelectCustomer_Click);
            // 
            // txtCustomer
            // 
            this.txtCustomer.Location = new System.Drawing.Point(140, 10);
            this.txtCustomer.Name = "txtCustomer";
            this.txtCustomer.ReadOnly = true;
            this.txtCustomer.Size = new System.Drawing.Size(250, 25);
            this.txtCustomer.TabIndex = 1;
            // 
            // lblCustomer
            // 
            this.lblCustomer.Location = new System.Drawing.Point(10, 10);
            this.lblCustomer.Name = "lblCustomer";
            this.lblCustomer.Size = new System.Drawing.Size(49, 21);
            this.lblCustomer.TabIndex = 0;
            this.lblCustomer.Values.Text = "Client:";
            // 
            // grpOrderInfo
            // 
            this.grpOrderInfo.Location = new System.Drawing.Point(20, 60);
            this.grpOrderInfo.Name = "grpOrderInfo";
            // 
            // grpOrderInfo.Panel
            // 
            this.grpOrderInfo.Panel.Controls.Add(this.flpStatus);
            this.grpOrderInfo.Panel.Controls.Add(this.lblStatus);
            this.grpOrderInfo.Panel.Controls.Add(this.dtpOrderDate);
            this.grpOrderInfo.Panel.Controls.Add(this.lblOrderDate);
            this.grpOrderInfo.Panel.Controls.Add(this.txtOrderNumber);
            this.grpOrderInfo.Panel.Controls.Add(this.lblOrderNumber);
            this.grpOrderInfo.Size = new System.Drawing.Size(984, 104);
            this.grpOrderInfo.TabIndex = 1;
            this.grpOrderInfo.Values.Heading = "Informations de commande";
            // 
            // flpStatus
            // 
            this.flpStatus.Location = new System.Drawing.Point(140, 40);
            this.flpStatus.Name = "flpStatus";
            this.flpStatus.Size = new System.Drawing.Size(800, 32);
            this.flpStatus.TabIndex = 5;
            // 
            // lblStatus
            // 
            this.lblStatus.Location = new System.Drawing.Point(10, 40);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(51, 21);
            this.lblStatus.TabIndex = 4;
            this.lblStatus.Values.Text = "Statut:";
            // 
            // dtpOrderDate
            // 
            this.dtpOrderDate.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.dtpOrderDate.Location = new System.Drawing.Point(440, 10);
            this.dtpOrderDate.Name = "dtpOrderDate";
            this.dtpOrderDate.Size = new System.Drawing.Size(150, 23);
            this.dtpOrderDate.TabIndex = 3;
            // 
            // lblOrderDate
            // 
            this.lblOrderDate.Location = new System.Drawing.Point(310, 10);
            this.lblOrderDate.Name = "lblOrderDate";
            this.lblOrderDate.Size = new System.Drawing.Size(133, 21);
            this.lblOrderDate.TabIndex = 2;
            this.lblOrderDate.Values.Text = "Date de commande:";
            // 
            // txtOrderNumber
            // 
            this.txtOrderNumber.Location = new System.Drawing.Point(140, 10);
            this.txtOrderNumber.Name = "txtOrderNumber";
            this.txtOrderNumber.ReadOnly = true;
            this.txtOrderNumber.Size = new System.Drawing.Size(150, 25);
            this.txtOrderNumber.TabIndex = 1;
            // 
            // lblOrderNumber
            // 
            this.lblOrderNumber.Location = new System.Drawing.Point(10, 10);
            this.lblOrderNumber.Name = "lblOrderNumber";
            this.lblOrderNumber.Size = new System.Drawing.Size(155, 21);
            this.lblOrderNumber.TabIndex = 0;
            this.lblOrderNumber.Values.Text = "Numéro de commande:";
            // 
            // headerPanel
            // 
            this.headerPanel.Controls.Add(this.lblTitle);
            this.headerPanel.Controls.Add(this.btnBack);
            this.headerPanel.Dock = System.Windows.Forms.DockStyle.Top;
            this.headerPanel.Location = new System.Drawing.Point(0, 0);
            this.headerPanel.Name = "headerPanel";
            this.headerPanel.Size = new System.Drawing.Size(1024, 50);
            this.headerPanel.TabIndex = 0;
            // 
            // lblTitle
            // 
            this.lblTitle.LabelStyle = Krypton.Toolkit.LabelStyle.TitlePanel;
            this.lblTitle.Location = new System.Drawing.Point(70, 10);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(286, 30);
            this.lblTitle.TabIndex = 1;
            this.lblTitle.Values.Text = "Ajouter un ordre de réparation";
            // 
            // btnBack
            // 
            this.btnBack.Location = new System.Drawing.Point(12, 10);
            this.btnBack.Name = "btnBack";
            this.btnBack.Size = new System.Drawing.Size(50, 30);
            this.btnBack.TabIndex = 0;
            this.btnBack.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnBack.Values.Text = "←";
            this.btnBack.Click += new System.EventHandler(this.btnBack_Click);
            // 
            // FRM_REPAIR_ORDER
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1024, 768);
            this.Controls.Add(this.kryptonPanel1);
            this.Name = "FRM_REPAIR_ORDER";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Ordre de réparation";
            this.Load += new System.EventHandler(this.FRM_REPAIR_ORDER_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).EndInit();
            this.kryptonPanel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grpPaymentInfo.Panel)).EndInit();
            this.grpPaymentInfo.Panel.ResumeLayout(false);
            this.grpPaymentInfo.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpPaymentInfo)).EndInit();
            this.grpPaymentInfo.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.cmbPaymentMethod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpServicesAndParts.Panel)).EndInit();
            this.grpServicesAndParts.Panel.ResumeLayout(false);
            this.grpServicesAndParts.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpServicesAndParts)).EndInit();
            this.grpServicesAndParts.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvParts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvServices)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpTechInfo.Panel)).EndInit();
            this.grpTechInfo.Panel.ResumeLayout(false);
            this.grpTechInfo.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpTechInfo)).EndInit();
            this.grpTechInfo.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.cmbTechnician)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCustomerDevice.Panel)).EndInit();
            this.grpCustomerDevice.Panel.ResumeLayout(false);
            this.grpCustomerDevice.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCustomerDevice)).EndInit();
            this.grpCustomerDevice.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.cmbDevice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpOrderInfo.Panel)).EndInit();
            this.grpOrderInfo.Panel.ResumeLayout(false);
            this.grpOrderInfo.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpOrderInfo)).EndInit();
            this.grpOrderInfo.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.headerPanel)).EndInit();
            this.headerPanel.ResumeLayout(false);
            this.headerPanel.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonPanel kryptonPanel1;
        private Krypton.Toolkit.KryptonPanel headerPanel;
        private Krypton.Toolkit.KryptonLabel lblTitle;
        private Krypton.Toolkit.KryptonButton btnBack;

        // Order information section
        private Krypton.Toolkit.KryptonGroupBox grpOrderInfo;
        private Krypton.Toolkit.KryptonLabel lblOrderNumber;
        private Krypton.Toolkit.KryptonTextBox txtOrderNumber;
        private Krypton.Toolkit.KryptonLabel lblOrderDate;
        private Krypton.Toolkit.KryptonDateTimePicker dtpOrderDate;
        private Krypton.Toolkit.KryptonLabel lblStatus;
        private System.Windows.Forms.FlowLayoutPanel flpStatus;

        // Customer and device section
        private Krypton.Toolkit.KryptonGroupBox grpCustomerDevice;
        private Krypton.Toolkit.KryptonLabel lblCustomer;
        private Krypton.Toolkit.KryptonTextBox txtCustomer;
        private Krypton.Toolkit.KryptonButton btnSelectCustomer;
        private Krypton.Toolkit.KryptonLabel lblDevice;
        private Krypton.Toolkit.KryptonComboBox cmbDevice;
        private Krypton.Toolkit.KryptonButton btnSelectDevice;
        private Krypton.Toolkit.KryptonLabel lblProblem;
        private Krypton.Toolkit.KryptonTextBox txtProblem;

        // Technician information section
        private Krypton.Toolkit.KryptonGroupBox grpTechInfo;
        private Krypton.Toolkit.KryptonLabel lblTechnician;
        private Krypton.Toolkit.KryptonComboBox cmbTechnician;
        private Krypton.Toolkit.KryptonLabel lblWarrantyPeriod;
        private Krypton.Toolkit.KryptonTextBox txtWarrantyPeriod;
        private Krypton.Toolkit.KryptonLabel lblTechNotes;
        private Krypton.Toolkit.KryptonTextBox txtTechNotes;

        // Services and parts section
        private Krypton.Toolkit.KryptonGroupBox grpServicesAndParts;
        private Krypton.Toolkit.KryptonLabel lblServices;
        private Krypton.Toolkit.KryptonButton btnAddService;
        private Krypton.Toolkit.KryptonDataGridView dgvServices;
        private Krypton.Toolkit.KryptonLabel lblParts;
        private Krypton.Toolkit.KryptonButton btnAddPart;
        private Krypton.Toolkit.KryptonDataGridView dgvParts;

        // Payment information section
        private Krypton.Toolkit.KryptonGroupBox grpPaymentInfo;
        private Krypton.Toolkit.KryptonLabel lblTotalCost;
        private Krypton.Toolkit.KryptonLabel lblTotalValue;
        private Krypton.Toolkit.KryptonLabel lblPaidAmount;
        private Krypton.Toolkit.KryptonTextBox txtPaidAmount;
        private Krypton.Toolkit.KryptonLabel lblPaymentMethod;
        private Krypton.Toolkit.KryptonComboBox cmbPaymentMethod;
        private Krypton.Toolkit.KryptonLabel lblRemainingAmount;
        private Krypton.Toolkit.KryptonLabel lblRemainingValue;

        // Buttons
        private Krypton.Toolkit.KryptonButton btnSave;
        private Krypton.Toolkit.KryptonButton btnSaveAndPay;
        private Krypton.Toolkit.KryptonButton btnCancel;
    }
}
