﻿using Dapper;
using IRepairIT.DB;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace IRepairIT.Data
{
    public class DatabaseManager
    {
        private readonly string _connectionString;
        private readonly string _databasePrefix = "IRepairIT";
        private readonly ConnectionSettings _connectionSettings;

        public DatabaseManager()
        {
            string settingsFilePath = Path.Combine(Application.StartupPath, "connection.json");
            _connectionSettings = ConnectionSettings.LoadFromJson(settingsFilePath);

            _connectionString = _connectionSettings.GetMasterConnectionString();
        }
        public bool CreateDatabase(string dbInfo)
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    var dbName = $"{_databasePrefix}{dbInfo}";

                    var checkSql = "SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name = @dbName";
                    var exists = connection.ExecuteScalar<int>(checkSql, new { dbName });

                    if (exists > 0)
                    {
                        MessageBox.Show($"Database {dbName} already exists.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return false;
                    }

                    var createSql = $"CREATE DATABASE IF NOT EXISTS `{dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
                    connection.Execute(createSql);

                    var scriptPath = Path.Combine(Application.StartupPath, "db", "db.sql");
                    var dbConnectionString = _connectionSettings.GetConnectionString(dbName);

                    if (File.Exists(scriptPath))
                    {
                        var script = File.ReadAllText(scriptPath);

                        using (var dbConnection = new MySqlConnection(dbConnectionString))
                        {
                            dbConnection.Open();

                            var commands = script.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                            foreach (var command in commands)
                            {
                                if (!string.IsNullOrWhiteSpace(command))
                                {
                                    try
                                    {
                                        dbConnection.Execute(command + ";");
                                    }
                                    catch (Exception cmdEx)
                                    {
                                        Console.WriteLine($"Error executing SQL command: {cmdEx.Message}");
                                        Console.WriteLine($"Command: {command}");
                                    }
                                }
                            }
                        }
                    }

                }

                return true;

            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error creating database: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
        public DatabaseManager(string server, string username, string password)
        {
            _connectionSettings = new ConnectionSettings
            {
                Server = server,
                Port = 3306,
                Username = username,
                Password = password
            };

            _connectionString = _connectionSettings.GetMasterConnectionString();
        }

        public List<string> GetIRepairDatabases()
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    var sql = "SELECT schema_name FROM information_schema.schemata WHERE schema_name LIKE @pattern ORDER BY schema_name";
                    var databases = connection.Query<string>(sql, new { pattern = $"%{_databasePrefix}%" }).ToList();
                    return databases;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error retrieving databases: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new List<string>();
            }
        }




        public bool CheckDatabaseExists(string dbName)
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    var sql = "SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name = @dbName";
                    var count = connection.ExecuteScalar<int>(sql, new { dbName });
                    return count > 0;
                }
            }
            catch (MySqlException ex)
            {
                Console.WriteLine($"Error checking if database exists: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error checking if database exists: {ex.Message}");
                return false;
            }
        }

    }
}
