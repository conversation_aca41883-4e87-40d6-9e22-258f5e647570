namespace IRepairIT.FRMS.FREPAIRS
{
    partial class FRM_PAYMENT
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.kryptonPanel1 = new Krypton.Toolkit.KryptonPanel();
            this.lblTotalAmount = new Krypton.Toolkit.KryptonLabel();
            this.lblPaidAmount = new Krypton.Toolkit.KryptonLabel();
            this.lblRemainingAmount = new Krypton.Toolkit.KryptonLabel();
            this.lblAmount = new Krypton.Toolkit.KryptonLabel();
            this.txtAmount = new Krypton.Toolkit.KryptonTextBox();
            this.lblPaymentMethod = new Krypton.Toolkit.KryptonLabel();
            this.cmbPaymentMethod = new Krypton.Toolkit.KryptonComboBox();
            this.lblNotes = new Krypton.Toolkit.KryptonLabel();
            this.txtNotes = new Krypton.Toolkit.KryptonTextBox();
            this.btnSave = new Krypton.Toolkit.KryptonButton();
            this.btnCancel = new Krypton.Toolkit.KryptonButton();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).BeginInit();
            this.kryptonPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPaymentMethod)).BeginInit();
            this.SuspendLayout();
            // 
            // kryptonPanel1
            // 
            this.kryptonPanel1.Controls.Add(this.lblTotalAmount);
            this.kryptonPanel1.Controls.Add(this.lblPaidAmount);
            this.kryptonPanel1.Controls.Add(this.lblRemainingAmount);
            this.kryptonPanel1.Controls.Add(this.lblAmount);
            this.kryptonPanel1.Controls.Add(this.txtAmount);
            this.kryptonPanel1.Controls.Add(this.lblPaymentMethod);
            this.kryptonPanel1.Controls.Add(this.cmbPaymentMethod);
            this.kryptonPanel1.Controls.Add(this.lblNotes);
            this.kryptonPanel1.Controls.Add(this.txtNotes);
            this.kryptonPanel1.Controls.Add(this.btnSave);
            this.kryptonPanel1.Controls.Add(this.btnCancel);
            this.kryptonPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel1.Location = new System.Drawing.Point(0, 0);
            this.kryptonPanel1.Name = "kryptonPanel1";
            this.kryptonPanel1.Size = new System.Drawing.Size(434, 361);
            this.kryptonPanel1.TabIndex = 0;
            // 
            // lblTotalAmount
            // 
            this.lblTotalAmount.Location = new System.Drawing.Point(20, 20);
            this.lblTotalAmount.Name = "lblTotalAmount";
            this.lblTotalAmount.Size = new System.Drawing.Size(139, 21);
            this.lblTotalAmount.TabIndex = 0;
            this.lblTotalAmount.Values.Text = "Montant total: 0,00 €";
            // 
            // lblPaidAmount
            // 
            this.lblPaidAmount.Location = new System.Drawing.Point(20, 50);
            this.lblPaidAmount.Name = "lblPaidAmount";
            this.lblPaidAmount.Size = new System.Drawing.Size(140, 21);
            this.lblPaidAmount.TabIndex = 1;
            this.lblPaidAmount.Values.Text = "Montant payé: 0,00 €";
            // 
            // lblRemainingAmount
            // 
            this.lblRemainingAmount.Location = new System.Drawing.Point(20, 80);
            this.lblRemainingAmount.Name = "lblRemainingAmount";
            this.lblRemainingAmount.Size = new System.Drawing.Size(153, 21);
            this.lblRemainingAmount.TabIndex = 2;
            this.lblRemainingAmount.Values.Text = "Montant restant: 0,00 €";
            // 
            // lblAmount
            // 
            this.lblAmount.Location = new System.Drawing.Point(20, 120);
            this.lblAmount.Name = "lblAmount";
            this.lblAmount.Size = new System.Drawing.Size(147, 21);
            this.lblAmount.TabIndex = 3;
            this.lblAmount.Values.Text = "Montant du paiement:";
            // 
            // txtAmount
            // 
            this.txtAmount.Location = new System.Drawing.Point(180, 120);
            this.txtAmount.Name = "txtAmount";
            this.txtAmount.Size = new System.Drawing.Size(200, 25);
            this.txtAmount.TabIndex = 4;
            // 
            // lblPaymentMethod
            // 
            this.lblPaymentMethod.Location = new System.Drawing.Point(20, 160);
            this.lblPaymentMethod.Name = "lblPaymentMethod";
            this.lblPaymentMethod.Size = new System.Drawing.Size(149, 21);
            this.lblPaymentMethod.TabIndex = 5;
            this.lblPaymentMethod.Values.Text = "Méthode de paiement:";
            // 
            // cmbPaymentMethod
            // 
            this.cmbPaymentMethod.DropDownWidth = 200;
            this.cmbPaymentMethod.IntegralHeight = false;
            this.cmbPaymentMethod.Location = new System.Drawing.Point(180, 160);
            this.cmbPaymentMethod.Name = "cmbPaymentMethod";
            this.cmbPaymentMethod.Size = new System.Drawing.Size(200, 24);
            this.cmbPaymentMethod.TabIndex = 6;
            // 
            // lblNotes
            // 
            this.lblNotes.Location = new System.Drawing.Point(20, 200);
            this.lblNotes.Name = "lblNotes";
            this.lblNotes.Size = new System.Drawing.Size(50, 21);
            this.lblNotes.TabIndex = 7;
            this.lblNotes.Values.Text = "Notes:";
            // 
            // txtNotes
            // 
            this.txtNotes.Location = new System.Drawing.Point(180, 200);
            this.txtNotes.Multiline = true;
            this.txtNotes.Name = "txtNotes";
            this.txtNotes.Size = new System.Drawing.Size(200, 80);
            this.txtNotes.TabIndex = 8;
            // 
            // btnSave
            // 
            this.btnSave.Location = new System.Drawing.Point(230, 310);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(90, 30);
            this.btnSave.TabIndex = 9;
            this.btnSave.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnSave.Values.Text = "Enregistrer";
            this.btnSave.Click += new System.EventHandler(this.BtnSave_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(330, 310);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(90, 30);
            this.btnCancel.TabIndex = 10;
            this.btnCancel.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnCancel.Values.Text = "Annuler";
            this.btnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            // 
            // FRM_PAYMENT
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(434, 361);
            this.Controls.Add(this.kryptonPanel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FRM_PAYMENT";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Ajouter un paiement";
            this.Load += new System.EventHandler(this.FRM_PAYMENT_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).EndInit();
            this.kryptonPanel1.ResumeLayout(false);
            this.kryptonPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPaymentMethod)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonPanel kryptonPanel1;
        private Krypton.Toolkit.KryptonLabel lblTotalAmount;
        private Krypton.Toolkit.KryptonLabel lblPaidAmount;
        private Krypton.Toolkit.KryptonLabel lblRemainingAmount;
        private Krypton.Toolkit.KryptonLabel lblAmount;
        private Krypton.Toolkit.KryptonTextBox txtAmount;
        private Krypton.Toolkit.KryptonLabel lblPaymentMethod;
        private Krypton.Toolkit.KryptonComboBox cmbPaymentMethod;
        private Krypton.Toolkit.KryptonLabel lblNotes;
        private Krypton.Toolkit.KryptonTextBox txtNotes;
        private Krypton.Toolkit.KryptonButton btnSave;
        private Krypton.Toolkit.KryptonButton btnCancel;
    }
}
