﻿using IRepairIT.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class RepairOrder
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Numéro de commande")]
        public string OrderNumber { get; set; }

        [Display(Name = "Identifiant de l'appareil")]
        public int DeviceId { get; set; }

        [Display(Name = "Identifiant du client")]
        public int CustomerId { get; set; }

        [Display(Name = "Statut")]
        public string Status { get; set; }

        [Display(Name = "Coût total")]
        public decimal TotalCost { get; set; }

        [Display(Name = "Coût du service")]
        public decimal ServiceCost { get; set; }

        [Display(Name = "Coût des pièces")]
        public decimal PartCost { get; set; }

        [Display(Name = "Montant payé")]
        public decimal PaidAmount { get; set; }

        [Display(Name = "Statut de paiement")]
        public PaymentStatus PaymentStatus { get; set; }

        [Display(Name = "Méthode de paiement")]
        public string PaymentMethod { get; set; }

        [Display(Name = "Identifiant du technicien")]
        public int? TechnicianId { get; set; }

        [Display(Name = "Période de garantie (jours)")]
        public int? WarrantyPeriod { get; set; }

        [Display(Name = "Notes de réparation")]
        public string RepairNotes { get; set; }

        [Display(Name = "Notes techniques")]
        public string TechnicalNotes { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Date de mise à jour")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "Appareil")]
        public Device Device { get; set; }

        [Display(Name = "Client")]
        public Customer Customer { get; set; }

        [Display(Name = "Technicien")]
        public User Technician { get; set; }
    }
}
