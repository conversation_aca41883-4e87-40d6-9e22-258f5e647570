﻿using IRepairIT.utilities;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class Part : EntityValidator
    {
        [DisplayName("#")]
        public int Id { get; set; }

        [DisplayName("Nom"), Required(ErrorMessage = "Le champ [ Nom ] doit être renseigné")]
        public string Name { get; set; }

        [DisplayName("Code")]
        public string Code { get; set; }

        [DisplayName("Description")]
        public string Description { get; set; }

        [DisplayName("Prix d'achat"), Required(ErrorMessage = "Le champ [ Prix d'achat ] doit être renseigné")]
        public decimal Purchase_Price { get; set; }

        [DisplayName("Prix de vente"), Required(ErrorMessage = "Le champ [ Prix de vente ] doit être renseigné")]
        public decimal Selling_Price { get; set; }

        [DisplayName("Quantité")]
        public int Quantity { get; set; }

        [DisplayName("Quantité minimale")]
        public int Min_Quantity { get; set; }

        [DisplayName("Catégorie")]
        public string Category { get; set; }

        [DisplayName("Fournisseur")]
        public string Supplier { get; set; }

        [DisplayName("Date de création")]
        public DateTime CreatedAt { get; set; }

        [DisplayName("Date de mise à jour")]
        public DateTime? UpdatedAt { get; set; }
    }
}
