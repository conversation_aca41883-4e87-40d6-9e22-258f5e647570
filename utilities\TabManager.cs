﻿using Krypton.Navigator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace IRepairIT.Utilities
{
    public class TabManager : IDisposable
    {
        private readonly KryptonNavigator _navigator;
        private readonly Dictionary<string, KryptonPage> _tabs;
        private readonly Dictionary<string, Form> _forms;
        private readonly HashSet<string> _nonClosableTabs;

        public TabManager(KryptonNavigator navigator)
        {
            _navigator = navigator ?? throw new ArgumentNullException(nameof(navigator));
            _tabs = new Dictionary<string, KryptonPage>(StringComparer.OrdinalIgnoreCase);
            _forms = new Dictionary<string, Form>(StringComparer.OrdinalIgnoreCase);
            _nonClosableTabs = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            _navigator.Button.ButtonDisplayLogic = ButtonDisplayLogic.None;
            _navigator.Button.CloseButtonAction = CloseButtonAction.RemovePage;

            _navigator.SelectedPageChanged += Navigator_SelectedPageChanged;
        }

        public void AddOrSelectTab(Func<Form> createForm, bool mutli = false)
        {
            if (createForm == null)
                throw new ArgumentNullException(nameof(createForm));

            Form form = createForm();
            string title = form.Text.Trim();

            if (IsOpenTab(title) && mutli.Equals(false))
            {
                _navigator.SelectedPage = _tabs[title];
                form.BringToFront();
            }
            else
            {
                CreateTab(form, title);
            }
        }

        private void CreateTab(Form form, string title)
        {
            KryptonPage newTab = new KryptonPage
            {
                Text = title
            };

            form.TopLevel = false;
            form.FormBorderStyle = FormBorderStyle.None;
            form.Dock = DockStyle.Fill;

            newTab.Controls.Add(form);
            form.Show();

            _navigator.Pages.Add(newTab);

            _tabs[title] = newTab;
            _forms[title] = form;

            _navigator.SelectedPage = newTab;
        }

        private bool IsOpenTab(string tabName)
        {
            return _navigator.Pages.OfType<KryptonPage>().Any(p => p.Text.Equals(tabName, StringComparison.OrdinalIgnoreCase));
        }

        public void PreventTabClose(string title)
        {
            if (!string.IsNullOrEmpty(title))
            {
                _nonClosableTabs.Add(title.Trim());
                UpdateCloseButtonAction();
            }
        }

        public void AllowTabClose(string title)
        {
            if (!string.IsNullOrEmpty(title))
            {
                _nonClosableTabs.Remove(title.Trim());
                UpdateCloseButtonAction();
            }
        }

        private void UpdateCloseButtonAction()
        {
            if (_navigator.SelectedPage != null)
            {
                string currentTitle = _navigator.SelectedPage.Text.Trim();
                _navigator.Button.CloseButtonAction = _nonClosableTabs.Contains(currentTitle)
                    ? CloseButtonAction.None
                    : CloseButtonAction.RemovePage;
            }
        }

        private void Navigator_SelectedPageChanged(object sender, EventArgs e)
        {
            UpdateCloseButtonAction();
        }

        public void Dispose()
        {
            _navigator.SelectedPageChanged -= Navigator_SelectedPageChanged;
        }
    }
}
