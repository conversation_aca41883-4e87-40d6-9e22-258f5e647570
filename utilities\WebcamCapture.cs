using System;
using System.Drawing;
using System.Windows.Forms;
using AForge.Video;
using AForge.Video.DirectShow;

namespace IRepairIT.utilities
{
    public class WebcamCapture : IDisposable
    {
        // Static method to check if any webcam is available without creating an instance
        public static bool IsWebcamAvailable()
        {
            try
            {
                // Use AForge to check for available video devices
                var videoDevices = new AForge.Video.DirectShow.FilterInfoCollection(
                    AForge.Video.DirectShow.FilterCategory.VideoInputDevice);
                
                return videoDevices != null && videoDevices.Count > 0;
            }
            catch
            {
                // If there's an error accessing the webcam, assume none is available
                return false;
            }
        }

        private FilterInfoCollection _videoDevices;
        private VideoCaptureDevice _videoSource;
        private PictureBox _pictureBox;
        private bool _isRunning = false;

        public event EventHandler<Bitmap> ImageCaptured;

        public WebcamCapture(PictureBox pictureBox)
        {
            _pictureBox = pictureBox;
            InitializeWebcam();
        }

        private void InitializeWebcam()
        {
            try
            {
                // Enumerate video devices
                _videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);
                
                if (_videoDevices.Count == 0)
                {
                    throw new Exception("Aucune webcam détectée");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing webcam: {ex.Message}");
                throw;
            }
        }

        public string[] GetAvailableCameras()
        {
            string[] cameraNames = new string[_videoDevices.Count];
            
            for (int i = 0; i < _videoDevices.Count; i++)
            {
                cameraNames[i] = _videoDevices[i].Name;
            }
            
            return cameraNames;
        }

        public void StartCamera(int cameraIndex = 0)
        {
            try
            {
                if (_isRunning)
                {
                    StopCamera();
                }

                if (cameraIndex >= _videoDevices.Count)
                {
                    cameraIndex = 0;
                }

                // Create video source
                _videoSource = new VideoCaptureDevice(_videoDevices[cameraIndex].MonikerString);
                _videoSource.NewFrame += VideoSource_NewFrame;
                
                // Start video source
                _videoSource.Start();
                _isRunning = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error starting camera: {ex.Message}");
                throw;
            }
        }

        private void VideoSource_NewFrame(object sender, NewFrameEventArgs eventArgs)
        {
            try
            {
                // Get the current frame
                Bitmap bitmap = (Bitmap)eventArgs.Frame.Clone();
                
                // Update the PictureBox on the UI thread
                if (_pictureBox.InvokeRequired)
                {
                    _pictureBox.Invoke(new Action(() => _pictureBox.Image = bitmap));
                }
                else
                {
                    _pictureBox.Image = bitmap;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in video frame handler: {ex.Message}");
            }
        }

        public void CaptureImage()
        {
            if (!_isRunning || _pictureBox.Image == null)
            {
                return;
            }

            try
            {
                // Clone the current image
                Bitmap capturedImage = (Bitmap)_pictureBox.Image.Clone();
                
                // Raise the event
                ImageCaptured?.Invoke(this, capturedImage);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error capturing image: {ex.Message}");
                throw;
            }
        }

        public void StopCamera()
        {
            if (_videoSource != null && _videoSource.IsRunning)
            {
                _videoSource.SignalToStop();
                _videoSource.WaitForStop();
                _videoSource.NewFrame -= VideoSource_NewFrame;
                _isRunning = false;
            }
        }

        public void Dispose()
        {
            StopCamera();
        }
    }
}
