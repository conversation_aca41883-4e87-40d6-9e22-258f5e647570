﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class DeviceImage
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Identifiant de l'appareil")]
        public int DeviceId { get; set; }

        [Display(Name = "Chemin de l'image")]
        public string ImagePath { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        // Navigation property
        [Display(Name = "Appareil")]
        public Device Device { get; set; }
    }
}
