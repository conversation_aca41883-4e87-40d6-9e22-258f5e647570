using Dapper;
using IRepairIT.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IRepairIT.Data.Common
{
    public class PartCommands
    {
        private readonly DataAccess _db;

        public PartCommands()
        {
            _db = new DataAccess();
        }

        public async Task<int> GetCount(string searchTerm)
        {
            const string sql = @"parts WHERE (name LIKE CONCAT('%', @searchTerm, '%') OR description LIKE CONCAT('%', @searchTerm, '%'))";
            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            return await _db.CountQuery(sql, parameters);
        }

        public async Task<IEnumerable<Part>> GetALL(string searchTerm, int offset, int pageSize)
        {
            const string sql = @"SELECT * FROM parts WHERE (Name LIKE CONCAT('%', @searchTerm, '%') OR Description LIKE CONCAT('%', @searchTerm, '%')) ORDER BY Name ASC LIMIT @pageSize OFFSET @offsetVal";

            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            parameters.Add("offsetVal", offset);
            parameters.Add("pageSize", pageSize);
            var results = await _db.QueryQuery<Part>(sql, parameters);
            return results;
        }

        public async Task<int> InsertAsync(Part part)
        {
            const string sql = @"INSERT INTO parts (Name, Description, Selling_Price, Quantity, Min_Quantity)
                                VALUES (@p_name, @p_description, @p_price, @p_quantity, @p_min_quantity);";

            var parameters = new
            {
                p_name = part.Name,
                p_description = part.Description,
                p_price = part.Selling_Price,
                p_quantity = part.Quantity,
                p_min_quantity = part.Min_Quantity,
                created_at = DateTime.Now,
                updated_at = DateTime.Now
            };

            return await _db.InsertAndGetIdAsync(sql, parameters);
        }

        public async Task<int> UpdateAsync(Part part)
        {
            const string sql = @"UPDATE parts
                                SET Name = @p_name,
                                    Description = @p_description,
                                    Selling_Price = @p_price,
                                    Quantity = @p_quantity,
                                    Min_Quantity = @p_min_quantity,
                                    UpdatedAt = CURRENT_TIMESTAMP
                                WHERE Id = @p_id;";

            var parameters = new
            {
                p_id = part.Id,
                p_name = part.Name,
                p_description = part.Description,
                p_price = part.Selling_Price,
                p_quantity = part.Quantity,
                p_min_quantity = part.Min_Quantity
            };

            return await _db.ExecuteQuery(sql, parameters);
        }

        public async Task<Part> GetByIdAsync(int id)
        {
            const string sql = @"SELECT * FROM parts WHERE Id = @p_id";

            var parameters = new
            {
                p_id = id
            };

            return await _db.QuerySingleOrDefaultQuery<Part>(sql, parameters);
        }

        public async Task<IEnumerable<Part>> SearchByNameAsync(string name)
        {
            const string sql = @"SELECT * FROM parts WHERE Name LIKE @p_name ORDER BY Name LIMIT 10";

            var parameters = new
            {
                p_name = $"%{name}%"
            };

            return await _db.QueryQuery<Part>(sql, parameters);
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                // First check if the part is used in any repair orders
                const string checkSql = @"SELECT COUNT(*) FROM repair_order_parts WHERE part_id = @p_id";
                var checkParams = new { p_id = id };
                int usageCount = await _db.ExecuteScalerQuery<int>(checkSql, checkParams);

                if (usageCount > 0)
                {
                    // Part is in use, cannot delete
                    throw new Exception($"Cette pièce est utilisée dans {usageCount} commandes de réparation et ne peut pas être supprimée.");
                }

                // If not in use, proceed with deletion
                const string deleteSql = @"DELETE FROM parts WHERE Id = @p_id";
                var deleteParams = new { p_id = id };
                int rowsAffected = await _db.ExecuteQuery(deleteSql, deleteParams);

                return rowsAffected > 0;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<bool> UpdateQuantityAsync(int id, int quantity)
        {
            const string sql = @"UPDATE parts SET Quantity = @p_quantity, UpdatedAt = CURRENT_TIMESTAMP WHERE Id = @p_id";

            var parameters = new
            {
                p_id = id,
                p_quantity = quantity
            };

            return await _db.ExecuteQuery(sql, parameters) > 0;
        }

        public async Task<IEnumerable<dynamic>> GetTotalPartsAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT
                    p.Id,
                    p.Name,
                    SUM(rop.quantity) as part_count,
                    SUM(rop.price * rop.quantity) as total_amount
                FROM parts p
                JOIN repair_order_parts rop ON p.Id = rop.part_id
                JOIN repair_orders ro ON rop.repair_order_id = ro.id
                WHERE ro.created_at BETWEEN @startDate AND @endDate
                GROUP BY p.Id, p.Name
                ORDER BY total_amount DESC";

            var parameters = new DynamicParameters();
            parameters.Add("startDate", startDate);
            parameters.Add("endDate", endDate);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        public async Task<IEnumerable<dynamic>> GetTopPartsAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT
                    p.Id,
                    p.Name,
                    SUM(rop.quantity) as part_count,
                    SUM(rop.price * rop.quantity) as total_amount
                FROM parts p
                JOIN repair_order_parts rop ON p.Id = rop.part_id
                JOIN repair_orders ro ON rop.repair_order_id = ro.id
                WHERE ro.created_at BETWEEN @startDate AND @endDate
                GROUP BY p.Id, p.Name
                ORDER BY part_count DESC
                LIMIT 10";

            var parameters = new DynamicParameters();
            parameters.Add("startDate", startDate);
            parameters.Add("endDate", endDate);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }
    }
}
