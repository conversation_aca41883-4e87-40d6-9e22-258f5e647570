using IRepairIT.Data.Common;
using IRepairIT.Models;
using Krypton.Toolkit;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FINVENTORY
{
    public partial class FRM_PART_SELECTOR : KryptonForm
    {
        private readonly InventoryCommands _inventoryCmd;
        private string _searchTerm = "";
        private int _pageSize = 10;
        private int _currentPage = 1;
        private int _totalRows = 0;

        public int SelectedPartId { get; private set; }
        public Part SelectedPart { get; private set; }
        public int SelectedQuantity { get; private set; }
        public string SearchTerm { get; set; }

        public FRM_PART_SELECTOR()
        {
            InitializeComponent();
            _inventoryCmd = new InventoryCommands();
            SelectedPartId = 0;
            SelectedPart = null;
            SelectedQuantity = 1;
        }

        private async void FRM_PART_SELECTOR_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize the grid
                InitializeGrid();

                // Set search term if provided
                if (!string.IsNullOrEmpty(SearchTerm))
                {
                    txtSearch.Text = SearchTerm;
                    _searchTerm = SearchTerm;
                }

                // Load parts
                await LoadParts();

                // Set up autocomplete for the search box
                SetupAutocomplete();

                // Set default quantity
                numQuantity.Value = 1;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void InitializeGrid()
        {
            // Configure the DataGridView
            dgvParts.AutoGenerateColumns = false;
            dgvParts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvParts.MultiSelect = false;
            dgvParts.ReadOnly = true;
            dgvParts.AllowUserToAddRows = false;
            dgvParts.AllowUserToDeleteRows = false;
            dgvParts.AllowUserToResizeRows = false;
            dgvParts.RowHeadersVisible = false;
            dgvParts.AlternatingRowsDefaultCellStyle.BackColor = System.Drawing.Color.FromArgb(240, 240, 240);

            // Create columns
            DataGridViewTextBoxColumn idColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Id",
                HeaderText = "ID",
                Name = "Id",
                Visible = false
            };

            DataGridViewTextBoxColumn codeColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Code",
                HeaderText = "Code",
                Name = "Code",
                Width = 100
            };

            DataGridViewTextBoxColumn nameColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Name",
                HeaderText = "Nom",
                Name = "Name",
                Width = 200
            };

            DataGridViewTextBoxColumn categoryColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Category",
                HeaderText = "Catégorie",
                Name = "Category",
                Width = 100
            };

            DataGridViewTextBoxColumn priceColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Selling_Price",
                HeaderText = "Prix",
                Name = "Selling_Price",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
            };

            DataGridViewTextBoxColumn quantityColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Quantity",
                HeaderText = "Stock",
                Name = "Quantity",
                Width = 60,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleRight }
            };

            // Add columns to the grid
            dgvParts.Columns.AddRange(new DataGridViewColumn[] {
                idColumn, codeColumn, nameColumn, categoryColumn, priceColumn, quantityColumn
            });

            // Set double-click event
            dgvParts.CellDoubleClick += DgvParts_CellDoubleClick;
            dgvParts.SelectionChanged += DgvParts_SelectionChanged;
        }

        private async Task LoadParts()
        {
            try
            {
                // Calculate offset based on current page and page size
                int offset = (_currentPage - 1) * _pageSize;

                // Get parts with pagination
                var parts = await _inventoryCmd.GetALL(_searchTerm, offset, _pageSize);
                _totalRows = await _inventoryCmd.GetCount(_searchTerm);

                // Set data source
                dgvParts.DataSource = parts.ToList();

                // Update pagination info
                UpdatePaginationInfo();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des pièces: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void UpdatePaginationInfo()
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            lblPagination.Text = $"Page {_currentPage} sur {totalPages} (Total: {_totalRows})";

            // Enable/disable pagination buttons
            btnFirst.Enabled = _currentPage > 1;
            btnPrev.Enabled = _currentPage > 1;
            btnNext.Enabled = _currentPage < totalPages;
            btnLast.Enabled = _currentPage < totalPages;
        }

        private async void SetupAutocomplete()
        {
            try
            {
                // Get all parts for autocomplete
                var allParts = await _inventoryCmd.GetAllForAutocomplete();

                // Create autocomplete source
                AutoCompleteStringCollection autoCompleteSource = new AutoCompleteStringCollection();
                foreach (var part in allParts)
                {
                    autoCompleteSource.Add(part.Name);
                    if (!string.IsNullOrEmpty(part.Code))
                    {
                        autoCompleteSource.Add(part.Code);
                    }
                }

                // Set up autocomplete for the search box
                txtSearch.AutoCompleteMode = AutoCompleteMode.SuggestAppend;
                txtSearch.AutoCompleteSource = AutoCompleteSource.CustomSource;
                txtSearch.AutoCompleteCustomSource = autoCompleteSource;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting up autocomplete: {ex.Message}");
            }
        }

        private void DgvParts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                SelectPart();
            }
        }

        private void DgvParts_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvParts.SelectedRows.Count > 0)
            {
                try
                {
                    // Get the selected part
                    var selectedRow = dgvParts.SelectedRows[0];
                    var part = selectedRow.DataBoundItem as Part;

                    if (part != null)
                    {
                        // Update max quantity (ensure it's at least 1 to avoid errors)
                        numQuantity.Maximum = Math.Max(1, part.Quantity);

                        // If current quantity is more than available, adjust it
                        if (part.Quantity > 0 && numQuantity.Value > part.Quantity)
                        {
                            numQuantity.Value = part.Quantity;
                        }
                        else if (part.Quantity == 0)
                        {
                            // If no stock, set to 1 but disable the select button
                            numQuantity.Value = 1;
                        }

                        // Enable/disable select button based on stock
                        btnSelect.Enabled = part.Quantity > 0;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in selection changed: {ex.Message}");
                }
            }
        }

        private void btnSelect_Click(object sender, EventArgs e)
        {
            SelectPart();
        }

        private void SelectPart()
        {
            if (dgvParts.SelectedRows.Count > 0)
            {
                try
                {
                    // Get the selected part
                    var selectedRow = dgvParts.SelectedRows[0];
                    SelectedPart = selectedRow.DataBoundItem as Part;

                    if (SelectedPart != null)
                    {
                        // Check if there's enough stock
                        if (SelectedPart.Quantity <= 0)
                        {
                            KryptonMessageBox.Show("Cette pièce n'est pas en stock.", "Stock insuffisant",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                            return;
                        }

                        if (SelectedPart.Quantity < numQuantity.Value)
                        {
                            KryptonMessageBox.Show($"Stock insuffisant. Disponible: {SelectedPart.Quantity}", "Stock insuffisant",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                            return;
                        }

                        SelectedPartId = SelectedPart.Id;
                        SelectedQuantity = (int)numQuantity.Value;
                        DialogResult = DialogResult.OK;
                        Close();
                        return;
                    }
                }
                catch (Exception ex)
                {
                    KryptonMessageBox.Show($"Erreur lors de la sélection de la pièce: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    return;
                }
            }

            KryptonMessageBox.Show("Veuillez sélectionner une pièce", "Sélection requise",
                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private async void btnSearch_Click(object sender, EventArgs e)
        {
            _searchTerm = txtSearch.Text.Trim();
            _currentPage = 1;
            await LoadParts();
        }

        private async void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.SuppressKeyPress = true;
                _searchTerm = txtSearch.Text.Trim();
                _currentPage = 1;
                await LoadParts();
            }
        }

        private async void btnClearSearch_Click(object sender, EventArgs e)
        {
            txtSearch.Text = "";
            _searchTerm = "";
            _currentPage = 1;
            await LoadParts();
        }

        private async void btnFirst_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage = 1;
                await LoadParts();
            }
        }

        private async void btnPrev_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadParts();
            }
        }

        private async void btnNext_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage++;
                await LoadParts();
            }
        }

        private async void btnLast_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage = totalPages;
                await LoadParts();
            }
        }

        private async void btnAddNew_Click(object sender, EventArgs e)
        {
            // Check if there's text in the search box that doesn't match any existing part
            string searchText = txtSearch.Text.Trim();
            if (!string.IsNullOrEmpty(searchText))
            {
                // Ask if the user wants to create a new part with this name
                var result = KryptonMessageBox.Show(
                    $"Voulez-vous créer une nouvelle pièce nommée '{searchText}'?",
                    "Créer une nouvelle pièce",
                    KryptonMessageBoxButtons.YesNo,
                    KryptonMessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Open the part creation form with the name pre-filled
                    var partForm = new FRM_INVENTORY();
                    partForm.PartName = searchText;

                    if (partForm.ShowDialog() == DialogResult.OK)
                    {
                        // Reload the parts list
                        await LoadParts();

                        // Set up autocomplete again with the new part
                        SetupAutocomplete();
                    }
                }
            }
            else
            {
                // Just open the part creation form
                var partForm = new FRM_INVENTORY();
                if (partForm.ShowDialog() == DialogResult.OK)
                {
                    // Reload the parts list
                    await LoadParts();

                    // Set up autocomplete again with the new part
                    SetupAutocomplete();
                }
            }
        }
    }
}
