﻿using IRepairIT.Data.Common;
using IRepairIT.Enums;
using IRepairIT.Models;
using Krypton.Toolkit;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FCUSTOMERS
{
    public partial class FRM_CUSTOMERS_NEW_WIZARD : KryptonForm
    {
        #region Fields
        private int _currentStep = 1;
        private const int _totalSteps = 3;

        // Data Commands
        private readonly CustomerCommands _customerCmd;
        private readonly DeviceCommands _deviceCmd;
        private readonly RepairOrderCommands _repairOrderCmd;
        private readonly ServiceCommands _serviceCmd;
        private readonly InventoryCommands _inventoryCmd;
        private readonly UserCommands _userCmd;

        // Data Models
        private Customer _customer;
        private Device _device;
        private RepairOrder _repairOrder;

        // Step Panels
        private KryptonPanel _panelStep1; // Customer Info
        private KryptonPanel _panelStep2; // Device Info
        private KryptonPanel _panelStep3; // Repair Order Info

        // Step 1 Controls (Customer)
        private KryptonTextBox _txtCustomerName;
        private KryptonTextBox _txtCustomerPhone;
        private KryptonTextBox _txtCustomerEmail;
        private KryptonTextBox _txtCustomerAddress;
        private KryptonTextBox _txtCustomerNotes;

        // Step 2 Controls (Device)
        private KryptonComboBox _cmbDeviceType;
        private KryptonTextBox _txtDeviceBrand;
        private KryptonTextBox _txtDeviceModel;
        private KryptonTextBox _txtSerialNumber;
        private KryptonTextBox _txtIMEI;
        private KryptonTextBox _txtDeviceProblem;
        private KryptonTextBox _txtDeviceCondition;
        private KryptonTextBox _txtDevicePassword;
        private KryptonTextBox _txtAccessories;
        private KryptonDateTimePicker _dtpExpectedDelivery;

        // Step 3 Controls (Repair Order)
        private KryptonTextBox _txtOrderNumber;
        private KryptonComboBox _cmbTechnician;
        private KryptonTextBox _txtWarrantyPeriod;
        private KryptonTextBox _txtRepairNotes;
        private KryptonTextBox _txtTechnicalNotes;
        private KryptonDataGridView _dgvServices;
        private KryptonDataGridView _dgvParts;
        private KryptonLabel _lblTotalCost;

        // Temporary data for services and parts
        private List<dynamic> _tempServices = new List<dynamic>();
        private List<dynamic> _tempParts = new List<dynamic>();

        public bool HasChanges { get; private set; }
        public Customer CreatedCustomer { get; private set; }
        public Device CreatedDevice { get; private set; }
        public RepairOrder CreatedRepairOrder { get; private set; }
        #endregion

        #region Constructor
        public FRM_CUSTOMERS_NEW_WIZARD()
        {
            InitializeComponent();

            // Initialize commands
            _customerCmd = new CustomerCommands();
            _deviceCmd = new DeviceCommands();
            _repairOrderCmd = new RepairOrderCommands();
            _serviceCmd = new ServiceCommands();
            _inventoryCmd = new InventoryCommands();
            _userCmd = new UserCommands();

            // Initialize models
            _customer = new Customer();
            _device = new Device();
            _repairOrder = new RepairOrder();

            // Initialize form
            InitializeWizard();
        }
        #endregion

        #region Initialization
        private void InitializeWizard()
        {
            this.Text = "Assistant de création - Nouveau client et ordre de réparation";
            this.Size = new Size(920, 520);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimumSize = new Size(900, 490);
            this.MaximumSize = new Size(1200, 700);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowIcon = false;
            this.ShowInTaskbar = false;

            // Set modern appearance
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            CreateStepPanels();
            UpdateStepDisplay();
            LoadInitialData();
        }

        private void CreateStepPanels()
        {
            // Use panels from Designer
            _panelStep1 = panelStep1;  // Customer panel from Designer
            _panelStep2 = panelStep2;  // Device panel from Designer

            // Create repair order panel programmatically for now
            _panelStep3 = CreateRepairOrderPanel();
            panelContent.Controls.Add(_panelStep3);
            _panelStep3.Dock = DockStyle.Fill;

            // Set up control references from Designer
            SetupControlReferences();

            // Show only first step
            ShowStep(1);
        }

        private void SetupControlReferences()
        {
            // Customer controls from Designer
            _txtCustomerName = txtCustomerName;
            _txtCustomerPhone = txtCustomerPhone;
            _txtCustomerEmail = txtCustomerEmail;
            _txtCustomerAddress = txtCustomerAddress;
            _txtCustomerNotes = txtCustomerNotes;

            // Device controls from Designer
            _cmbDeviceType = cmbDeviceType;
            _txtDeviceBrand = txtDeviceBrand;
            _txtDeviceModel = txtDeviceModel;
            _txtSerialNumber = txtSerialNumber;
            _txtIMEI = txtIMEI;
            _txtDeviceProblem = txtDeviceProblem;
            _txtDeviceCondition = txtDeviceCondition;
            _txtDevicePassword = txtDevicePassword;
            _txtAccessories = txtAccessories;
            _dtpExpectedDelivery = dtpExpectedDelivery;
        }

        private async void LoadInitialData()
        {
            try
            {
                // Load device types
                var deviceTypes = new string[]
                {
                    "Smartphone", "Tablette", "Ordinateur portable", "Ordinateur de bureau",
                    "Télévision", "Console de jeu", "Montre connectée", "Écouteurs",
                    "Imprimante", "Autre"
                };

                _cmbDeviceType.Items.Clear();
                foreach (var type in deviceTypes)
                {
                    _cmbDeviceType.Items.Add(type);
                }

                // Load technicians (all users for now, can be filtered later)
                var allUsers = await _userCmd.GetAllAsync();
                var technicians = allUsers.Where(u => u.Role == Enums.UserRoles.technician || u.Role == Enums.UserRoles.admin);
                _cmbTechnician.Items.Clear();
                _cmbTechnician.DisplayMember = "Full_Name";
                _cmbTechnician.ValueMember = "Id";
                foreach (var tech in technicians)
                {
                    _cmbTechnician.Items.Add(tech);
                }

                // Generate order number
                try
                {
                    string orderNumber = await _repairOrderCmd.GenerateOrderNumber();
                    _txtOrderNumber.Text = orderNumber;
                }
                catch (Exception ex)
                {
                    // If order number generation fails, use a default format
                    _txtOrderNumber.Text = $"RO-{DateTime.Now:yyyyMMdd}-TEMP";
                    System.Diagnostics.Debug.WriteLine($"Error generating order number: {ex.Message}");
                }

                // Set default warranty period
                _txtWarrantyPeriod.Text = "30";

                // Set expected delivery date (7 days from now)
                _dtpExpectedDelivery.Value = DateTime.Now.AddDays(7);

                // Initialize grids
                InitializeServicesGrid();
                InitializePartsGrid();

            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des données: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
        #endregion

        #region Panel Creation Methods

        #region Helper Methods for Styled Controls
        private KryptonLabel CreateStyledLabel(string text, bool isRequired = false)
        {
            var label = new KryptonLabel
            {
                Text = text,
                Font = new Font("Segoe UI", 9.75F, isRequired ? FontStyle.Bold : FontStyle.Regular),
                ForeColor = isRequired ? Color.FromArgb(0, 100, 105) : Color.FromArgb(64, 64, 64),
                Size = new Size(200, 23),
                StateCommon = {
                    ShortText = {
                        Color1 = isRequired ? Color.FromArgb(0, 100, 105) : Color.FromArgb(64, 64, 64),
                        Font = new Font("Segoe UI", 9.75F, isRequired ? FontStyle.Bold : FontStyle.Regular)
                    }
                }
            };
            return label;
        }

        private KryptonTextBox CreateStyledTextBox(int tabIndex, string placeholder = "")
        {
            var textBox = new KryptonTextBox
            {
                TabIndex = tabIndex,
                Size = new Size(300, 26),
                StateCommon = {
                    Border = {
                        Color1 = Color.FromArgb(200, 200, 200),
                        Color2 = Color.FromArgb(180, 180, 180),
                        Rounding = 4,
                        Width = 1
                    },
                    Content = {
                        Color1 = Color.White,
                        Font = new Font("Segoe UI", 9.75F)
                    }
                },
                StateFocus = {
                    Border = {
                        Color1 = Color.FromArgb(0, 125, 128),
                        Color2 = Color.FromArgb(0, 150, 155),
                        Width = 2
                    }
                }
            };

            if (!string.IsNullOrEmpty(placeholder))
            {
                textBox.CueHint.CueHintText = placeholder;
                textBox.CueHint.Color1 = Color.Gray;
            }

            return textBox;
        }

        private KryptonTextBox CreateStyledMultilineTextBox(int tabIndex, string placeholder = "", int height = 60)
        {
            var textBox = CreateStyledTextBox(tabIndex, placeholder);
            textBox.Multiline = true;
            textBox.Height = height;
            textBox.Size = new Size(300, height);
            textBox.ScrollBars = ScrollBars.Vertical;
            return textBox;
        }

        private KryptonComboBox CreateStyledComboBox(int tabIndex, string placeholder = "")
        {
            var comboBox = new KryptonComboBox
            {
                TabIndex = tabIndex,
                Size = new Size(300, 26),
                DropDownStyle = ComboBoxStyle.DropDownList,
                StateCommon = {
                    ComboBox = {
                        Border = {
                            Color1 = Color.FromArgb(200, 200, 200),
                            Color2 = Color.FromArgb(180, 180, 180),
                            Rounding = 4,
                            Width = 1
                        },
                        Content = {
                            Color1 = Color.White,
                            Font = new Font("Segoe UI", 9.75F)
                        }
                    }
                },
                StateFocus = {
                    ComboBox = {
                        Border = {
                            Color1 = Color.FromArgb(0, 125, 128),
                            Color2 = Color.FromArgb(0, 150, 155),
                            Width = 2
                        }
                    }
                }
            };

            return comboBox;
        }

        private KryptonDateTimePicker CreateStyledDateTimePicker(int tabIndex)
        {
            var dateTimePicker = new KryptonDateTimePicker
            {
                TabIndex = tabIndex,
                Size = new Size(200, 26),
                Format = DateTimePickerFormat.Short,
                StateCommon = {
                    Border = {
                        Color1 = Color.FromArgb(200, 200, 200),
                        Color2 = Color.FromArgb(180, 180, 180),
                        Rounding = 4,
                        Width = 1
                    },
                    Content = {
                        Color1 = Color.White,
                        Font = new Font("Segoe UI", 9.75F)
                    }
                },
                StateFocus = {
                    Border = {
                        Color1 = Color.FromArgb(0, 125, 128),
                        Color2 = Color.FromArgb(0, 150, 155),
                        Width = 2
                    }
                }
            };

            return dateTimePicker;
        }

        private KryptonButton CreateStyledButton(string text, Color backgroundColor)
        {
            var button = new KryptonButton
            {
                Text = text,
                Size = new Size(140, 35),
                StateCommon = {
                    Back = {
                        Color1 = backgroundColor,
                        Color2 = Color.FromArgb(Math.Max(0, backgroundColor.R - 20),
                                               Math.Max(0, backgroundColor.G - 20),
                                               Math.Max(0, backgroundColor.B - 20))
                    },
                    Border = {
                        Color1 = backgroundColor,
                        Rounding = 6,
                        Width = 1
                    },
                    Content = {
                        ShortText = {
                            Color1 = Color.White,
                            Font = new Font("Segoe UI", 9.75F, FontStyle.Bold)
                        }
                    }
                },
                StateHover = {
                    Back = {
                        Color1 = Color.FromArgb(Math.Min(255, backgroundColor.R + 20),
                                               Math.Min(255, backgroundColor.G + 20),
                                               Math.Min(255, backgroundColor.B + 20)),
                        Color2 = backgroundColor
                    }
                }
            };
            return button;
        }

        private KryptonDataGridView CreateStyledDataGridView()
        {
            var dgv = new KryptonDataGridView
            {
                Size = new Size(350, 120),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                RowHeadersVisible = false,
                StateCommon = {
                    Background = {
                        Color1 = Color.White
                    },
                    BackStyle = Krypton.Toolkit.PaletteBackStyle.GridBackgroundList,
                    DataCell = {
                        Border = {
                            Color1 = Color.FromArgb(224, 224, 224),
                            DrawBorders = Krypton.Toolkit.PaletteDrawBorders.All
                        }
                    },
                    HeaderColumn = {
                        Back = {
                            Color1 = Color.FromArgb(0, 125, 128),
                            Color2 = Color.FromArgb(0, 100, 105)
                        },
                        Content = {
                            Color1 = Color.White,
                            Font = new Font("Segoe UI", 9.75F, FontStyle.Bold)
                        }
                    }
                }
            };
            return dgv;
        }
        #endregion

        private KryptonPanel CreateDevicePanel()
        {
            var panel = new KryptonPanel();
            panel.Dock = DockStyle.Fill;
            panel.Padding = new Padding(30, 25, 30, 25);
            panel.StateCommon.Color1 = Color.White;
            panel.StateCommon.Color2 = Color.FromArgb(252, 254, 255);
            panel.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;

            // Create section title
            var lblTitle = new KryptonLabel {
                Text = "🔧 Informations de l'appareil",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 125, 128),
                Location = new Point(0, 0),
                Size = new Size(800, 35),
                StateCommon = { ShortText = { Color1 = Color.FromArgb(0, 125, 128) } }
            };

            // Create input controls with enhanced styling
            var lblType = CreateStyledLabel("Type d'appareil *:", true);
            _cmbDeviceType = CreateStyledComboBox(1, "Sélectionnez le type");

            var lblBrand = CreateStyledLabel("Marque *:", true);
            _txtDeviceBrand = CreateStyledTextBox(2, "Ex: Samsung, Apple, HP");

            var lblModel = CreateStyledLabel("Modèle *:", true);
            _txtDeviceModel = CreateStyledTextBox(3, "Ex: Galaxy S21, iPhone 13");

            var lblSerial = CreateStyledLabel("Numéro de série:");
            _txtSerialNumber = CreateStyledTextBox(4, "Numéro de série de l'appareil");

            var lblIMEI = CreateStyledLabel("IMEI/Code:");
            _txtIMEI = CreateStyledTextBox(5, "IMEI ou code d'identification");

            var lblProblem = CreateStyledLabel("Description du problème *:", true);
            _txtDeviceProblem = CreateStyledMultilineTextBox(6, "Décrivez le problème en détail", 80);

            var lblCondition = CreateStyledLabel("État de l'appareil:");
            _txtDeviceCondition = CreateStyledMultilineTextBox(7, "État physique, rayures, etc.", 60);

            var lblPassword = CreateStyledLabel("Mot de passe/Code:");
            _txtDevicePassword = CreateStyledTextBox(8, "Code de déverrouillage");

            var lblAccessories = CreateStyledLabel("Accessoires inclus:");
            _txtAccessories = CreateStyledMultilineTextBox(9, "Chargeur, étui, écouteurs, etc.", 60);

            var lblDelivery = CreateStyledLabel("Date de livraison prévue:");
            _dtpExpectedDelivery = CreateStyledDateTimePicker(10);

            // Position controls in a modern grid layout
            lblTitle.Location = new Point(0, 0);

            // Row 1: Type, Brand, Model
            lblType.Location = new Point(20, 50);
            _cmbDeviceType.Location = new Point(20, 75);
            _cmbDeviceType.Size = new Size(250, 26);

            lblBrand.Location = new Point(290, 50);
            _txtDeviceBrand.Location = new Point(290, 75);
            _txtDeviceBrand.Size = new Size(200, 26);

            lblModel.Location = new Point(510, 50);
            _txtDeviceModel.Location = new Point(510, 75);
            _txtDeviceModel.Size = new Size(200, 26);

            // Row 2: Serial, IMEI
            lblSerial.Location = new Point(20, 120);
            _txtSerialNumber.Location = new Point(20, 145);
            _txtSerialNumber.Size = new Size(250, 26);

            lblIMEI.Location = new Point(290, 120);
            _txtIMEI.Location = new Point(290, 145);
            _txtIMEI.Size = new Size(200, 26);

            // Row 3: Problem description (full width)
            lblProblem.Location = new Point(20, 190);
            _txtDeviceProblem.Location = new Point(20, 215);
            _txtDeviceProblem.Size = new Size(690, 80);

            // Row 4: Condition and Password
            lblCondition.Location = new Point(20, 315);
            _txtDeviceCondition.Location = new Point(20, 340);
            _txtDeviceCondition.Size = new Size(330, 60);

            lblPassword.Location = new Point(370, 315);
            _txtDevicePassword.Location = new Point(370, 340);
            _txtDevicePassword.Size = new Size(170, 26);

            // Row 5: Accessories and Delivery
            lblAccessories.Location = new Point(20, 420);
            _txtAccessories.Location = new Point(20, 445);
            _txtAccessories.Size = new Size(330, 60);

            lblDelivery.Location = new Point(370, 420);
            _dtpExpectedDelivery.Location = new Point(370, 445);
            _dtpExpectedDelivery.Size = new Size(200, 26);

            // Add controls to panel
            panel.Controls.AddRange(new Control[] {
                lblTitle, lblType, _cmbDeviceType, lblBrand, _txtDeviceBrand, lblModel, _txtDeviceModel,
                lblSerial, _txtSerialNumber, lblIMEI, _txtIMEI, lblProblem, _txtDeviceProblem,
                lblCondition, _txtDeviceCondition, lblPassword, _txtDevicePassword,
                lblAccessories, _txtAccessories, lblDelivery, _dtpExpectedDelivery
            });

            return panel;
        }

        private KryptonPanel CreateRepairOrderPanel()
        {
            var panel = new KryptonPanel();
            panel.Dock = DockStyle.Fill;
            panel.Padding = new Padding(30, 25, 30, 25);
            panel.AutoScroll = true;
            panel.StateCommon.Color1 = Color.White;
            panel.StateCommon.Color2 = Color.FromArgb(252, 254, 255);
            panel.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;

            // Create section title
            var lblTitle = new KryptonLabel {
                Text = "⚙️ Ordre de réparation",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 125, 128),
                Location = new Point(0, 0),
                Size = new Size(800, 35),
                StateCommon = { ShortText = { Color1 = Color.FromArgb(0, 125, 128) } }
            };

            // Create input controls with enhanced styling
            var lblOrderNumber = CreateStyledLabel("Numéro de commande:");
            _txtOrderNumber = CreateStyledTextBox(1);
            _txtOrderNumber.ReadOnly = true;
            _txtOrderNumber.StateCommon.Back.Color1 = Color.FromArgb(248, 248, 248);

            var lblTechnician = CreateStyledLabel("Technicien assigné:");
            _cmbTechnician = CreateStyledComboBox(2, "Sélectionnez un technicien");

            var lblWarranty = CreateStyledLabel("Période de garantie (jours):");
            _txtWarrantyPeriod = CreateStyledTextBox(3, "Ex: 30, 90, 365");

            var lblRepairNotes = CreateStyledLabel("Notes de réparation:");
            _txtRepairNotes = CreateStyledMultilineTextBox(4, "Instructions et notes pour la réparation", 60);

            var lblTechNotes = CreateStyledLabel("Notes techniques:");
            _txtTechnicalNotes = CreateStyledMultilineTextBox(5, "Détails techniques et observations", 60);

            // Services section with enhanced styling
            var lblServices = new KryptonLabel {
                Text = "🔧 Services inclus:",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 100, 105),
                StateCommon = { ShortText = { Color1 = Color.FromArgb(0, 100, 105) } }
            };
            _dgvServices = CreateStyledDataGridView();
            var btnAddService = CreateStyledButton("➕ Ajouter service", Color.FromArgb(40, 167, 69));
            var btnRemoveService = CreateStyledButton("🗑️ Supprimer", Color.FromArgb(220, 53, 69));

            // Parts section with enhanced styling
            var lblParts = new KryptonLabel {
                Text = "🔩 Pièces détachées:",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 100, 105),
                StateCommon = { ShortText = { Color1 = Color.FromArgb(0, 100, 105) } }
            };
            _dgvParts = CreateStyledDataGridView();
            var btnAddPart = CreateStyledButton("➕ Ajouter pièce", Color.FromArgb(40, 167, 69));
            var btnRemovePart = CreateStyledButton("🗑️ Supprimer", Color.FromArgb(220, 53, 69));

            // Total cost with enhanced styling
            _lblTotalCost = new KryptonLabel {
                Text = "💰 Total: 0.00 €",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 125, 128),
                StateCommon = { ShortText = { Color1 = Color.FromArgb(0, 125, 128) } }
            };

            // Position controls
            lblTitle.Location = new Point(20, 20);

            lblOrderNumber.Location = new Point(20, 60);
            _txtOrderNumber.Location = new Point(130, 57);
            _txtOrderNumber.Size = new Size(150, 23);
            lblTechnician.Location = new Point(300, 60);
            _cmbTechnician.Location = new Point(380, 57);
            _cmbTechnician.Size = new Size(200, 23);
            lblWarranty.Location = new Point(600, 60);
            _txtWarrantyPeriod.Location = new Point(720, 57);
            _txtWarrantyPeriod.Size = new Size(80, 23);

            lblRepairNotes.Location = new Point(20, 100);
            _txtRepairNotes.Location = new Point(20, 120);
            _txtRepairNotes.Size = new Size(380, 50);
            lblTechNotes.Location = new Point(420, 100);
            _txtTechnicalNotes.Location = new Point(420, 120);
            _txtTechnicalNotes.Size = new Size(380, 50);

            lblServices.Location = new Point(20, 190);
            _dgvServices.Location = new Point(20, 210);
            _dgvServices.Size = new Size(380, 120);
            btnAddService.Location = new Point(20, 340);
            btnRemoveService.Location = new Point(150, 340);

            lblParts.Location = new Point(420, 190);
            _dgvParts.Location = new Point(420, 210);
            _dgvParts.Size = new Size(380, 120);
            btnAddPart.Location = new Point(420, 340);
            btnRemovePart.Location = new Point(550, 340);

            _lblTotalCost.Location = new Point(650, 380);

            // Event handlers
            btnAddService.Click += BtnAddService_Click;
            btnRemoveService.Click += BtnRemoveService_Click;
            btnAddPart.Click += BtnAddPart_Click;
            btnRemovePart.Click += BtnRemovePart_Click;

            // Add controls to panel
            panel.Controls.AddRange(new Control[] {
                lblTitle, lblOrderNumber, _txtOrderNumber, lblTechnician, _cmbTechnician,
                lblWarranty, _txtWarrantyPeriod, lblRepairNotes, _txtRepairNotes,
                lblTechNotes, _txtTechnicalNotes, lblServices, _dgvServices,
                btnAddService, btnRemoveService, lblParts, _dgvParts,
                btnAddPart, btnRemovePart, _lblTotalCost
            });

            return panel;
        }
        #endregion

        #region Navigation and Display Methods
        private void UpdateStepDisplay()
        {
            // Update header with enhanced descriptions
            switch (_currentStep)
            {
                case 1:
                    lblStepTitle.Text = "Étape 1: Informations du client";
                    lblStepDescription.Text = "Saisissez les informations personnelles du client.";
                    break;
                case 2:
                    lblStepTitle.Text = "Étape 2: Informations de l'appareil";
                    lblStepDescription.Text = "Décrivez l'appareil à réparer et son problème.";
                    break;
                case 3:
                    lblStepTitle.Text = "Étape 3: Ordre de réparation";
                    lblStepDescription.Text = "Configurez les services, pièces et détails de la réparation.";
                    break;
            }

            // Update progress bar with smooth animation
            kryptonProgressBar1.Value = (_currentStep * 100) / _totalSteps;

            // Update buttons with enhanced styling
            btnBack.Enabled = _currentStep > 1;
            btnNext.Visible = _currentStep < _totalSteps;
            btnFinish.Visible = _currentStep == _totalSteps;

            // Show appropriate panel
            ShowStep(_currentStep);

            // Update button appearance
            UpdateButtonAppearance();
        }

        private void ShowStep(int step)
        {
            _panelStep1.Visible = step == 1;
            _panelStep2.Visible = step == 2;
            _panelStep3.Visible = step == 3;
        }

        private void UpdateButtonAppearance()
        {
            // Update Back button appearance
            if (btnBack.Enabled)
            {
                btnBack.StateCommon.Back.Color1 = Color.FromArgb(240, 240, 240);
                btnBack.StateCommon.Back.Color2 = Color.FromArgb(220, 220, 220);
                btnBack.StateCommon.Content.ShortText.Color1 = Color.Black;
            }
            else
            {
                btnBack.StateCommon.Back.Color1 = Color.FromArgb(250, 250, 250);
                btnBack.StateCommon.Back.Color2 = Color.FromArgb(240, 240, 240);
                btnBack.StateCommon.Content.ShortText.Color1 = Color.Gray;
            }

            // Update Next/Finish button appearance
            var activeButton = btnNext.Visible ? btnNext : btnFinish;
            activeButton.StateCommon.Back.Color1 = Color.FromArgb(0, 125, 128);
            activeButton.StateCommon.Back.Color2 = Color.FromArgb(0, 100, 105);
            activeButton.StateCommon.Content.ShortText.Color1 = Color.White;
        }

        private async void btnNext_Click(object sender, EventArgs e)
        {
            if (await ValidateCurrentStep())
            {
                if (_currentStep < _totalSteps)
                {
                    _currentStep++;
                    UpdateStepDisplay();

                    // If moving to step 3, update total cost
                    if (_currentStep == 3)
                    {
                        UpdateTotalCost();
                    }
                }
            }
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            if (_currentStep > 1)
            {
                _currentStep--;
                UpdateStepDisplay();
            }
        }

        private async void btnFinish_Click(object sender, EventArgs e)
        {
            if (await ValidateCurrentStep())
            {
                await SaveAllData();
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            var result = KryptonMessageBox.Show(
                "Êtes-vous sûr de vouloir annuler? Toutes les données saisies seront perdues.",
                "Confirmation",
                KryptonMessageBoxButtons.YesNo,
                KryptonMessageBoxIcon.Question
            );

            if (result == DialogResult.Yes)
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }
        #endregion

        #region Validation Methods
        private async Task<bool> ValidateCurrentStep()
        {
            switch (_currentStep)
            {
                case 1:
                    return await ValidateCustomerStep();
                case 2:
                    return ValidateDeviceStep();
                case 3:
                    return ValidateRepairOrderStep();
                default:
                    return true;
            }
        }

        private async Task<bool> ValidateCustomerStep()
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(_txtCustomerName.Text))
            {
                KryptonMessageBox.Show("Le nom du client est obligatoire.", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                _txtCustomerName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(_txtCustomerPhone.Text))
            {
                KryptonMessageBox.Show("Le téléphone du client est obligatoire.", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                _txtCustomerPhone.Focus();
                return false;
            }

            // Validate email format if provided
            if (!string.IsNullOrWhiteSpace(_txtCustomerEmail.Text))
            {
                if (!IsValidEmail(_txtCustomerEmail.Text))
                {
                    KryptonMessageBox.Show("L'adresse email n'est pas valide.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    _txtCustomerEmail.Focus();
                    return false;
                }
            }

            // Check if phone already exists
            try
            {
                bool phoneExists = await _customerCmd.PhoneExistsAsync(_txtCustomerPhone.Text.Trim(), 0);
                if (phoneExists)
                {
                    KryptonMessageBox.Show("Ce numéro de téléphone est déjà utilisé par un autre client.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    _txtCustomerPhone.Focus();
                    return false;
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la validation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                return false;
            }

            // Check if email already exists
            if (!string.IsNullOrWhiteSpace(_txtCustomerEmail.Text))
            {
                try
                {
                    bool emailExists = await _customerCmd.EmailExistsAsync(_txtCustomerEmail.Text.Trim(), 0);
                    if (emailExists)
                    {
                        KryptonMessageBox.Show("Cette adresse email est déjà utilisée par un autre client.", "Validation",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                        _txtCustomerEmail.Focus();
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    KryptonMessageBox.Show($"Erreur lors de la validation: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    return false;
                }
            }

            return true;
        }

        private bool ValidateDeviceStep()
        {
            // Validate required fields
            if (_cmbDeviceType.SelectedItem == null || string.IsNullOrWhiteSpace(_cmbDeviceType.Text))
            {
                KryptonMessageBox.Show("Le type d'appareil est obligatoire.", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                _cmbDeviceType.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(_txtDeviceBrand.Text))
            {
                KryptonMessageBox.Show("La marque de l'appareil est obligatoire.", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                _txtDeviceBrand.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(_txtDeviceModel.Text))
            {
                KryptonMessageBox.Show("Le modèle de l'appareil est obligatoire.", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                _txtDeviceModel.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(_txtDeviceProblem.Text))
            {
                KryptonMessageBox.Show("La description du problème est obligatoire.", "Validation",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                _txtDeviceProblem.Focus();
                return false;
            }

            return true;
        }

        private bool ValidateRepairOrderStep()
        {
            // Validate warranty period
            if (!string.IsNullOrWhiteSpace(_txtWarrantyPeriod.Text))
            {
                if (!int.TryParse(_txtWarrantyPeriod.Text, out int warranty) || warranty < 0)
                {
                    KryptonMessageBox.Show("La période de garantie doit être un nombre entier positif.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    _txtWarrantyPeriod.Focus();
                    return false;
                }
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
        #endregion

        #region Grid Management Methods
        private void InitializeServicesGrid()
        {
            _dgvServices.AutoGenerateColumns = false;
            _dgvServices.Columns.Clear();

            _dgvServices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ServiceName",
                HeaderText = "Service",
                DataPropertyName = "ServiceName",
                Width = 200
            });

            _dgvServices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Price",
                HeaderText = "Prix",
                DataPropertyName = "Price",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            _dgvServices.DataSource = _tempServices;
        }

        private void InitializePartsGrid()
        {
            _dgvParts.AutoGenerateColumns = false;
            _dgvParts.Columns.Clear();

            _dgvParts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PartName",
                HeaderText = "Pièce",
                DataPropertyName = "PartName",
                Width = 150
            });

            _dgvParts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = "Qté",
                DataPropertyName = "Quantity",
                Width = 50
            });

            _dgvParts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Price",
                HeaderText = "Prix",
                DataPropertyName = "Price",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            _dgvParts.DataSource = _tempParts;
        }

        private  void BtnAddService_Click(object sender, EventArgs e)
        {
            try
            {
                // Services will be loaded by the selector form

                // Create selection form
                using (var serviceSelector = new FRMS.FSERVICES.FRM_SERVICE_SELECTOR())
                {
                    if (serviceSelector.ShowDialog() == DialogResult.OK)
                    {
                        var selectedService = serviceSelector.SelectedService;
                        if (selectedService != null)
                        {
                            // Check if service already added
                            bool exists = _tempServices.Any(s => s.ServiceId == selectedService.id);
                            if (!exists)
                            {
                                _tempServices.Add(new
                                {
                                    ServiceId = selectedService.id,
                                    ServiceName = selectedService.name,
                                    Price = selectedService.price
                                });

                                RefreshServicesGrid();
                                UpdateTotalCost();
                            }
                            else
                            {
                                KryptonMessageBox.Show("Ce service a déjà été ajouté.", "Information",
                                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'ajout du service: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void BtnRemoveService_Click(object sender, EventArgs e)
        {
            if (_dgvServices.SelectedRows.Count > 0)
            {
                int index = _dgvServices.SelectedRows[0].Index;
                if (index >= 0 && index < _tempServices.Count)
                {
                    _tempServices.RemoveAt(index);
                    RefreshServicesGrid();
                    UpdateTotalCost();
                }
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un service à supprimer.", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private  void BtnAddPart_Click(object sender, EventArgs e)
        {
            try
            {
                // Create part selector form
                using (var partSelector = new FRMS.FINVENTORY.FRM_PART_SELECTOR())
                {
                    if (partSelector.ShowDialog() == DialogResult.OK)
                    {
                        var selectedPart = partSelector.SelectedPart;
                        if (selectedPart != null)
                        {
                            // Check if part already added
                            var existingPart = _tempParts.FirstOrDefault(p => p.PartId == selectedPart.Id);
                            if (existingPart == null)
                            {
                                _tempParts.Add(new
                                {
                                    PartId = selectedPart.Id,
                                    PartName = selectedPart.Name,
                                    Quantity = 1,
                                    Price = selectedPart.Selling_Price
                                });
                            }
                            else
                            {
                                // Increase quantity
                                var index = _tempParts.IndexOf(existingPart);
                                _tempParts[index] = new
                                {
                                    PartId = existingPart.PartId,
                                    PartName = existingPart.PartName,
                                    Quantity = existingPart.Quantity + 1,
                                    Price = existingPart.Price
                                };
                            }

                            RefreshPartsGrid();
                            UpdateTotalCost();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'ajout de la pièce: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void BtnRemovePart_Click(object sender, EventArgs e)
        {
            if (_dgvParts.SelectedRows.Count > 0)
            {
                int index = _dgvParts.SelectedRows[0].Index;
                if (index >= 0 && index < _tempParts.Count)
                {
                    _tempParts.RemoveAt(index);
                    RefreshPartsGrid();
                    UpdateTotalCost();
                }
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner une pièce à supprimer.", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private void RefreshServicesGrid()
        {
            _dgvServices.DataSource = null;
            _dgvServices.DataSource = _tempServices;
            _dgvServices.Refresh();
        }

        private void RefreshPartsGrid()
        {
            _dgvParts.DataSource = null;
            _dgvParts.DataSource = _tempParts;
            _dgvParts.Refresh();
        }

        private void UpdateTotalCost()
        {
            decimal serviceCost = 0;
            decimal partCost = 0;

            // Calculate service cost safely
            if (_tempServices != null && _tempServices.Count > 0)
            {
                serviceCost = _tempServices.Sum(s => (decimal)s.Price);
            }

            // Calculate part cost safely
            if (_tempParts != null && _tempParts.Count > 0)
            {
                partCost = _tempParts.Sum(p => (decimal)p.Price * (int)p.Quantity);
            }

            decimal totalCost = serviceCost + partCost;

            _lblTotalCost.Text = $"Total: {totalCost:C2}";
        }
        #endregion

        #region Save Methods
        private async Task SaveAllData()
        {
            try
            {
                Cursor = Cursors.WaitCursor;

                // Step 1: Save Customer
                await SaveCustomer();

                // Step 2: Save Device
                await SaveDevice();

                // Step 3: Save Repair Order
                await SaveRepairOrder();

                // Show success message
                KryptonMessageBox.Show(
                    "Le client, l'appareil et l'ordre de réparation ont été créés avec succès!",
                    "Succès",
                    KryptonMessageBoxButtons.OK,
                    KryptonMessageBoxIcon.Information
                );

                HasChanges = true;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private async Task SaveCustomer()
        {
            // Populate customer object
            _customer.name = _txtCustomerName.Text.Trim();
            _customer.phone = _txtCustomerPhone.Text.Trim();
            _customer.email = _txtCustomerEmail.Text.Trim();
            _customer.address = _txtCustomerAddress.Text.Trim();
            _customer.notes = _txtCustomerNotes.Text.Trim();
            _customer.created_at = DateTime.Now;

            // Save customer
            int customerId = await _customerCmd.InsertAsync(_customer);
            if (customerId <= 0)
            {
                throw new Exception("Erreur lors de la création du client");
            }

            _customer.id = customerId;
            CreatedCustomer = _customer;
        }

        private async Task SaveDevice()
        {
            // Populate device object
            _device.CustomerId = _customer.id;
            _device.Type = _cmbDeviceType.Text;
            _device.Brand = _txtDeviceBrand.Text.Trim();
            _device.Model = _txtDeviceModel.Text.Trim();
            _device.SerialNumber = _txtSerialNumber.Text.Trim();
            _device.IMEI = _txtIMEI.Text.Trim();
            _device.Problem = _txtDeviceProblem.Text.Trim();
            _device.Condition = _txtDeviceCondition.Text.Trim();
            _device.Password = _txtDevicePassword.Text.Trim();
            _device.Accessories = _txtAccessories.Text.Trim();
            _device.ReceiptDate = DateTime.Now;
            _device.ExpectedDeliveryDate = _dtpExpectedDelivery.Value;
            _device.CreatedAt = DateTime.Now;

            // Save device
            int deviceId = await _deviceCmd.InsertAsync(_device);
            if (deviceId <= 0)
            {
                throw new Exception("Erreur lors de la création de l'appareil");
            }

            _device.Id = deviceId;
            CreatedDevice = _device;
        }

        private async Task SaveRepairOrder()
        {
            // Calculate costs safely
            decimal serviceCost = 0;
            decimal partCost = 0;

            if (_tempServices != null && _tempServices.Count > 0)
            {
                serviceCost = _tempServices.Sum(s => (decimal)s.Price);
            }

            if (_tempParts != null && _tempParts.Count > 0)
            {
                partCost = _tempParts.Sum(p => (decimal)p.Price * (int)p.Quantity);
            }

            decimal totalCost = serviceCost + partCost;

            // Get technician ID
            int? technicianId = null;
            if (_cmbTechnician.SelectedItem != null)
            {
                var technician = _cmbTechnician.SelectedItem as User;
                if (technician != null)
                {
                    technicianId = technician.Id;
                }
            }

            // Get warranty period
            int? warrantyPeriod = null;
            if (int.TryParse(_txtWarrantyPeriod.Text, out int warranty))
            {
                warrantyPeriod = warranty;
            }

            // Populate repair order object
            _repairOrder.OrderNumber = _txtOrderNumber.Text;
            _repairOrder.CustomerId = _customer.id;
            _repairOrder.DeviceId = _device.Id;
            _repairOrder.Status = RepairOrderStatus.received.ToString();
            _repairOrder.TotalCost = totalCost;
            _repairOrder.ServiceCost = serviceCost;
            _repairOrder.PartCost = partCost;
            _repairOrder.PaidAmount = 0;
            _repairOrder.PaymentStatus = Enums.PaymentStatus.unpaid;
            _repairOrder.TechnicianId = technicianId;
            _repairOrder.WarrantyPeriod = warrantyPeriod;
            _repairOrder.RepairNotes = _txtRepairNotes.Text.Trim();
            _repairOrder.TechnicalNotes = _txtTechnicalNotes.Text.Trim();
            _repairOrder.CreatedAt = DateTime.Now;

            // Save repair order
            int repairOrderId = await _repairOrderCmd.InsertAsync(_repairOrder);
            if (repairOrderId <= 0)
            {
                throw new Exception("Erreur lors de la création de l'ordre de réparation");
            }

            _repairOrder.Id = repairOrderId;

            // Save services
            if (_tempServices != null && _tempServices.Count > 0)
            {
                foreach (var service in _tempServices)
                {
                    var repairOrderService = new RepairOrderService
                    {
                        RepairOrderId = repairOrderId,
                        ServiceId = (int)service.ServiceId,
                        Price = (decimal)service.Price,
                        CreatedAt = DateTime.Now
                    };
                    await _repairOrderCmd.AddServiceAsync(repairOrderService);
                }
            }

            // Save parts
            if (_tempParts != null && _tempParts.Count > 0)
            {
                foreach (var part in _tempParts)
                {
                    var repairOrderPart = new RepairOrderPart
                    {
                        RepairOrderId = repairOrderId,
                        PartId = (int)part.PartId,
                        Quantity = (int)part.Quantity,
                        Price = (decimal)part.Price,
                        CreatedAt = DateTime.Now
                    };
                    await _repairOrderCmd.AddPartAsync(repairOrderPart);
                }
            }

            CreatedRepairOrder = _repairOrder;
        }
        #endregion

        #region Event Handlers
        private void FRM_CUSTOMERS_NEW_WIZARD_Load(object sender, EventArgs e)
        {
            // Wire up button events
            btnNext.Click += btnNext_Click;
            btnBack.Click += btnBack_Click;
            btnFinish.Click += btnFinish_Click;
            btnCancel.Click += btnCancel_Click;
        }
        #endregion
    }
}