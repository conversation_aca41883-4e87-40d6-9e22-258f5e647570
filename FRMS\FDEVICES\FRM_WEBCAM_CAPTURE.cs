using IRepairIT.utilities;
using Krypton.Toolkit;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FDEVICES
{
    public partial class FRM_WEBCAM_CAPTURE : KryptonForm
    {
        private WebcamCapture _webcam;
        private string _capturedImagePath;
        private string _imagesDirectory;

        public string CapturedImagePath => _capturedImagePath;
        public bool HasCapturedImage { get; private set; }

        public FRM_WEBCAM_CAPTURE()
        {
            InitializeComponent();

            // Create images directory if it doesn't exist
            _imagesDirectory = Path.Combine(Application.StartupPath, "Images", "Devices");
            if (!Directory.Exists(_imagesDirectory))
            {
                Directory.CreateDirectory(_imagesDirectory);
            }
        }

        private void FRM_WEBCAM_CAPTURE_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize webcam
                _webcam = new WebcamCapture(pictureBoxWebcam);
                _webcam.ImageCaptured += Webcam_ImageCaptured;

                // Load available cameras
                string[] cameras = _webcam.GetAvailableCameras();
                cmbCameras.Items.AddRange(cameras);
                
                if (cmbCameras.Items.Count > 0)
                {
                    cmbCameras.SelectedIndex = 0;
                    btnStartCamera.Enabled = true;
                }
                else
                {
                    KryptonMessageBox.Show("Aucune webcam détectée", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    btnStartCamera.Enabled = false;
                    btnCapture.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'initialisation de la webcam: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                btnStartCamera.Enabled = false;
                btnCapture.Enabled = false;
            }
        }

        private void Webcam_ImageCaptured(object sender, Bitmap capturedImage)
        {
            try
            {
                // Create a temporary file name
                string fileName = $"webcam_{Guid.NewGuid()}.jpg";
                _capturedImagePath = Path.Combine(_imagesDirectory, fileName);

                // Save the image
                capturedImage.Save(_capturedImagePath, ImageFormat.Jpeg);
                
                // Update UI
                pictureBoxCaptured.Image = capturedImage;
                btnSave.Enabled = true;
                HasCapturedImage = true;
                
                // Show success message
                lblStatus.Text = "Image capturée avec succès";
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la capture de l'image: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                lblStatus.Text = "Erreur lors de la capture";
            }
        }

        private void btnStartCamera_Click(object sender, EventArgs e)
        {
            try
            {
                if (btnStartCamera.Text == "Démarrer la caméra")
                {
                    _webcam.StartCamera(cmbCameras.SelectedIndex);
                    btnStartCamera.Text = "Arrêter la caméra";
                    btnCapture.Enabled = true;
                    cmbCameras.Enabled = false;
                    lblStatus.Text = "Caméra démarrée";
                }
                else
                {
                    _webcam.StopCamera();
                    btnStartCamera.Text = "Démarrer la caméra";
                    btnCapture.Enabled = false;
                    cmbCameras.Enabled = true;
                    lblStatus.Text = "Caméra arrêtée";
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du démarrage/arrêt de la caméra: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                lblStatus.Text = "Erreur caméra";
            }
        }

        private void btnCapture_Click(object sender, EventArgs e)
        {
            try
            {
                _webcam.CaptureImage();
                lblStatus.Text = "Capture en cours...";
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la capture: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                lblStatus.Text = "Erreur capture";
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            // Delete captured image if exists
            if (HasCapturedImage && !string.IsNullOrEmpty(_capturedImagePath) && File.Exists(_capturedImagePath))
            {
                try
                {
                    File.Delete(_capturedImagePath);
                }
                catch
                {
                    // Ignore errors
                }
            }

            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void FRM_WEBCAM_CAPTURE_FormClosing(object sender, FormClosingEventArgs e)
        {
            // Stop the webcam
            _webcam?.Dispose();
        }
    }
}
