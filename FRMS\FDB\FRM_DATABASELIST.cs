﻿using IRepairIT.DB;
using Krypton.Toolkit;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FDB
{
    public partial class FRM_DATABASELIST : KryptonForm
    {

        private string _settingsFilePath;

        public string SelectedDatabase { get; private set; }
        public FRM_DATABASELIST()
        {
            InitializeComponent();
            _settingsFilePath = Path.Combine(Application.StartupPath, "connection.json");

            // Lier l'événement de clic sur le bouton de fermeture
            btnClose.Click += BtnClose_Click;

            // Ajouter un événement pour la touche Escape pour fermer l'application
            this.KeyPreview = true;
            this.KeyDown += FRM_DATABASELIST_KeyDown;
           
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            // Afficher un message de confirmation
            if (KryptonMessageBox.Show("Voulez-vous vraiment quitter l'application ?", "Confirmation", KryptonMessageBoxButtons.YesNo, KryptonMessageBoxIcon.Question) == DialogResult.Yes)
            {
                // Fermer l'application
                Application.Exit();
            }
        }

     
        private void FRM_DATABASELIST_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                // Afficher un message de confirmation
                if (KryptonMessageBox.Show("Voulez-vous vraiment quitter l'application ?", "Confirmation", KryptonMessageBoxButtons.YesNo, KryptonMessageBoxIcon.Question) == DialogResult.Yes)
                {
                    // Fermer l'application
                    Application.Exit();
                }
            }
        }

        private void FRM_DATABASELIST_Load(object sender, System.EventArgs e)
        {

            LoadDatabases();
        }
        private void LoadDatabases()
        {
            try
            {
                lvDatabases.Items.Clear();
                var settings = ConnectionSettings.LoadFromJson(_settingsFilePath);
                string connectionString = settings.GetConnectionString("");

                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();

                    var command = new MySqlCommand("SHOW DATABASES LIKE 'irepairit%'", connection);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string dbName = reader.GetString(0);

                            string year = dbName.Replace("irepairit", "");

                            var item = new ListViewItem(year);
                            item.ImageIndex = 0;
                            item.Tag = dbName;
                            lvDatabases.Items.Add(item);
                        }
                    }
                }

                lvDatabases.ListViewItemSorter = new YearComparer();
                lvDatabases.Sort();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des dossiers: {ex.Message}", "Erreur", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void lvDatabases_TabIndexChanged(object sender, EventArgs e)
        {
            bool hasSelection = lvDatabases.SelectedItems.Count > 0;
            btnDelete.Enabled = hasSelection;
            btnDuplicate.Enabled = hasSelection;
            btnSelect.Enabled = hasSelection;
        }

        private void lvDatabases_DoubleClick(object sender, EventArgs e)
        {
            if (lvDatabases.SelectedItems.Count > 0)
            {
                SelectDatabase();
            }
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            var setupWizard = new FRM_SETUPDB();
            this.Hide();
            setupWizard.ShowDialog();

            LoadDatabases();
            this.Show();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (lvDatabases.SelectedItems.Count == 0)
                return;

            string year = lvDatabases.SelectedItems[0].Text;
            string dbName = lvDatabases.SelectedItems[0].Tag.ToString();

            if (KryptonMessageBox.Show($"Êtes-vous sûr de vouloir supprimer le dossier {year} ?\nCette action est irréversible.", "Confirmation", KryptonMessageBoxButtons.YesNo, KryptonMessageBoxIcon.Warning) != DialogResult.Yes)
                return;

            if (DeleteDatabase(dbName))
            {
                KryptonMessageBox.Show($"Dossier {year} supprimé avec succès.", "Succès", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                LoadDatabases();
            }
        }

        private void btnDuplicate_Click(object sender, EventArgs e)
        {
            if (lvDatabases.SelectedItems.Count == 0)
                return;

            string sourceYear = lvDatabases.SelectedItems[0].Text;
            string sourceDbName = lvDatabases.SelectedItems[0].Tag.ToString();

            using (var form = new FRM_YEARINPUT())
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    int targetYear = form.Year;
                    string targetDbName = $"irepairit{targetYear}";

                    if (DatabaseExists(targetDbName))
                    {
                        KryptonMessageBox.Show($"Le dossier pour l'année {targetYear} existe déjà.", "Information", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        return;
                    }

                    if (DuplicateDatabase(sourceDbName, targetDbName))
                    {
                        KryptonMessageBox.Show($"Dossier {sourceYear} dupliqué vers {targetYear} avec succès.", "Succès", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        LoadDatabases();
                    }
                }
            }
        }

        private void btnSelect_Click(object sender, EventArgs e)
        {
            SelectDatabase();
        }
        private void SelectDatabase()
        {
            if (lvDatabases.SelectedItems.Count == 0)
                return;

            SelectedDatabase = lvDatabases.SelectedItems[0].Tag.ToString();

            var settings = ConnectionSettings.LoadFromJson(_settingsFilePath);
            string connectionString = settings.GetConnectionString(SelectedDatabase);

            Properties.Settings.Default.CurrentConnectionString = connectionString;
            Properties.Settings.Default.CurrentDatabase = SelectedDatabase;

            if (chkDontShowAgain.Checked)
            {
                Properties.Settings.Default.ShowDatabaseSelector = false;
                Properties.Settings.Default.DefaultDatabase = SelectedDatabase;
            }
            else
            {
                Properties.Settings.Default.ShowDatabaseSelector = true;
                Properties.Settings.Default.DefaultDatabase = "";
            }

            Properties.Settings.Default.Save();

            DialogResult = DialogResult.OK;
            Close();
        }

        private bool DatabaseExists(string dbName)
        {
            try
            {
                var settings = ConnectionSettings.LoadFromJson(_settingsFilePath);
                string connectionString = settings.GetConnectionString("");

                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();

                    var command = new MySqlCommand($"SELECT COUNT(*) FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{dbName}'", connection);
                    int count = Convert.ToInt32(command.ExecuteScalar());

                    return count > 0;
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la vérification de l'existence de la base de données: {ex.Message}", "Erreur", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                return false;
            }
        }


        private bool DeleteDatabase(string dbName)
        {
            try
            {
                var settings = ConnectionSettings.LoadFromJson(_settingsFilePath);
                string connectionString = settings.GetConnectionString("");

                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();

                    var command = new MySqlCommand($"DROP DATABASE {dbName}", connection);
                    command.ExecuteNonQuery();

                    return true;
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la suppression de la base de données: {ex.Message}", "Erreur", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                return false;
            }
        }

        private bool DuplicateDatabase(string sourceDbName, string targetDbName)
        {
            try
            {
                var settings = ConnectionSettings.LoadFromJson(_settingsFilePath);
                string connectionString = settings.GetConnectionString("");
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();

                    var createDbCommand = new MySqlCommand($"CREATE DATABASE {targetDbName + "_copies"}", connection);
                    createDbCommand.ExecuteNonQuery();

                    var tablesCommand = new MySqlCommand($"SELECT table_name FROM information_schema.tables WHERE table_schema = '{sourceDbName}'", connection);
                    var tables = new List<string>();
                    using (var reader = tablesCommand.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            tables.Add(reader.GetString(0));
                        }
                    }

                    foreach (var table in tables)
                    {
                        var createTableCommand = new MySqlCommand($"CREATE TABLE {targetDbName + "_copies"}.{table} LIKE {sourceDbName}.{table}", connection);
                        createTableCommand.ExecuteNonQuery();

                        var copyDataCommand = new MySqlCommand($"INSERT INTO {targetDbName + "_copies"}.{table} SELECT * FROM {sourceDbName}.{table}", connection);
                        copyDataCommand.ExecuteNonQuery();
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la duplication de la base de données: {ex.Message}", "Erreur", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                return false;
            }
        }

        private void btnClose_Click_1(object sender, EventArgs e)
        {
            if (KryptonMessageBox.Show("Voulez-vous vraiment quitter l'application ?", "Confirmation", KryptonMessageBoxButtons.YesNo, KryptonMessageBoxIcon.Question) == DialogResult.Yes)
            {
                // Fermer l'application
                Application.Exit();
            }
        }
    }
    public class YearComparer : System.Collections.IComparer
    {
        public int Compare(object x, object y)
        {
            ListViewItem item1 = (ListViewItem)x;
            ListViewItem item2 = (ListViewItem)y;

            int year1 = int.Parse(item1.Text);
            int year2 = int.Parse(item2.Text);

            return year2.CompareTo(year1);
        }
    }
}
