using IRepairIT.Data.Common;
using IRepairIT.Models;
using Krypton.Toolkit;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FCOMMON
{
    public partial class FRM_SELECT_CUSTOMER : KryptonForm
    {
        private readonly CustomerCommands _customerCmd;
        private string _searchTerm = "";
        private int _pageSize = 10;
        private int _currentPage = 1;
        private int _totalRows = 0;

        public Customer SelectedCustomer { get; private set; }

        public FRM_SELECT_CUSTOMER()
        {
            InitializeComponent();
            _customerCmd = new CustomerCommands();
            SelectedCustomer = null;
        }

        private async void FRM_SELECT_CUSTOMER_Load(object sender, EventArgs e)
        {
            // Initialize the grid
            InitializeGrid();

            // Load customers
            await LoadCustomers();
        }

        private void InitializeGrid()
        {
            // Configure the DataGridView
            dgvCustomers.AutoGenerateColumns = false;

            // Add columns
            var idColumn = new KryptonDataGridViewTextBoxColumn
            {
                Name = "id",
                HeaderText = "ID",
                DataPropertyName = "id",
                Width = 50
            };

            var firstNameColumn = new KryptonDataGridViewTextBoxColumn
            {
                Name = "firstName",
                HeaderText = "Prénom",
                DataPropertyName = "firstName",
                Width = 150
            };

            var lastNameColumn = new KryptonDataGridViewTextBoxColumn
            {
                Name = "lastName",
                HeaderText = "Nom",
                DataPropertyName = "lastName",
                Width = 150
            };

            var customerColumn = new KryptonDataGridViewTextBoxColumn
            {
                Name = "fullCustomer",
                HeaderText = "Customer",
                DataPropertyName = "fullCustomer",
                Visible = false
            };

            dgvCustomers.Columns.AddRange(new DataGridViewColumn[] { idColumn, firstNameColumn, lastNameColumn, customerColumn });

            // Set selection mode
            dgvCustomers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCustomers.MultiSelect = false;

            // Set event handlers
            dgvCustomers.CellDoubleClick += DgvCustomers_CellDoubleClick;
            dgvCustomers.SelectionChanged += DgvCustomers_SelectionChanged;
        }

        private async Task LoadCustomers()
        {
            try
            {
                // Calculate offset based on current page and page size
                int offset = (_currentPage - 1) * _pageSize;

                // Get customers with pagination
                var customers = await _customerCmd.GetALL(_searchTerm, offset, _pageSize);
                _totalRows = await _customerCmd.GetCount(_searchTerm);

                // Create a DataTable to hold the customer data
                var dataTable = new DataTable();
                dataTable.Columns.Add("id", typeof(int));
                dataTable.Columns.Add("firstName", typeof(string));
                dataTable.Columns.Add("lastName", typeof(string));
                dataTable.Columns.Add("fullCustomer", typeof(Customer));

                // Fill the DataTable with customer data
                foreach (var customer in customers)
                {
                    // Split the name into first name and last name (assuming format is "FirstName LastName")
                    string firstName = customer.name;
                    string lastName = "";

                    // Try to split the name if it contains a space
                    if (customer.name.Contains(" "))
                    {
                        var nameParts = customer.name.Split(new[] { ' ' }, 2);
                        firstName = nameParts[0];
                        lastName = nameParts[1];
                    }

                    dataTable.Rows.Add(customer.id, firstName, lastName, customer);
                }

                // Set the DataSource
                dgvCustomers.DataSource = dataTable;

                // Update pagination info
                lblPagination.Text = $"Total: {_totalRows} - Page: {_currentPage}/{Math.Ceiling((double)_totalRows / _pageSize)}";

                // Enable/disable pagination buttons
                btnFirst.Enabled = btnPrev.Enabled = _currentPage > 1;
                btnNext.Enabled = btnLast.Enabled = _currentPage < Math.Ceiling((double)_totalRows / _pageSize);
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des clients: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void DgvCustomers_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvCustomers.SelectedRows[0];

                try
                {
                    // Get the customer data from the DataTable row
                    DataRowView rowView = (DataRowView)selectedRow.DataBoundItem;
                    var customer = rowView["fullCustomer"] as Customer;

                    if (customer != null)
                    {
                        // Update the ComboBox with the selected customer
                        cmbCustomer.Text = customer.name;
                    }
                }
                catch (Exception ex)
                {
                    // Just log the error, don't show a message box to avoid cascading errors
                    System.Diagnostics.Debug.WriteLine($"Error in selection change: {ex.Message}");
                }
            }
        }

        private void DgvCustomers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                SelectCustomer();
            }
        }

        private void SelectCustomer()
        {
            if (dgvCustomers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvCustomers.SelectedRows[0];

                try
                {
                    // Get the customer data from the DataTable row
                    DataRowView rowView = (DataRowView)selectedRow.DataBoundItem;
                    var customer = rowView["fullCustomer"] as Customer;

                    if (customer != null)
                    {
                        SelectedCustomer = customer;
                        DialogResult = DialogResult.OK;
                        Close();
                        return;
                    }
                }
                catch (Exception ex)
                {
                    KryptonMessageBox.Show($"Erreur lors de la sélection du client: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    return;
                }
            }

            KryptonMessageBox.Show("Veuillez sélectionner un client", "Sélection requise",
                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            _searchTerm = txtSearch.Text.Trim();
            _currentPage = 1;
            await LoadCustomers();
        }

        private async void BtnClearSearch_Click(object sender, EventArgs e)
        {
            txtSearch.Text = "";
            _searchTerm = "";
            _currentPage = 1;
            await LoadCustomers();
        }

        private async void BtnFirst_Click(object sender, EventArgs e)
        {
            _currentPage = 1;
            await LoadCustomers();
        }

        private async void BtnPrev_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadCustomers();
            }
        }

        private async void BtnNext_Click(object sender, EventArgs e)
        {
            if (_currentPage < Math.Ceiling((double)_totalRows / _pageSize))
            {
                _currentPage++;
                await LoadCustomers();
            }
        }

        private async void BtnLast_Click(object sender, EventArgs e)
        {
            _currentPage = (int)Math.Ceiling((double)_totalRows / _pageSize);
            await LoadCustomers();
        }

        private void BtnSelect_Click(object sender, EventArgs e)
        {
            SelectCustomer();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void TxtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                BtnSearch_Click(sender, e);
            }
        }
    }
}
