﻿using IRepairIT.Data.Common;
using IRepairIT.Enums;
using Krypton.Toolkit;
using System;
using System.ComponentModel.DataAnnotations;
using System.Drawing;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FMAIN
{
    public partial class FRM_DASHBOARD : KryptonForm
    {
        public DashboardCommands Cmd;
        public FRM_DASHBOARD()
        {
            InitializeComponent();
            Cmd = new DashboardCommands();
            SetupPanelColors();
        }

        private void SetupPanelColors()
        {
            // Set background color for the main panel
            kryptonPanel1.StateCommon.Color1 = Color.FromArgb(245, 245, 245);

            // Set colors for summary panels - Using more vibrant colors
            // Clients panel - Vibrant Blue
            kryptonPanel2.StateCommon.Color1 = Color.FromArgb(179, 229, 252);
            kryptonPanel2.StateCommon.Color2 = Color.FromArgb(144, 202, 249);

            // Devices panel - Vibrant Green
            kryptonPanel3.StateCommon.Color1 = Color.FromArgb(200, 230, 201);
            kryptonPanel3.StateCommon.Color2 = Color.FromArgb(165, 214, 167);

            // Orders panel - Vibrant Orange
            kryptonPanel4.StateCommon.Color1 = Color.FromArgb(255, 224, 178);
            kryptonPanel4.StateCommon.Color2 = Color.FromArgb(255, 204, 128);

            // Debts panel - Vibrant Purple
            kryptonPanel5.StateCommon.Color1 = Color.FromArgb(225, 190, 231);
            kryptonPanel5.StateCommon.Color2 = Color.FromArgb(206, 147, 216);

            // Style the header groups with more vibrant colors
            SetupHeaderGroup(kryptonHeaderGroup5, "Clients (récents)", Color.FromArgb(41, 121, 255));
            SetupHeaderGroup(kryptonHeaderGroup6, "Ordres de réparation (récents)", Color.FromArgb(41, 121, 255));

            // Style the data grids
            SetupDataGrid(kryptonDataGridView1);
            SetupDataGrid(kryptonDataGridView2);
        }

        private void SetupHeaderGroup(KryptonHeaderGroup headerGroup, string title, Color color)
        {
            // Create a gradient effect for the header
            headerGroup.StateCommon.HeaderPrimary.Back.Color1 = color;
            headerGroup.StateCommon.HeaderPrimary.Back.Color2 = Color.FromArgb(
                Math.Max(0, color.R - 30),
                Math.Max(0, color.G - 30),
                Math.Max(0, color.B - 30));
            headerGroup.StateCommon.HeaderPrimary.Back.ColorStyle = PaletteColorStyle.LinearShadow;
            headerGroup.StateCommon.HeaderPrimary.Back.ColorAngle = 45;

            // Improve text appearance
            headerGroup.StateCommon.HeaderPrimary.Content.ShortText.Color1 = Color.White;
            headerGroup.StateCommon.HeaderPrimary.Content.ShortText.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            headerGroup.StateCommon.HeaderPrimary.Content.ShortText.TextH = PaletteRelativeAlign.Center;

            // Set title
            headerGroup.ValuesPrimary.Heading = title;

            // Improve panel appearance
            headerGroup.StateCommon.Back.Color1 = Color.White;
            headerGroup.StateCommon.Back.Color2 = Color.FromArgb(250, 250, 250);
            headerGroup.StateCommon.Back.ColorStyle = PaletteColorStyle.Solid;

            // Improve border appearance
            headerGroup.StateCommon.Border.DrawBorders = PaletteDrawBorders.All;
            headerGroup.StateCommon.Border.Rounding = 12;
            headerGroup.StateCommon.Border.Width = 1;
            headerGroup.StateCommon.Border.Color1 = Color.FromArgb(224, 224, 224);

            // Add shadow effect
            headerGroup.StateCommon.Border.ColorStyle = PaletteColorStyle.Solid;
        }

        private void SetupDataGrid(KryptonDataGridView grid)
        {
            grid.StateCommon.Background.Color1 = Color.White;
            grid.StateCommon.Background.Color2 = Color.White;
            // More vibrant header colors
            grid.StateCommon.HeaderColumn.Back.Color1 = Color.FromArgb(41, 121, 255);
            grid.StateCommon.HeaderColumn.Back.Color2 = Color.FromArgb(30, 100, 230);
            grid.StateCommon.HeaderColumn.Content.Color1 = Color.White;
            grid.StateCommon.HeaderColumn.Content.Font = new Font("Segoe UI", 9.75F, FontStyle.Bold);
            grid.StateCommon.DataCell.Back.Color1 = Color.White;
            grid.StateCommon.DataCell.Border.Color1 = Color.FromArgb(224, 224, 224);
            grid.StateCommon.DataCell.Border.DrawBorders = PaletteDrawBorders.Bottom;
            grid.StateCommon.DataCell.Content.Font = new Font("Segoe UI", 9.75F);
            grid.RowHeadersVisible = false;
            // Improved alternating row color for better readability
            grid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(240, 249, 255);
            grid.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            // Set selection colors for better visibility
            grid.StateCommon.DataCell.Back.ColorStyle = PaletteColorStyle.Solid;
            grid.StateSelected.DataCell.Back.Color1 = Color.FromArgb(187, 222, 251);
            grid.StateSelected.DataCell.Content.Color1 = Color.Black;
            grid.ReadOnly = true;
        }

        private async void FRM_DASHBOARD_Load(object sender, EventArgs e)
        {
            try
            {
                // Load summary statistics
                lblClientCount.Text = (await Cmd.CustomersCount()).ToString();
                lblDevicesCount.Text = (await Cmd.DevicesCount()).ToString();
                LblOrderCount.Text = (await Cmd.OrdersCount()).ToString();
                LblDebts.Text = (await Cmd.TotalDebts()).ToString("C");

                // Load latest data
                kryptonDataGridView1.DataSource = await Cmd.LatestOrders();
                kryptonDataGridView2.DataSource = await Cmd.LatestCustomers();

                // Add cell formatting event handler
                kryptonDataGridView1.CellFormatting += KryptonDataGridView1_CellFormatting;

                // Format data grids
                FormatDataGrids();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show("Erreur lors du chargement des données: " + ex.Message, "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void FormatDataGrids()
        {
            // Format orders grid
            if (kryptonDataGridView1.Columns.Count > 0)
            {
                if (kryptonDataGridView1.Columns.Contains("id"))
                    kryptonDataGridView1.Columns["id"].Visible = false;
                kryptonDataGridView1.AutoResizeColumns();
            }

            // Format customers grid
            if (kryptonDataGridView2.Columns.Count > 0)
            {
                if (kryptonDataGridView2.Columns.Contains("id"))
                    kryptonDataGridView2.Columns["id"].Visible = false;
                kryptonDataGridView2.AutoResizeColumns();
            }
        }

        private void KryptonDataGridView1_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex >= 0 && kryptonDataGridView1.Columns[e.ColumnIndex].Name == "Status" && e.Value != null)
            {
                // Format status column
                string status = e.Value.ToString();

                // Try to parse the status string to enum for display purposes
                if (Enum.TryParse(status, out IRepairIT.Enums.RepairOrderStatus statusEnum))
                {
                    // Get display name from enum
                    var displayAttribute = statusEnum.GetType()
                        .GetField(statusEnum.ToString())
                        .GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.DisplayAttribute), false);

                    if (displayAttribute.Length > 0)
                    {
                        e.Value = ((System.ComponentModel.DataAnnotations.DisplayAttribute)displayAttribute[0]).Name;
                    }

                    // Set cell color based on status with more vibrant colors
                    switch (statusEnum)
                    {
                        case IRepairIT.Enums.RepairOrderStatus.received:
                            e.CellStyle.BackColor = Color.FromArgb(179, 229, 252); // Vibrant Light Blue
                            e.CellStyle.ForeColor = Color.FromArgb(13, 71, 161);   // Dark Blue text
                            break;
                        case IRepairIT.Enums.RepairOrderStatus.diagnosed:
                            e.CellStyle.BackColor = Color.FromArgb(255, 245, 157); // Vibrant Light Yellow
                            e.CellStyle.ForeColor = Color.FromArgb(130, 119, 23);  // Dark Yellow text
                            break;
                        case IRepairIT.Enums.RepairOrderStatus.waiting_parts:
                            e.CellStyle.BackColor = Color.FromArgb(255, 205, 210); // Vibrant Light Pink
                            e.CellStyle.ForeColor = Color.FromArgb(183, 28, 28);   // Dark Red text
                            break;
                        case IRepairIT.Enums.RepairOrderStatus.in_progress:
                            e.CellStyle.BackColor = Color.FromArgb(200, 230, 201); // Vibrant Light Green
                            e.CellStyle.ForeColor = Color.FromArgb(27, 94, 32);    // Dark Green text
                            break;
                        case IRepairIT.Enums.RepairOrderStatus.repaired:
                            e.CellStyle.BackColor = Color.FromArgb(165, 214, 167); // Medium Green
                            e.CellStyle.ForeColor = Color.FromArgb(27, 94, 32);    // Dark Green text
                            break;
                        case IRepairIT.Enums.RepairOrderStatus.ready:
                            e.CellStyle.BackColor = Color.FromArgb(129, 199, 132); // Stronger Green
                            e.CellStyle.ForeColor = Color.FromArgb(27, 94, 32);    // Dark Green text
                            break;
                        case IRepairIT.Enums.RepairOrderStatus.delivered:
                            e.CellStyle.BackColor = Color.FromArgb(224, 224, 224); // Light Gray
                            e.CellStyle.ForeColor = Color.FromArgb(66, 66, 66);    // Dark Gray text
                            break;
                        case IRepairIT.Enums.RepairOrderStatus.cancelled:
                            e.CellStyle.BackColor = Color.FromArgb(239, 154, 154); // Vibrant Light Red
                            e.CellStyle.ForeColor = Color.FromArgb(183, 28, 28);   // Dark Red text
                            break;
                        case IRepairIT.Enums.RepairOrderStatus.unrepairable:
                            e.CellStyle.BackColor = Color.FromArgb(229, 115, 115); // Medium Red
                            e.CellStyle.ForeColor = Color.FromArgb(183, 28, 28);   // Dark Red text
                            break;
                    }
                }
            }
        }
    }
}
