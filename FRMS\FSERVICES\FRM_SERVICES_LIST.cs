﻿using FastReport;
using IRepairIT.Data.Common;
using IRepairIT.Models;
using Krypton.Toolkit;
using System;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FSERVICES
{
    public partial class FRM_SERVICES_LIST : KryptonForm
    {
        private ServiceCommands _Cmd;
        private int _pageSize;
        private int _CurrentPage = 1;
        private int _totalRows;
        private string _searchTerm = "";
        public FRM_SERVICES_LIST()
        {
            InitializeComponent();

            // Set default page size to 100
            CmbPageView.SelectedIndex = 2;
            _pageSize = 100;

            // Initialize service commands
            _Cmd = new ServiceCommands();

            // Set button icons and text
            btnFirst.Text = "Premier";
            btnPrev.Text = "Précédent";
            btnNext.Text = "Suivant";
            btnLast.Text = "Dernier";

            // Set up context menu
            SetupContextMenu();
        }
        public async Task LOAD_ALL()
        {
            try
            {
                // Get page size from combo box
                _pageSize = Convert.ToInt32(CmbPageView.Text);

                // Calculate offset based on current page and page size
                int offset = (_CurrentPage - 1) * _pageSize;

                // Get services with pagination
                var result = await _Cmd.GetALL(_searchTerm, offset, _pageSize);
                _totalRows = await _Cmd.GetCount(_searchTerm);

                // Set data source
                kryptonDataGridView1.DataSource = result;

                // Hide unnecessary columns
                kryptonDataGridView1.Columns[nameof(Service.id)].Visible = false;
                kryptonDataGridView1.Columns[nameof(Service.created_at)].Visible = false;
                kryptonDataGridView1.Columns[nameof(Service.updated_at)].Visible = false;

                // Format price column
                if (kryptonDataGridView1.Columns.Contains(nameof(Service.price)))
                {
                    kryptonDataGridView1.Columns[nameof(Service.price)].DefaultCellStyle.Format = "C2";
                    kryptonDataGridView1.Columns[nameof(Service.price)].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                }

                // Format duration column
                if (kryptonDataGridView1.Columns.Contains(nameof(Service.duration)))
                {
                    kryptonDataGridView1.Columns[nameof(Service.duration)].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                }

                // Update header with pagination info
                int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
                kryptonHeaderGroup1.ValuesSecondary.Heading = $"Total: {_totalRows} - Page: {_CurrentPage}/{Math.Max(1, totalPages)}";

                // Update pagination buttons
                UpdatePaginationButtons();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des services: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
        private void UpdatePaginationButtons()
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);

            // Enable/disable pagination buttons based on current page
            btnFirst.Enabled = _CurrentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnPrev.Enabled = _CurrentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnNext.Enabled = _CurrentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
            btnLast.Enabled = _CurrentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
        }

        private async void FRM_SERVICES_LIST_Load(object sender, System.EventArgs e)
        {
            try
            {
                // Set row height
                kryptonDataGridView1.RowTemplate.Height = 32;

                // Load services data
                await LOAD_ALL();

                // Apply enhanced styling
                IRepairIT.Utilities.DGV_STYLE.DesignDGV(kryptonDataGridView1);

                // Add cell formatting event handler
                kryptonDataGridView1.CellFormatting += KryptonDataGridView1_CellFormatting;

                // Set header colors
                kryptonHeaderGroup1.StateCommon.HeaderPrimary.Back.Color1 = Color.FromArgb(45, 62, 80);
                kryptonHeaderGroup1.StateCommon.HeaderPrimary.Back.Color2 = Color.FromArgb(45, 62, 80);
                kryptonHeaderGroup1.StateCommon.HeaderPrimary.Content.ShortText.Color1 = Color.White;
                kryptonHeaderGroup1.StateCommon.HeaderPrimary.Content.ShortText.Font = new Font("Segoe UI", 12f, FontStyle.Bold);

                kryptonHeaderGroup1.StateCommon.HeaderSecondary.Back.Color1 = Color.FromArgb(52, 152, 219);
                kryptonHeaderGroup1.StateCommon.HeaderSecondary.Back.Color2 = Color.FromArgb(41, 128, 185);
                kryptonHeaderGroup1.StateCommon.HeaderSecondary.Content.ShortText.Color1 = Color.White;
                kryptonHeaderGroup1.StateCommon.HeaderSecondary.Content.ShortText.Font = new Font("Segoe UI", 9f, FontStyle.Bold);

                // Set panel colors
                kryptonPanel2.StateCommon.Color1 = Color.FromArgb(236, 240, 241);

                // Focus on search box
                TXTSearch.Focus();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du formulaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void KryptonDataGridView1_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            KryptonDataGridView dgv = sender as KryptonDataGridView;
            if (dgv == null || e.RowIndex < 0) return;

            // Add row numbers
            dgv.Rows[e.RowIndex].HeaderCell.Value = (e.RowIndex + 1).ToString();

            // Format specific columns based on content
            if (e.ColumnIndex >= 0)
            {
                // Format price column
                if (dgv.Columns[e.ColumnIndex].Name == nameof(Service.price) && e.Value != null)
                {
                    if (decimal.TryParse(e.Value.ToString(), out decimal price))
                    {
                        // Color based on price range
                        if (price > 2000)
                        {
                            e.CellStyle.ForeColor = Color.FromArgb(192, 57, 43); // Red for high prices
                            e.CellStyle.Font = new Font(dgv.Font, FontStyle.Bold);
                        }
                        else if (price > 1000)
                        {
                            e.CellStyle.ForeColor = Color.FromArgb(211, 84, 0); // Orange for medium prices
                        }
                        else
                        {
                            e.CellStyle.ForeColor = Color.FromArgb(39, 174, 96); // Green for low prices
                        }
                    }
                }

                // Format duration column
                if (dgv.Columns[e.ColumnIndex].Name == nameof(Service.duration) && e.Value != null)
                {
                    if (int.TryParse(e.Value.ToString(), out int duration))
                    {
                        // Color based on duration
                        if (duration > 120) // More than 2 hours
                        {
                            e.CellStyle.ForeColor = Color.FromArgb(192, 57, 43); // Red for long duration
                        }
                        else if (duration > 60) // More than 1 hour
                        {
                            e.CellStyle.ForeColor = Color.FromArgb(211, 84, 0); // Orange for medium duration
                        }
                        else if (duration > 0)
                        {
                            e.CellStyle.ForeColor = Color.FromArgb(39, 174, 96); // Green for short duration
                        }

                        // Format as hours and minutes
                        if (duration > 0)
                        {
                            int hours = duration / 60;
                            int minutes = duration % 60;

                            if (hours > 0)
                            {
                                e.Value = hours + "h" + (minutes > 0 ? minutes + "m" : "");
                            }
                            else
                            {
                                e.Value = minutes + "m";
                            }
                        }
                    }
                }
            }
        }

        private async void TXTSearch_TextChanged(object sender, EventArgs e)
        {
            _searchTerm = TXTSearch.Text;
            _CurrentPage = 1;
            await LOAD_ALL();
        }

        private void SetupContextMenu()
        {
            try
            {
                // Create a standard context menu strip
                ContextMenuStrip contextMenuStrip = new ContextMenuStrip();

                // Add items to the standard context menu
                ToolStripMenuItem refreshItem = new ToolStripMenuItem("Rafraîchir", null, async (s, e) => await LOAD_ALL());
                ToolStripMenuItem addItem = new ToolStripMenuItem("Ajouter", null, btnAdd_Click);
                ToolStripMenuItem editItem = new ToolStripMenuItem("Modifier", null, btnEdit_Click);
                ToolStripMenuItem deleteItem = new ToolStripMenuItem("Supprimer", null, btnDelete_Click);
                ToolStripSeparator separator = new ToolStripSeparator();
                ToolStripMenuItem printItem = new ToolStripMenuItem("Imprimer", null, PrintDataGridView);

                // Set icons if available
                if (btnAdd.Image != null) addItem.Image = btnAdd.Image;
                if (btnEdit.Image != null) editItem.Image = btnEdit.Image;
                if (btnDelete.Image != null) deleteItem.Image = btnDelete.Image;

                // Add items to the menu
                contextMenuStrip.Items.Add(refreshItem);
                contextMenuStrip.Items.Add(addItem);
                contextMenuStrip.Items.Add(editItem);
                contextMenuStrip.Items.Add(deleteItem);
                contextMenuStrip.Items.Add(separator);
                contextMenuStrip.Items.Add(printItem);

                // Attach the context menu to the DataGridView
                kryptonDataGridView1.ContextMenuStrip = contextMenuStrip;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la configuration du menu contextuel: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void PrintDataGridView(object sender, EventArgs e)
        {
            try
            {
                // Check if there's data to print
                if (kryptonDataGridView1.Rows.Count == 0)
                {
                    KryptonMessageBox.Show("Aucune donnée à imprimer", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                // Create a new report
                Report report = new Report();

                // Convert DataGridView data to DataTable
                DataTable dt = new DataTable("Services");

                // Add columns for the report
                dt.Columns.Add("Nom", typeof(string));
                dt.Columns.Add("Description", typeof(string));
                dt.Columns.Add("Prix", typeof(decimal));
                dt.Columns.Add("Durée", typeof(string));

                // Add rows
                foreach (DataGridViewRow row in kryptonDataGridView1.Rows)
                {
                    if (!row.IsNewRow)
                    {
                        DataRow dataRow = dt.NewRow();

                        // Get values from visible columns
                        string name = row.Cells[nameof(Service.name)].Value?.ToString() ?? "";
                        string description = row.Cells[nameof(Service.description)].Value?.ToString() ?? "";
                        decimal price = Convert.ToDecimal(row.Cells[nameof(Service.price)].Value ?? 0);
                        string duration = row.Cells[nameof(Service.duration)].FormattedValue?.ToString() ?? "";

                        // Add values to the row
                        dataRow["Nom"] = name;
                        dataRow["Description"] = description;
                        dataRow["Prix"] = price;
                        dataRow["Durée"] = duration;

                        dt.Rows.Add(dataRow);
                    }
                }

                // Create a simple report design
                string reportXml = @"<?xml version=""1.0"" encoding=""utf-8""?>
<Report ScriptLanguage=""CSharp"" ReportInfo.Created=""01/01/2023 00:00:00"" ReportInfo.Modified=""01/01/2023 00:00:00"" ReportInfo.CreatorVersion=""*******"">
  <Dictionary>
    <TableDataSource Name=""Services"" ReferenceName=""Services"">
      <Column Name=""Nom"" DataType=""System.String""/>
      <Column Name=""Description"" DataType=""System.String""/>
      <Column Name=""Prix"" DataType=""System.Decimal""/>
      <Column Name=""Durée"" DataType=""System.String""/>
    </TableDataSource>
    <Parameter Name=""Date"" DataType=""System.String""/>
    <Parameter Name=""TotalServices"" DataType=""System.String""/>
  </Dictionary>
  <ReportPage Name=""Page1"" Landscape=""false"" PaperWidth=""210"" PaperHeight=""297"" RawPaperSize=""9"" FirstPageSource=""15"" OtherPagesSource=""15"">
    <ReportTitleBand Name=""ReportTitle1"" Width=""718.2"" Height=""75.6"">
      <TextObject Name=""Text1"" Width=""718.2"" Height=""28.35"" Text=""Liste des Services"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 14pt, style=Bold""/>
      <TextObject Name=""Text2"" Left=""529.2"" Top=""28.35"" Width=""189"" Height=""18.9"" Text=""Date: [Date]"" HorzAlign=""Right"" Font=""Arial, 10pt""/>
      <TextObject Name=""Text3"" Top=""47.25"" Width=""718.2"" Height=""18.9"" Text=""Nombre total de services: [TotalServices]"" Font=""Arial, 10pt""/>
    </ReportTitleBand>
    <PageHeaderBand Name=""PageHeader1"" Top=""79.6"" Width=""718.2"" Height=""28.35"" Fill.Color=""WhiteSmoke"">
      <TextObject Name=""Text5"" Width=""179.55"" Height=""28.35"" Text=""Nom"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold"" Border.Lines=""All""/>
      <TextObject Name=""Text7"" Left=""179.55"" Width=""274.05"" Height=""28.35"" Text=""Description"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold"" Border.Lines=""All""/>
      <TextObject Name=""Text9"" Left=""453.6"" Width=""132.3"" Height=""28.35"" Text=""Prix"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold"" Border.Lines=""All""/>
      <TextObject Name=""Text11"" Left=""585.9"" Width=""132.3"" Height=""28.35"" Text=""Durée"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold"" Border.Lines=""All""/>
    </PageHeaderBand>
    <DataBand Name=""Data1"" Top=""111.95"" Width=""718.2"" Height=""18.9"" DataSource=""Services"">
      <TextObject Name=""Text4"" Width=""179.55"" Height=""18.9"" Text=""[Services.Nom]"" VertAlign=""Center"" Font=""Arial, 10pt"" Border.Lines=""All""/>
      <TextObject Name=""Text6"" Left=""179.55"" Width=""274.05"" Height=""18.9"" Text=""[Services.Description]"" VertAlign=""Center"" Font=""Arial, 10pt"" Border.Lines=""All""/>
      <TextObject Name=""Text8"" Left=""453.6"" Width=""132.3"" Height=""18.9"" Text=""[Services.Prix]"" Format=""Currency"" Format.UseLocale=""true"" HorzAlign=""Right"" VertAlign=""Center"" Font=""Arial, 10pt"" Border.Lines=""All""/>
      <TextObject Name=""Text10"" Left=""585.9"" Width=""132.3"" Height=""18.9"" Text=""[Services.Durée]"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 10pt"" Border.Lines=""All""/>
    </DataBand>
    <PageFooterBand Name=""PageFooter1"" Top=""134.85"" Width=""718.2"" Height=""18.9"">
      <TextObject Name=""Text12"" Width=""718.2"" Height=""18.9"" Text=""Page [Page] de [TotalPages]"" HorzAlign=""Right"" Font=""Arial, 8pt""/>
    </PageFooterBand>
  </ReportPage>
</Report>";

                // Register the data source
                report.RegisterData(dt, "Services");

                // Set report parameters
                report.SetParameterValue("Date", DateTime.Now.ToString("dd/MM/yyyy"));
                report.SetParameterValue("TotalServices", dt.Rows.Count.ToString());

                // Load the report design
                report.Load(new MemoryStream(System.Text.Encoding.UTF8.GetBytes(reportXml)));

                // Show preview dialog
                report.Show();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'impression: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void CmbPageView_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                // Get the selected page size
                if (CmbPageView.SelectedItem != null)
                {
                    _pageSize = Convert.ToInt32(CmbPageView.Text);
                }

                // Reset to first page when page size changes
                _CurrentPage = 1;

                // Reload services with new page size
                await LOAD_ALL();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du changement de taille de page: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void btnFirst_Click(object sender, EventArgs e)
        {
            try
            {
                // Go to first page
                _CurrentPage = 1;
                await LOAD_ALL();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la navigation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void btnPrev_Click(object sender, EventArgs e)
        {
            try
            {
                // Go to previous page if not on first page
                if (_CurrentPage > 1)
                {
                    _CurrentPage--;
                    await LOAD_ALL();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la navigation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void btnNext_Click(object sender, EventArgs e)
        {
            try
            {
                // Calculate total pages
                int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);

                // Go to next page if not on last page
                if (_CurrentPage < totalPages)
                {
                    _CurrentPage++;
                    await LOAD_ALL();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la navigation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void btnLast_Click(object sender, EventArgs e)
        {
            try
            {
                // Calculate total pages and go to last page
                int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
                _CurrentPage = totalPages;
                await LOAD_ALL();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la navigation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            // Open the add service form
            var frm = new FRM_SERVICES();
            frm.ShowDialog();

            // Reload services if the form was closed
            LOAD_ALL().ConfigureAwait(false);
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                // Check if a service is selected
                if (kryptonDataGridView1.CurrentRow == null || kryptonDataGridView1.CurrentRow.DataBoundItem == null)
                {
                    KryptonMessageBox.Show("Veuillez sélectionner un service à modifier", "Information",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    return;
                }

                // Get the selected service
                var selectedService = (Service)kryptonDataGridView1.CurrentRow.DataBoundItem;

                // Open the edit service form with the selected service ID
                var frm = new FRM_SERVICES(selectedService.id);
                frm.ShowDialog();

                // Reload services when the form is closed
                LOAD_ALL().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'ouverture du formulaire de modification: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            // Check if a service is selected
            if (kryptonDataGridView1.CurrentRow == null || kryptonDataGridView1.CurrentRow.DataBoundItem == null)
            {
                KryptonMessageBox.Show("Veuillez sélectionner un service à supprimer", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                return;
            }

            // Get the selected service
            var selectedService = (Service)kryptonDataGridView1.CurrentRow.DataBoundItem;

            // Confirm deletion
            var result = KryptonMessageBox.Show(
                $"Êtes-vous sûr de vouloir supprimer le service '{selectedService.name}'?",
                "Confirmation",
                KryptonMessageBoxButtons.YesNo,
                KryptonMessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    // Delete the service
                    bool success = await _Cmd.DeleteAsync(selectedService.id);

                    if (success)
                    {
                        KryptonMessageBox.Show("Le service a été supprimé avec succès", "Succès",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                    }
                    else
                    {
                        KryptonMessageBox.Show("Impossible de supprimer le service", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }

                    // Reload services
                    await LOAD_ALL();
                }
                catch (Exception ex)
                {
                    KryptonMessageBox.Show($"Erreur lors de la suppression: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
            }
        }


    }
}
