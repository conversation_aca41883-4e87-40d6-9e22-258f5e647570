﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="kryptonTableLayoutPanel1.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAxwAAABkCAYAAAAMnjY5AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJTSURBVHhe7dvRCYAwEAXBtHv9FxAJqBVk/2ZAJCUsj1sz
        s4/z/z5vb29vb29vb29vb+8b7z84AAAAbhIcAABAar1/AACAqywcAABARnAAAAAZwQEAAKTccAAAAAkL
        BwAAkBEcAABARnAAAAApNxwAAEDCwgEAAGQEBwAAkBEcAABAyg0HAACQsHAAAAAZwQEAAGQEBwAAkHLD
        AQAAJCwcAABARnAAAAAZwQEAAKTccAAAAAkLBwAAkBEcAABARnAAAAApNxwAAEDCwgEAAGQEBwAAkBEc
        AABAyg0HAACQsHAAAAAZwQEAAGQEBwAAkHLDAQAAJCwcAABARnAAAAAZwQEAAKTccAAAAAkLBwAAkBEc
        AABARnAAAAApNxwAAEDCwgEAAGQEBwAAkBEcAABAyg0HAACQsHAAAAAZwQEAAGQEBwAAkHLDAQAAJCwc
        AABARnAAAAAZwQEAAKTccAAAAAkLBwAAkBEcAABARnAAAAApNxwAAEDCwgEAAGQEBwAAkBEcAABAyg0H
        AACQsHAAAAAZwQEAAGQEBwAAkHLDAQAAJCwcAABARnAAAAAZwQEAAKTccAAAAAkLBwAAkBEcAABARnAA
        AAApNxwAAEDCwgEAAGQEBwAAkBEcAABAyg0HAACQsHAAAAAZwQEAAGQEBwAAkHLDAQAAJCwcAABARnAA
        AAAZwQEAAKTccAAAAAkLBwAAkBEcAABARnAAAAApNxwAAEDCwgEAAGQEBwAAkBEcAABAyg0HAACQsHAA
        AAAZwQEAAGQEBwAAkBIcAABAYmb2A05nDTuOB6fYAAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="kryptonContextMenu1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="kryptonContextMenuItem1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAW9JREFUOE+V
        k81LAlEUxWdt/1lmUTGTJURF0CKj9jVmabkQQ2ifWWpUrtpYoYES7vuYTTAZmAqjpS6buTd69zljBDrp
        gQtvuOf83pv3IYw6XcKqd/2GFXrXNr7tinmarJbnPPOCwzEicJkABAD8TwSRfTvA/CtdiHNsXKCG6bEV
        +TRNQ9nnJ8gSTT4UIBo97P4mA3yKkntwgG4Y8FBRza/OakRptjeg0mpg6O4MpUQQpdMA7uWS+KKVYSru
        pzbfLMpIMz0AtfYHelIh3LqOYbGkQPFNgUA2gZMs7IrJ5LMHHBTSuJk5QgMMMsKVUoSJYx8PDwRYvAhj
        QX2kIQdQ6YYOX2ZRg9QXsHAexvvXp27YqnJTo9WwYUd9AZF8Grdv43Sx/gAi+Uu+L5b6AqrtBt/EYDaJ
        7NjguVbiYZGdhlqvmC4bAOm9Vcf9XIqHpk92+YrURtXsdmQLGES/gCEekyXyUoZf5WGes1XkpYwouYUf
        1kP0EdQ6Zs8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="kryptonContextMenuItem2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAS5JREFUOE9j
        8PL2ZcjOyd8BxH9ycgv+EsIgdSD1oWERDGAANeAPCPz9+5cgBgGQergB3j5+DCCT0RXiwyD1YeGRI8YA
        ILj56tP7pKP3r0huvXaSedOZQ1JF9TXJoRFEGPDj18/NW6+dEE1c2d3kO7/2rs/8ml8+82ru+s6ubvKb
        XsEp72iE2wCQzSDNQQsbjgM1/0PHQIOOe82p4sRpAMjZUJvhmoDi/4Cu+IswpLoJpwHH7l+RAiq6C1MM
        wkBxNANq7uI0YNv1k8wgP8M0omOQQSB5nAYcvndZEqiZCBfgyAs3Ht1LCp3fQCAMappw5sb8guJbsd2V
        oqDQRjYESfNx77nVnAzq6hoMoJwFwqDUhYwD4qMZvOdUcYFCG6jhHsjPYBrI955XzaUaYMcAAFiItIhD
        6cCfAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="kryptonContextMenuItem3.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAASJJREFUOE9j
        8PL2ZcjOyd8BxH9ycgv+EsIgdSD1oWERDGAANeAPCPz9+5cgBgGQergB3j5+DCCT0RXiwyD1YeGRI9qA
        gqLyZYFZzQxqPoWkG/Dt+89VHQt3cdqWbzhhU74xhCQDvv34tXzOrusSLrVbqoCaU4D4PNEGADWvmL3z
        moRj9eYLthWb/gE111mXbWAjygCg5pVQzeehmm/ZlG2UlrUGJiZCBgA1r8KiWUbNGxiAjIz4Dfj9+8/a
        RXtvigM1n4Nqvg3EMuDQB2kGRyOOvPDr1+8XO07fE/Ko3+YE1PQaqlkWbjMM4MqNZdWNrSHNmxOBmi4A
        sQtYM7LNMKCursEAylkgDEpdMByU1cQAjOslQI25wNDmlbeNxdTMwMAAAPm/pL6x8l5AAAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="kryptonContextMenuItem4.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAASxJREFUOE9j
        8PL2ZcjOyd8BxH9ycgv+EsIgdSD1oWERDGAANeAPCPz9+5cgBgGQergB3j5+DCCT0RXiwyD1YeGRI8aA
        P79/3/z+8nnS26N7JV9sXcV8a+NKqZaiwuTI0HDCBvz+8X3ziy0rRS/EezSd9ja6e9rL4Ncpb8O7R72N
        m/b7mHCGyAnjNgBo9U2Q5jMB5sdPexv+Q8envAyOH/fU58RpAMjZEJsxNcPwKU/9JpwGAP0sBXY2Fo0w
        DHTFXZwGvNi6mhnkZ5BCIB8DA+X+guRxGvDm8C5JoGYiXIAjL7y4cTXpeLAN/jDwMmjCmRsLCopuLY7x
        FwWFNg7Nx4GByMmgrq7BAMpZIAxKXcg4LcCPARhVXKc8DZqAGu6B0wGQBtl8EiiepirOAAAsRaSdLtd5
        NgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="kryptonContextMenuItem5.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAKZJREFUOE9j
        QAZe3r4M2Tn5O4H4b05uwT98GKQGpBasCYS9ffwYEpNSQQb8/fPnz19CAKQGpBakYReIATK1qrqOAURD
        1RAEILVAAwpgNv5buWo16QYgafi3e8/eUQNGDQAbQHJSRjbg5KnTTEA+aZkJOSk/f/48u7mlnQkoATIY
        5BowTk3NYAgLj0TBoWERYAxSAM9M6Bgo/h6IUzy9fKAZHh0wMAAAoiELKr3Oa0wAAAAASUVORK5CYII=
</value>
  </data>
</root>