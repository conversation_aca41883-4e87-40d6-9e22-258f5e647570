﻿using IRepairIT.Data;
using IRepairIT.DB;
using IRepairIT.FRMS.FDB;
using IRepairIT.FRMS.FLOGIN;
using Krypton.Toolkit;
using MySql.Data.MySqlClient;
using System;
using System.IO;
using System.Threading;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FSPLASH
{
    public partial class FRM_SPLASH : KryptonForm
    {
        private DatabaseManager _dbManager;
        private Thread _loadingThread;
        private bool _isClosing = false;
        public FRM_SPLASH()
        {
            InitializeComponent();
            string settingsFilePath = Path.Combine(Application.StartupPath, "connection.json");
            if (!File.Exists(settingsFilePath))
            {
                var defaultSettings = new ConnectionSettings();
                defaultSettings.SaveToJson(settingsFilePath);
            }

            _dbManager = new DatabaseManager();
        }

        private void FRM_SPLASH_Load(object sender, EventArgs e)
        {
            progressBar.Minimum = 0;
            progressBar.Maximum = 100;
            progressBar.Value = 0;

            System.Windows.Forms.Timer fadeInTimer = new System.Windows.Forms.Timer();
            fadeInTimer.Interval = 30;
            fadeInTimer.Tick += (s, args) =>
            {
                if (this.Opacity < 1.0)
                    this.Opacity += 0.05;
                else
                    fadeInTimer.Stop();
            };
            fadeInTimer.Start();

            _loadingThread = new Thread(new ThreadStart(LoadingProcess));
            _loadingThread.IsBackground = true;
            _loadingThread.Start();
        }
        private void LoadingProcess()
        {
            try
            {
                if (_isClosing) return;
                UpdateProgress(0, "Vérification des paramètres de connexion...");
                Thread.Sleep(200);

                if (_isClosing) return;
                string settingsFilePath = Path.Combine(Application.StartupPath, "connection.json");
                bool settingsExist = File.Exists(settingsFilePath);

                if (!settingsExist && !_isClosing)
                {
                    UpdateProgress(10, "Paramètres de connexion non trouvés. Configuration...");

                    if (_isClosing) return;
                    if (!ShowConnectionSettings())
                    {
                        ExitApplication();
                        return;
                    }

                    if (_isClosing) return;
                    _dbManager = new DatabaseManager();
                }

                if (_isClosing) return;
                UpdateProgress(20, "Initialisation...");
                Thread.Sleep(200);

                if (_isClosing) return;
                UpdateProgress(30, "Vérification de la connexion à la base de données...");
                Thread.Sleep(200);

                if (_isClosing) return;
                if (!CheckDatabaseConnection())
                {
                    if (_isClosing) return;
                    UpdateProgress(35, "Connexion à la base de données échouée. Configuration des paramètres...");

                    if (_isClosing) return;
                    if (!ShowConnectionSettings())
                    {
                        ExitApplication();
                        return;
                    }

                    if (_isClosing) return;
                    _dbManager = new DatabaseManager();

                    if (_isClosing) return;
                    if (!CheckDatabaseConnection())
                    {
                        if (_isClosing) return;
                        UpdateProgress(38, "Connexion à la base de données échouée. L'application va se fermer.");

                        try
                        {
                            if (!_isClosing && this.IsHandleCreated && !this.IsDisposed)
                            {
                                this.Invoke(new Action(() =>
                                {
                                    MessageBox.Show("Impossible de se connecter à la base de données. Veuillez vérifier vos paramètres de connexion et redémarrer l'application.",
                                        "Erreur de Connexion", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }));
                            }
                        }
                        catch (Exception)
                        {
                        }

                        ExitApplication();
                        return;
                    }
                }

                if (_isClosing) return;
                UpdateProgress(40, "Connexion à la base de données réussie...");
                Thread.Sleep(150);

                if (_isClosing) return;
                UpdateProgress(50, "Mise à jour des procédures stockées...");

                string currentDb = Properties.Settings.Default.CurrentDatabase;

                Thread.Sleep(150);

                if (_isClosing) return;
                UpdateProgress(55, "Chargement des données...");
                Thread.Sleep(150);

                if (_isClosing) return;
                UpdateProgress(60, "Préparation de l'interface...");
                Thread.Sleep(150);

                if (_isClosing) return;
                UpdateProgress(70, "Finalisation...");
                Thread.Sleep(150);

                if (_isClosing) return;
                UpdateProgress(80, "Prêt à démarrer...");
                Thread.Sleep(150);

                for (int i = 80; i <= 100; i += 5)
                {
                    if (_isClosing) return;
                    UpdateProgress(i, "Démarrage de l'application...");
                    Thread.Sleep(40);
                }

                if (!_isClosing)
                {
                    try
                    {
                        if (this.IsHandleCreated && !this.IsDisposed)
                        {
                            this.Invoke(new Action(() => ProceedToNextForm()));
                        }
                    }
                    catch (ObjectDisposedException)
                    {
                    }
                    catch (InvalidOperationException)
                    {
                    }
                }
            }
            catch (ThreadAbortException)
            {
            }
            catch (Exception ex)
            {
                try
                {
                    if (!_isClosing && this.IsHandleCreated && !this.IsDisposed)
                    {
                        this.Invoke(new Action(() =>
                        {
                            MessageBox.Show($"Erreur lors du chargement: {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            Application.Exit();
                        }));
                    }
                    else
                    {
                        Application.Exit();
                    }
                }
                catch (Exception)
                {
                    Application.Exit();
                }
            }
        }

        private void UpdateProgress(int value, string status)
        {
            try
            {
                if (this.IsDisposed || !this.IsHandleCreated)
                {
                    return;
                }

                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() => UpdateProgress(value, status)));
                    return;
                }

                progressBar.Value = value;
                lblStatus.Text = status;
                Application.DoEvents();
            }
            catch (ObjectDisposedException)
            {
            }
            catch (InvalidOperationException)
            {
            }
        }
        private bool CheckDatabaseConnection()
        {
            try
            {
                string settingsFilePath = Path.Combine(Application.StartupPath, "connection.json");
                var settings = ConnectionSettings.LoadFromJson(settingsFilePath);
                string unprotectedConnectionString = settings.GetMasterConnectionString();
                using (var connection = new MySqlConnection(unprotectedConnectionString))
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }
        private bool ShowConnectionSettings()
        {
            bool result = false;

            try
            {
                if (this.IsDisposed || !this.IsHandleCreated)
                {
                    return false;
                }

                this.Invoke(new Action(() =>
                {
                    var connectionSettingsForm = new FRM_DATABASELIST();
                    result = connectionSettingsForm.ShowDialog() == DialogResult.OK;
                }));
            }
            catch (ObjectDisposedException)
            {
                return false;
            }
            catch (InvalidOperationException)
            {
                return false;
            }

            return result;
        }
        private void ProceedToNextForm()
        {
            if (_isClosing || this.IsDisposed)
            {
                return;
            }

            try
            {
                var databases = _dbManager.GetIRepairDatabases();

                if (databases.Count == 0)
                {
                    var setupWizard = new FRM_SETUPDB();
                    this.Hide();
                    setupWizard.ShowDialog();
                    this.Close();
                }
                else
                {
                    if (!Properties.Settings.Default.ShowDatabaseSelector && !string.IsNullOrEmpty(Properties.Settings.Default.DefaultDatabase))
                    {
                        string defaultDb = Properties.Settings.Default.DefaultDatabase;

                        if (_dbManager.CheckDatabaseExists(defaultDb))
                        {
                            var settings = ConnectionSettings.LoadFromJson(Path.Combine(Application.StartupPath, "connection.json"));
                            string connectionString = settings.GetConnectionString(defaultDb);

                            Properties.Settings.Default.CurrentConnectionString = connectionString;
                            Properties.Settings.Default.CurrentDatabase = defaultDb;
                            Properties.Settings.Default.Save();

                            var loginForm = new FRM_LOGIN();
                            this.Hide();
                            loginForm.ShowDialog();
                            this.Close();
                        }
                        else
                        {
                            Properties.Settings.Default.ShowDatabaseSelector = true;
                            Properties.Settings.Default.DefaultDatabase = "";
                            Properties.Settings.Default.Save();

                            MessageBox.Show($"La base de données '{defaultDb}' n'existe plus ou a été supprimée.\n\nVeuillez sélectionner une autre base de données.",
                                "Base de données introuvable", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                            var databaseselectorform = new FRM_DATABASELIST();
                            this.Hide();
                            if (databaseselectorform.ShowDialog() == DialogResult.OK)
                            {
                                var loginform = new FRM_LOGIN();
                                loginform.ShowDialog();
                            }
                            this.Close();
                        }
                    }
                    else
                    {
                        var databaseselectorform = new FRM_DATABASELIST();
                        this.Hide();
                        if (databaseselectorform.ShowDialog() == DialogResult.OK)
                        {
                            var loginform = new FRM_LOGIN();
                            loginform.ShowDialog();
                        }
                        this.Close();
                    }
                }
            }
            catch (ObjectDisposedException)
            {
                Application.Exit();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'accès aux données: {ex.Message}\n\nVeuillez vérifier vos paramètres de connexion et redémarrer l'application.",
                    "Erreur de Connexion", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

        private void FRM_SPLASH_FormClosing(object sender, FormClosingEventArgs e)
        {
            _isClosing = true;

            try
            {
                if (_loadingThread != null && _loadingThread.IsAlive)
                {
                    _loadingThread.Abort();
                }
            }
            catch (Exception)
            {
            }
        }
        private void ExitApplication()
        {
            try
            {
                if (this.IsDisposed || !this.IsHandleCreated)
                {
                    _isClosing = true;
                    Application.Exit();
                    return;
                }

                this.Invoke(new Action(() =>
                {
                    _isClosing = true;
                    Application.Exit();
                }));
            }
            catch (ObjectDisposedException)
            {
                _isClosing = true;
                Application.Exit();
            }
            catch (InvalidOperationException)
            {
                _isClosing = true;
                Application.Exit();
            }
        }
    }
}
