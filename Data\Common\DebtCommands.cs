﻿﻿using Dapper;
using IRepairIT.Enums;
using IRepairIT.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IRepairIT.Data.Common
{
    public class DebtCommands
    {
        private readonly DataAccess _db;

        public DebtCommands()
        {
            _db = new DataAccess();
        }

        public async Task<int> GetCount(string searchTerm)
        {
            const string sql = @"debts d
                JOIN customers c ON d.customer_id = c.id
                LEFT JOIN repair_orders ro ON d.repair_order_id = ro.id
                WHERE (c.name LIKE CONCAT('%', @searchTerm, '%')
                OR c.phone LIKE CONCAT('%', @searchTerm, '%')
                OR ro.order_number LIKE CONCAT('%', @searchTerm, '%'))";

            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            return await _db.CountQuery(sql, parameters);
        }

        public async Task<IEnumerable<Debt>> GetAll(string searchTerm, int offset, int pageSize)
        {
            const string sql = @"SELECT d.*, c.name as CustomerName, ro.order_number as OrderNumber
                FROM debts d
                JOIN customers c ON d.customer_id = c.id
                LEFT JOIN repair_orders ro ON d.repair_order_id = ro.id
                WHERE (c.name LIKE CONCAT('%', @searchTerm, '%')
                OR c.phone LIKE CONCAT('%', @searchTerm, '%')
                OR ro.order_number LIKE CONCAT('%', @searchTerm, '%'))
                ORDER BY d.created_at DESC
                LIMIT @pageSize OFFSET @offset";

            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            parameters.Add("offset", offset);
            parameters.Add("pageSize", pageSize);

            return await _db.QueryQuery<Debt>(sql, parameters);
        }

        public async Task<Debt> GetByIdAsync(int id)
        {
            const string sql = @"SELECT d.*, c.name as CustomerName, ro.order_number as OrderNumber
                FROM debts d
                JOIN customers c ON d.customer_id = c.id
                LEFT JOIN repair_orders ro ON d.repair_order_id = ro.id
                WHERE d.id = @id";

            var parameters = new DynamicParameters();
            parameters.Add("id", id);

            return await _db.QuerySingleOrDefaultQuery<Debt>(sql, parameters);
        }

        public async Task<IEnumerable<Debt>> GetByCustomerIdAsync(int customerId)
        {
            const string sql = @"SELECT d.*, c.name as CustomerName, ro.order_number as OrderNumber
                FROM debts d
                JOIN customers c ON d.customer_id = c.id
                LEFT JOIN repair_orders ro ON d.repair_order_id = ro.id
                WHERE d.customer_id = @customerId
                ORDER BY d.created_at DESC";

            var parameters = new DynamicParameters();
            parameters.Add("customerId", customerId);

            return await _db.QueryQuery<Debt>(sql, parameters);
        }

        public async Task<IEnumerable<Debt>> GetByRepairOrderIdAsync(int repairOrderId)
        {
            const string sql = @"SELECT d.*, c.name as CustomerName, ro.order_number as OrderNumber
                FROM debts d
                JOIN customers c ON d.customer_id = c.id
                LEFT JOIN repair_orders ro ON d.repair_order_id = ro.id
                WHERE d.repair_order_id = @repairOrderId
                ORDER BY d.created_at DESC";

            var parameters = new DynamicParameters();
            parameters.Add("repairOrderId", repairOrderId);

            return await _db.QueryQuery<Debt>(sql, parameters);
        }

        public async Task<int> InsertAsync(Debt debt)
        {
            const string sql = @"INSERT INTO debts (
                customer_id, repair_order_id, amount, paid_amount,
                debt_date, due_date, status, notes, created_at)
                VALUES (
                @CustomerId, @RepairOrderId, @Amount, @PaidAmount,
                @DebtDate, @DueDate, @Status, @Notes, @CreatedAt)";

            var parameters = new DynamicParameters();
            parameters.Add("CustomerId", debt.CustomerId);
            parameters.Add("RepairOrderId", debt.RepairOrderId);
            parameters.Add("Amount", debt.Amount);
            parameters.Add("PaidAmount", debt.PaidAmount);
            parameters.Add("DebtDate", debt.DebtDate);
            parameters.Add("DueDate", debt.DueDate);
            parameters.Add("Status", debt.Status.ToString());
            parameters.Add("Notes", debt.Notes);
            parameters.Add("CreatedAt", DateTime.Now);

            return await _db.InsertAndGetIdAsync(sql, parameters);
        }

        public async Task<bool> UpdateAsync(Debt debt)
        {
            const string sql = @"UPDATE debts SET
                customer_id = @CustomerId,
                repair_order_id = @RepairOrderId,
                amount = @Amount,
                paid_amount = @PaidAmount,
                debt_date = @DebtDate,
                due_date = @DueDate,
                status = @Status,
                notes = @Notes,
                updated_at = @UpdatedAt
                WHERE id = @Id";

            var parameters = new DynamicParameters();
            parameters.Add("Id", debt.Id);
            parameters.Add("CustomerId", debt.CustomerId);
            parameters.Add("RepairOrderId", debt.RepairOrderId);
            parameters.Add("Amount", debt.Amount);
            parameters.Add("PaidAmount", debt.PaidAmount);
            parameters.Add("DebtDate", debt.DebtDate);
            parameters.Add("DueDate", debt.DueDate);
            parameters.Add("Status", debt.Status.ToString());
            parameters.Add("Notes", debt.Notes);
            parameters.Add("UpdatedAt", DateTime.Now);

            var result = await _db.ExecuteQuery(sql, parameters);
            return result > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            const string sql = "DELETE FROM debts WHERE id = @id";
            var parameters = new DynamicParameters();
            parameters.Add("id", id);

            var result = await _db.ExecuteQuery(sql, parameters);
            return result > 0;
        }

        public async Task<bool> UpdatePaymentAsync(int id, decimal paidAmount)
        {
            // Get the current debt
            var debt = await GetByIdAsync(id);
            if (debt == null)
                return false;

            // Update paid amount
            debt.PaidAmount += paidAmount;

            // Update status
            if (debt.PaidAmount >= debt.Amount)
            {
                debt.Status = PaymentStatus.paid;
            }
            else if (debt.PaidAmount > 0)
            {
                debt.Status = PaymentStatus.partially_paid;
            }

            // Save changes
            return await UpdateAsync(debt);
        }

        public async Task<int> CreateDebtFromRepairOrderAsync(int repairOrderId, DateTime? dueDate = null, string notes = null)
        {
            // Get the repair order
            const string sqlRepairOrder = @"SELECT * FROM repair_orders WHERE id = @id";
            var parameters = new DynamicParameters();
            parameters.Add("id", repairOrderId);
            var repairOrder = await _db.QuerySingleOrDefaultQuery<RepairOrder>(sqlRepairOrder, parameters);

            if (repairOrder == null)
                return 0;

            // Check if there's an outstanding amount
            decimal outstandingAmount = repairOrder.TotalCost - repairOrder.PaidAmount;
            if (outstandingAmount <= 0)
                return 0;

            // Create a new debt
            var debt = new Debt
            {
                CustomerId = repairOrder.CustomerId,
                RepairOrderId = repairOrderId,
                Amount = outstandingAmount,
                PaidAmount = 0,
                DebtDate = DateTime.Now,
                DueDate = dueDate,
                Status = PaymentStatus.unpaid,
                Notes = notes ?? $"Dette pour l'ordre de réparation #{repairOrder.OrderNumber}",
                CreatedAt = DateTime.Now
            };

            // Insert the debt
            return await InsertAsync(debt);
        }

        public async Task<IEnumerable<dynamic>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT
                    d.id,
                    d.customer_id,
                    d.repair_order_id,
                    d.amount,
                    d.paid_amount,
                    d.debt_date,
                    d.due_date,
                    d.status,
                    d.notes,
                    d.created_at,
                    d.updated_at,
                    c.name as customer_name,
                    ro.order_number as order_number,
                    (d.amount - d.paid_amount) as remaining_amount
                FROM debts d
                JOIN customers c ON d.customer_id = c.id
                LEFT JOIN repair_orders ro ON d.repair_order_id = ro.id
                WHERE d.debt_date BETWEEN @startDate AND @endDate
                ORDER BY d.debt_date DESC";

            var parameters = new DynamicParameters();
            parameters.Add("startDate", startDate);
            parameters.Add("endDate", endDate);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        public async Task<IEnumerable<dynamic>> GetPaymentsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT
                    d.id as debt_id,
                    d.customer_id,
                    d.repair_order_id,
                    d.paid_amount,
                    d.debt_date,
                    d.status,
                    c.name as customer_name,
                    ro.order_number as order_number,
                    d.updated_at as payment_date
                FROM debts d
                JOIN customers c ON d.customer_id = c.id
                LEFT JOIN repair_orders ro ON d.repair_order_id = ro.id
                WHERE d.paid_amount > 0
                AND d.updated_at BETWEEN @startDate AND @endDate
                ORDER BY d.updated_at DESC";

            var parameters = new DynamicParameters();
            parameters.Add("startDate", startDate);
            parameters.Add("endDate", endDate);

            return await _db.QueryQuery<dynamic>(sql, parameters);
        }

        public async Task<IEnumerable<dynamic>> GetOverdueDebtsAsync()
        {
            const string sql = @"
                SELECT
                    d.id,
                    d.customer_id,
                    d.repair_order_id,
                    d.amount,
                    d.paid_amount,
                    d.debt_date,
                    d.due_date,
                    d.status,
                    d.notes,
                    d.created_at,
                    d.updated_at,
                    c.name as customer_name,
                    c.phone as customer_phone,
                    ro.order_number as order_number,
                    (d.amount - d.paid_amount) as remaining_amount,
                    DATEDIFF(CURRENT_DATE, d.due_date) as days_overdue
                FROM debts d
                JOIN customers c ON d.customer_id = c.id
                LEFT JOIN repair_orders ro ON d.repair_order_id = ro.id
                WHERE d.due_date < CURRENT_DATE
                AND d.status != 'paid'
                ORDER BY d.due_date ASC";

            return await _db.QueryQuery<dynamic>(sql);
        }
    }
}
