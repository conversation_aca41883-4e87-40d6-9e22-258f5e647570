﻿namespace IRepairIT.FRMS.FDB
{
    partial class FRM_DATABASELIST
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FRM_DATABASELIST));
            this.kryptonPanel1 = new Krypton.Toolkit.KryptonPanel();
            this.lvDatabases = new System.Windows.Forms.ListView();
            this.imageList = new System.Windows.Forms.ImageList(this.components);
            this.kryptonHeader1 = new Krypton.Toolkit.KryptonHeader();
            this.btnClose = new Krypton.Toolkit.ButtonSpecAny();
            this.btnDuplicate = new Krypton.Toolkit.KryptonButton();
            this.btnNew = new Krypton.Toolkit.KryptonButton();
            this.btnDelete = new Krypton.Toolkit.KryptonButton();
            this.chkDontShowAgain = new Krypton.Toolkit.KryptonCheckBox();
            this.btnSelect = new Krypton.Toolkit.KryptonButton();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).BeginInit();
            this.kryptonPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // kryptonPanel1
            // 
            this.kryptonPanel1.Controls.Add(this.lvDatabases);
            this.kryptonPanel1.Controls.Add(this.kryptonHeader1);
            this.kryptonPanel1.Controls.Add(this.btnDuplicate);
            this.kryptonPanel1.Controls.Add(this.btnNew);
            this.kryptonPanel1.Controls.Add(this.btnDelete);
            this.kryptonPanel1.Controls.Add(this.chkDontShowAgain);
            this.kryptonPanel1.Controls.Add(this.btnSelect);
            this.kryptonPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel1.Location = new System.Drawing.Point(0, 0);
            this.kryptonPanel1.Name = "kryptonPanel1";
            this.kryptonPanel1.Size = new System.Drawing.Size(410, 450);
            this.kryptonPanel1.TabIndex = 0;
            // 
            // lvDatabases
            // 
            this.lvDatabases.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lvDatabases.HideSelection = false;
            this.lvDatabases.LargeImageList = this.imageList;
            this.lvDatabases.Location = new System.Drawing.Point(138, 40);
            this.lvDatabases.Name = "lvDatabases";
            this.lvDatabases.Size = new System.Drawing.Size(260, 398);
            this.lvDatabases.TabIndex = 19;
            this.lvDatabases.UseCompatibleStateImageBehavior = false;
            this.lvDatabases.View = System.Windows.Forms.View.Tile;
            this.lvDatabases.SelectedIndexChanged += new System.EventHandler(this.lvDatabases_TabIndexChanged);
            this.lvDatabases.DoubleClick += new System.EventHandler(this.lvDatabases_DoubleClick);
            // 
            // imageList
            // 
            this.imageList.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList.ImageStream")));
            this.imageList.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList.Images.SetKeyName(0, "folder.png");
            // 
            // kryptonHeader1
            // 
            this.kryptonHeader1.ButtonSpecs.Add(this.btnClose);
            this.kryptonHeader1.Dock = System.Windows.Forms.DockStyle.Top;
            this.kryptonHeader1.Location = new System.Drawing.Point(0, 0);
            this.kryptonHeader1.Name = "kryptonHeader1";
            this.kryptonHeader1.Size = new System.Drawing.Size(410, 34);
            this.kryptonHeader1.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 14.25F, System.Drawing.FontStyle.Bold);
            this.kryptonHeader1.TabIndex = 18;
            this.kryptonHeader1.Values.Description = "";
            this.kryptonHeader1.Values.Heading = "Sélection du Dossier";
            this.kryptonHeader1.Values.Image = global::IRepairIT.Properties.Resources.folder;
            // 
            // btnClose
            // 
            this.btnClose.Image = global::IRepairIT.Properties.Resources.close_window;
            this.btnClose.Style = Krypton.Toolkit.PaletteButtonStyle.Alternate;
            this.btnClose.UniqueName = "58ebb0e87a444370b7b010bf7556bd39";
            this.btnClose.Click += new System.EventHandler(this.btnClose_Click_1);
            // 
            // btnDuplicate
            // 
            this.btnDuplicate.Enabled = false;
            this.btnDuplicate.Location = new System.Drawing.Point(12, 403);
            this.btnDuplicate.Name = "btnDuplicate";
            this.btnDuplicate.Size = new System.Drawing.Size(120, 35);
            this.btnDuplicate.TabIndex = 17;
            this.btnDuplicate.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnDuplicate.Values.Text = "Dupliquer";
            this.btnDuplicate.Click += new System.EventHandler(this.btnDuplicate_Click);
            // 
            // btnNew
            // 
            this.btnNew.Location = new System.Drawing.Point(12, 321);
            this.btnNew.Name = "btnNew";
            this.btnNew.Size = new System.Drawing.Size(120, 35);
            this.btnNew.TabIndex = 16;
            this.btnNew.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnNew.Values.Text = "Nouveau";
            this.btnNew.Click += new System.EventHandler(this.btnNew_Click);
            // 
            // btnDelete
            // 
            this.btnDelete.Enabled = false;
            this.btnDelete.Location = new System.Drawing.Point(12, 362);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new System.Drawing.Size(120, 35);
            this.btnDelete.TabIndex = 16;
            this.btnDelete.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnDelete.Values.Text = "Supprimer";
            this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click);
            // 
            // chkDontShowAgain
            // 
            this.chkDontShowAgain.Location = new System.Drawing.Point(12, 99);
            this.chkDontShowAgain.Name = "chkDontShowAgain";
            this.chkDontShowAgain.Size = new System.Drawing.Size(109, 20);
            this.chkDontShowAgain.TabIndex = 15;
            this.chkDontShowAgain.Values.Text = "Ne plus afficher";
            // 
            // btnSelect
            // 
            this.btnSelect.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnSelect.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSelect.Enabled = false;
            this.btnSelect.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnSelect.Location = new System.Drawing.Point(12, 58);
            this.btnSelect.Name = "btnSelect";
            this.btnSelect.Size = new System.Drawing.Size(120, 35);
            this.btnSelect.StateCommon.Border.DrawBorders = Krypton.Toolkit.PaletteDrawBorders.None;
            this.btnSelect.StateCommon.Border.Rounding = 5F;
            this.btnSelect.StateCommon.Border.Width = 0;
            this.btnSelect.StateCommon.Content.Image.ImageH = Krypton.Toolkit.PaletteRelativeAlign.Far;
            this.btnSelect.StateCommon.Content.Image.ImageV = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.btnSelect.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnSelect.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75F);
            this.btnSelect.StateNormal.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(13)))), ((int)(((byte)(110)))), ((int)(((byte)(253)))));
            this.btnSelect.StateNormal.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(13)))), ((int)(((byte)(110)))), ((int)(((byte)(253)))));
            this.btnSelect.StateNormal.Border.Rounding = 1F;
            this.btnSelect.StateNormal.Border.Width = 1;
            this.btnSelect.StateTracking.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(11)))), ((int)(((byte)(94)))), ((int)(((byte)(215)))));
            this.btnSelect.StateTracking.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(11)))), ((int)(((byte)(94)))), ((int)(((byte)(215)))));
            this.btnSelect.TabIndex = 13;
            this.btnSelect.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnSelect.Values.Text = "Sélectionner";
            this.btnSelect.Click += new System.EventHandler(this.btnSelect_Click);
            // 
            // FRM_DATABASELIST
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(410, 450);
            this.Controls.Add(this.kryptonPanel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FRM_DATABASELIST";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Sélection du Dossier";
            this.Load += new System.EventHandler(this.FRM_DATABASELIST_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).EndInit();
            this.kryptonPanel1.ResumeLayout(false);
            this.kryptonPanel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonPanel kryptonPanel1;
        private Krypton.Toolkit.KryptonButton btnSelect;
        private Krypton.Toolkit.KryptonCheckBox chkDontShowAgain;
        private Krypton.Toolkit.KryptonButton btnNew;
        private Krypton.Toolkit.KryptonButton btnDelete;
        private Krypton.Toolkit.KryptonButton btnDuplicate;
        private Krypton.Toolkit.KryptonHeader kryptonHeader1;
        private Krypton.Toolkit.ButtonSpecAny btnClose;
        private System.Windows.Forms.ImageList imageList;
        private System.Windows.Forms.ListView lvDatabases;
    }
}