﻿﻿using IRepairIT.Data.Common;
using Krypton.Toolkit;
using System;
using System.IO;
using System.Windows.Forms;
using FastReport;

namespace IRepairIT.FRMS.FREPORTS
{
    public partial class FRM_SALES_REPORT : KryptonForm
    {
        private readonly RepairOrderCommands _repairOrderCmd;
        private readonly ServiceCommands _serviceCmd;
        private readonly PartCommands _partCmd;
        private readonly ShopInfoCommands _shopInfoCmd;

        public FRM_SALES_REPORT()
        {
            InitializeComponent();
            _repairOrderCmd = new RepairOrderCommands();
            _serviceCmd = new ServiceCommands();
            _partCmd = new PartCommands();
            _shopInfoCmd = new ShopInfoCommands();
        }

        private void FRM_SALES_REPORT_Load(object sender, EventArgs e)
        {
            // Initialize date pickers
            dtpStartDate.Value = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            dtpEndDate.Value = DateTime.Now;

            // Initialize report type combo box
            InitializeReportTypeComboBox();
        }

        private void InitializeReportTypeComboBox()
        {
            cmbReportType.Items.Clear();
            cmbReportType.Items.Add("إجمالي الطلبات");
            cmbReportType.Items.Add("إجمالي الخدمات");
            cmbReportType.Items.Add("إجمالي قطع الغيار");
            cmbReportType.Items.Add("أكثر الخدمات مبيعًا");
            cmbReportType.Items.Add("أكثر قطع الغيار مبيعًا");
            cmbReportType.Items.Add("بيانات المبيعات");
            cmbReportType.SelectedIndex = 0;
        }

        private async void BtnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                Cursor = Cursors.WaitCursor;

                // Get date range
                DateTime startDate = dtpStartDate.Value.Date;
                DateTime endDate = dtpEndDate.Value.Date.AddDays(1).AddSeconds(-1);

                // Get report type
                string reportType = cmbReportType.SelectedItem.ToString();

                // Generate report based on type
                switch (reportType)
                {
                    case "إجمالي الطلبات":
                        await GenerateTotalOrdersReport(startDate, endDate);
                        break;
                    case "إجمالي الخدمات":
                        await GenerateTotalServicesReport(startDate, endDate);
                        break;
                    case "إجمالي قطع الغيار":
                        await GenerateTotalPartsReport(startDate, endDate);
                        break;
                    case "أكثر الخدمات مبيعًا":
                        await GenerateTopServicesReport(startDate, endDate);
                        break;
                    case "أكثر قطع الغيار مبيعًا":
                        await GenerateTopPartsReport(startDate, endDate);
                        break;
                    case "بيانات المبيعات":
                        await GenerateSalesDataReport(startDate, endDate);
                        break;
                    default:
                        KryptonMessageBox.Show("الرجاء اختيار نوع التقرير", "خطأ",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        break;
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"خطأ أثناء إنشاء التقرير: {ex.Message}", "خطأ",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private async System.Threading.Tasks.Task GenerateTotalOrdersReport(DateTime startDate, DateTime endDate)
        {
            // Get total orders data
            var totalOrders = await _repairOrderCmd.GetTotalOrdersAsync(startDate, endDate);

            // Create report
            using (Report report = new Report())
            {
                // Get report file path
                string reportFile = Path.Combine(Application.StartupPath, "Reports", "TotalOrdersReport.frx");

                // Check if report file exists
                if (!File.Exists(reportFile))
                {
                    // Create directory if it doesn't exist
                    Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                    // Copy the report file from the project to the output directory
                    File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "TotalOrdersReport.frx"), reportFile);

                    if (!File.Exists(reportFile))
                    {
                        KryptonMessageBox.Show("ملف التقرير غير موجود: " + reportFile, "خطأ",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return;
                    }
                }

                // Load report
                report.Load(reportFile);

                // Register data
                report.RegisterData(totalOrders, "TotalOrders");

                // Get shop info
                var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                if (shopInfo != null)
                {
                    report.SetParameterValue("ShopName", shopInfo.name);
                    report.SetParameterValue("ShopAddress", shopInfo.address);
                    report.SetParameterValue("ShopPhone", shopInfo.phone);
                    report.SetParameterValue("ShopEmail", shopInfo.email);
                }

                // Set date range parameters
                report.SetParameterValue("StartDate", startDate.ToString("dd/MM/yyyy"));
                report.SetParameterValue("EndDate", endDate.ToString("dd/MM/yyyy"));

                // Prepare report
                report.Prepare();

                // Show report
                report.Show();
            }
        }

        private async System.Threading.Tasks.Task GenerateTotalServicesReport(DateTime startDate, DateTime endDate)
        {
            // Get total services data
            var totalServices = await _serviceCmd.GetTotalServicesAsync(startDate, endDate);

            // Create report
            using (Report report = new Report())
            {
                // Get report file path
                string reportFile = Path.Combine(Application.StartupPath, "Reports", "TotalServicesReport.frx");

                // Check if report file exists
                if (!File.Exists(reportFile))
                {
                    // Create directory if it doesn't exist
                    Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                    // Copy the report file from the project to the output directory
                    File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "TotalServicesReport.frx"), reportFile);

                    if (!File.Exists(reportFile))
                    {
                        KryptonMessageBox.Show("ملف التقرير غير موجود: " + reportFile, "خطأ",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return;
                    }
                }

                // Load report
                report.Load(reportFile);

                // Register data
                report.RegisterData(totalServices, "TotalServices");

                // Get shop info
                var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                if (shopInfo != null)
                {
                    report.SetParameterValue("ShopName", shopInfo.name);
                    report.SetParameterValue("ShopAddress", shopInfo.address);
                    report.SetParameterValue("ShopPhone", shopInfo.phone);
                    report.SetParameterValue("ShopEmail", shopInfo.email);
                }

                // Set date range parameters
                report.SetParameterValue("StartDate", startDate.ToString("dd/MM/yyyy"));
                report.SetParameterValue("EndDate", endDate.ToString("dd/MM/yyyy"));

                // Prepare report
                report.Prepare();

                // Show report
                report.Show();
            }
        }

        private async System.Threading.Tasks.Task GenerateTotalPartsReport(DateTime startDate, DateTime endDate)
        {
            // Get total parts data
            var totalParts = await _partCmd.GetTotalPartsAsync(startDate, endDate);

            // Create report
            using (Report report = new Report())
            {
                // Get report file path
                string reportFile = Path.Combine(Application.StartupPath, "Reports", "TotalPartsReport.frx");

                // Check if report file exists
                if (!File.Exists(reportFile))
                {
                    // Create directory if it doesn't exist
                    Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                    // Copy the report file from the project to the output directory
                    File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "TotalPartsReport.frx"), reportFile);

                    if (!File.Exists(reportFile))
                    {
                        KryptonMessageBox.Show("ملف التقرير غير موجود: " + reportFile, "خطأ",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return;
                    }
                }

                // Load report
                report.Load(reportFile);

                // Register data
                report.RegisterData(totalParts, "TotalParts");

                // Get shop info
                var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                if (shopInfo != null)
                {
                    report.SetParameterValue("ShopName", shopInfo.name);
                    report.SetParameterValue("ShopAddress", shopInfo.address);
                    report.SetParameterValue("ShopPhone", shopInfo.phone);
                    report.SetParameterValue("ShopEmail", shopInfo.email);
                }

                // Set date range parameters
                report.SetParameterValue("StartDate", startDate.ToString("dd/MM/yyyy"));
                report.SetParameterValue("EndDate", endDate.ToString("dd/MM/yyyy"));

                // Prepare report
                report.Prepare();

                // Show report
                report.Show();
            }
        }

        private async System.Threading.Tasks.Task GenerateTopServicesReport(DateTime startDate, DateTime endDate)
        {
            // Get top services data
            var topServices = await _serviceCmd.GetTopServicesAsync(startDate, endDate);

            // Create report
            using (Report report = new Report())
            {
                // Get report file path
                string reportFile = Path.Combine(Application.StartupPath, "Reports", "TopServicesReport.frx");

                // Check if report file exists
                if (!File.Exists(reportFile))
                {
                    // Create directory if it doesn't exist
                    Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                    // Copy the report file from the project to the output directory
                    File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "TopServicesReport.frx"), reportFile);

                    if (!File.Exists(reportFile))
                    {
                        KryptonMessageBox.Show("ملف التقرير غير موجود: " + reportFile, "خطأ",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return;
                    }
                }

                // Load report
                report.Load(reportFile);

                // Register data
                report.RegisterData(topServices, "TopServices");

                // Get shop info
                var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                if (shopInfo != null)
                {
                    report.SetParameterValue("ShopName", shopInfo.name);
                    report.SetParameterValue("ShopAddress", shopInfo.address);
                    report.SetParameterValue("ShopPhone", shopInfo.phone);
                    report.SetParameterValue("ShopEmail", shopInfo.email);
                }

                // Set date range parameters
                report.SetParameterValue("StartDate", startDate.ToString("dd/MM/yyyy"));
                report.SetParameterValue("EndDate", endDate.ToString("dd/MM/yyyy"));

                // Prepare report
                report.Prepare();

                // Show report
                report.Show();
            }
        }

        private async System.Threading.Tasks.Task GenerateTopPartsReport(DateTime startDate, DateTime endDate)
        {
            // Get top parts data
            var topParts = await _partCmd.GetTopPartsAsync(startDate, endDate);

            // Create report
            using (Report report = new Report())
            {
                // Get report file path
                string reportFile = Path.Combine(Application.StartupPath, "Reports", "TopPartsReport.frx");

                // Check if report file exists
                if (!File.Exists(reportFile))
                {
                    // Create directory if it doesn't exist
                    Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                    // Copy the report file from the project to the output directory
                    File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "TopPartsReport.frx"), reportFile);

                    if (!File.Exists(reportFile))
                    {
                        KryptonMessageBox.Show("ملف التقرير غير موجود: " + reportFile, "خطأ",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return;
                    }
                }

                // Load report
                report.Load(reportFile);

                // Register data
                report.RegisterData(topParts, "TopParts");

                // Get shop info
                var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                if (shopInfo != null)
                {
                    report.SetParameterValue("ShopName", shopInfo.name);
                    report.SetParameterValue("ShopAddress", shopInfo.address);
                    report.SetParameterValue("ShopPhone", shopInfo.phone);
                    report.SetParameterValue("ShopEmail", shopInfo.email);
                }

                // Set date range parameters
                report.SetParameterValue("StartDate", startDate.ToString("dd/MM/yyyy"));
                report.SetParameterValue("EndDate", endDate.ToString("dd/MM/yyyy"));

                // Prepare report
                report.Prepare();

                // Show report
                report.Show();
            }
        }

        private async System.Threading.Tasks.Task GenerateSalesDataReport(DateTime startDate, DateTime endDate)
        {
            // Get sales data
            var salesData = await _repairOrderCmd.GetSalesDataAsync(startDate, endDate);

            // Create report
            using (Report report = new Report())
            {
                // Get report file path
                string reportFile = Path.Combine(Application.StartupPath, "Reports", "SalesDataReport.frx");

                // Check if report file exists
                if (!File.Exists(reportFile))
                {
                    // Create directory if it doesn't exist
                    Directory.CreateDirectory(Path.Combine(Application.StartupPath, "Reports"));

                    // Copy the report file from the project to the output directory
                    File.Copy(Path.Combine(Application.StartupPath, "..", "..", "Reports", "SalesDataReport.frx"), reportFile);

                    if (!File.Exists(reportFile))
                    {
                        KryptonMessageBox.Show("ملف التقرير غير موجود: " + reportFile, "خطأ",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        return;
                    }
                }

                // Load report
                report.Load(reportFile);

                // Register data
                report.RegisterData(salesData, "SalesData");

                // Get shop info
                var shopInfo = await _shopInfoCmd.GetShopInfoAsync();
                if (shopInfo != null)
                {
                    report.SetParameterValue("ShopName", shopInfo.name);
                    report.SetParameterValue("ShopAddress", shopInfo.address);
                    report.SetParameterValue("ShopPhone", shopInfo.phone);
                    report.SetParameterValue("ShopEmail", shopInfo.email);
                }

                // Set date range parameters
                report.SetParameterValue("StartDate", startDate.ToString("dd/MM/yyyy"));
                report.SetParameterValue("EndDate", endDate.ToString("dd/MM/yyyy"));

                // Prepare report
                report.Prepare();

                // Show report
                report.Show();
            }
        }
    }
}
