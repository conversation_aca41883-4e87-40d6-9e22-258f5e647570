﻿﻿namespace IRepairIT.FRMS.FDEVICES
{
    partial class FRM_DEVICE
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FRM_DEVICE));
            this.kryptonLabel1 = new Krypton.Toolkit.KryptonLabel();
            this.cmbCustomer = new Krypton.Toolkit.KryptonComboBox();
            this.btnAddCustomer = new Krypton.Toolkit.ButtonSpecAny();
            this.kryptonLabel2 = new Krypton.Toolkit.KryptonLabel();
            this.cmbDeviceType = new Krypton.Toolkit.KryptonComboBox();
            this.kryptonLabel3 = new Krypton.Toolkit.KryptonLabel();
            this.txtBrand = new Krypton.Toolkit.KryptonTextBox();
            this.kryptonLabel4 = new Krypton.Toolkit.KryptonLabel();
            this.txtModel = new Krypton.Toolkit.KryptonTextBox();
            this.kryptonLabel5 = new Krypton.Toolkit.KryptonLabel();
            this.txtSerialNumber = new Krypton.Toolkit.KryptonTextBox();
            this.kryptonLabel6 = new Krypton.Toolkit.KryptonLabel();
            this.txtIMEI = new Krypton.Toolkit.KryptonTextBox();
            this.kryptonLabel7 = new Krypton.Toolkit.KryptonLabel();
            this.txtProblem = new Krypton.Toolkit.KryptonTextBox();
            this.kryptonLabel8 = new Krypton.Toolkit.KryptonLabel();
            this.txtCondition = new Krypton.Toolkit.KryptonTextBox();
            this.kryptonLabel9 = new Krypton.Toolkit.KryptonLabel();
            this.txtPassword = new Krypton.Toolkit.KryptonTextBox();
            this.kryptonLabel10 = new Krypton.Toolkit.KryptonLabel();
            this.dtpExpectedDelivery = new Krypton.Toolkit.KryptonDateTimePicker();
            this.kryptonLabel11 = new Krypton.Toolkit.KryptonLabel();
            this.txtAccessories = new Krypton.Toolkit.KryptonTextBox();
            this.kryptonLabel12 = new Krypton.Toolkit.KryptonLabel();
            this.btnUploadImages = new Krypton.Toolkit.KryptonButton();
            this.btnWebcamCapture = new Krypton.Toolkit.KryptonButton();
            this.upload_images_help = new Krypton.Toolkit.KryptonLabel();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.btnSave = new Krypton.Toolkit.KryptonButton();
            this.btnCancel = new Krypton.Toolkit.KryptonButton();
            ((System.ComponentModel.ISupportInitialize)(this.cmbCustomer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDeviceType)).BeginInit();
            this.SuspendLayout();
            //
            // kryptonLabel1
            //
            this.kryptonLabel1.Location = new System.Drawing.Point(21, 21);
            this.kryptonLabel1.Name = "kryptonLabel1";
            this.kryptonLabel1.Size = new System.Drawing.Size(51, 24);
            this.kryptonLabel1.TabIndex = 0;
            this.kryptonLabel1.Values.Text = "Client *";
            //
            // cmbCustomer
            //
            this.cmbCustomer.ButtonSpecs.AddRange(new Krypton.Toolkit.ButtonSpecAny[] {
            this.btnAddCustomer});
            this.cmbCustomer.DropDownWidth = 400;
            this.cmbCustomer.Location = new System.Drawing.Point(21, 51);
            this.cmbCustomer.Name = "cmbCustomer";
            this.cmbCustomer.Size = new System.Drawing.Size(400, 25);
            this.cmbCustomer.TabIndex = 1;
            //
            // btnAddCustomer
            //
            this.btnAddCustomer.Image = global::IRepairIT.Properties.Resources.button_circle_add;
            this.btnAddCustomer.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            this.btnAddCustomer.Click += new System.EventHandler(this.BtnAddCustomer_Click);
            //
            // kryptonLabel2
            //
            this.kryptonLabel2.Location = new System.Drawing.Point(427, 21);
            this.kryptonLabel2.Name = "kryptonLabel2";
            this.kryptonLabel2.Size = new System.Drawing.Size(104, 24);
            this.kryptonLabel2.TabIndex = 2;
            this.kryptonLabel2.Values.Text = "Type d\'appareil *";
            //
            // cmbDeviceType
            //
            this.cmbDeviceType.DropDownWidth = 400;
            this.cmbDeviceType.Location = new System.Drawing.Point(427, 51);
            this.cmbDeviceType.Name = "cmbDeviceType";
            this.cmbDeviceType.Size = new System.Drawing.Size(400, 25);
            this.cmbDeviceType.TabIndex = 3;
            //
            // kryptonLabel3
            //
            this.kryptonLabel3.Location = new System.Drawing.Point(21, 82);
            this.kryptonLabel3.Name = "kryptonLabel3";
            this.kryptonLabel3.Size = new System.Drawing.Size(64, 24);
            this.kryptonLabel3.TabIndex = 4;
            this.kryptonLabel3.Values.Text = "Marque *";
            //
            // txtBrand
            //
            this.txtBrand.Location = new System.Drawing.Point(21, 112);
            this.txtBrand.Name = "txtBrand";
            this.txtBrand.Size = new System.Drawing.Size(400, 27);
            this.txtBrand.TabIndex = 5;
            //
            // kryptonLabel4
            //
            this.kryptonLabel4.Location = new System.Drawing.Point(427, 82);
            this.kryptonLabel4.Name = "kryptonLabel4";
            this.kryptonLabel4.Size = new System.Drawing.Size(65, 24);
            this.kryptonLabel4.TabIndex = 6;
            this.kryptonLabel4.Values.Text = "Modèle *";
            //
            // txtModel
            //
            this.txtModel.Location = new System.Drawing.Point(427, 112);
            this.txtModel.Name = "txtModel";
            this.txtModel.Size = new System.Drawing.Size(400, 27);
            this.txtModel.TabIndex = 7;
            //
            // kryptonLabel5
            //
            this.kryptonLabel5.Location = new System.Drawing.Point(21, 145);
            this.kryptonLabel5.Name = "kryptonLabel5";
            this.kryptonLabel5.Size = new System.Drawing.Size(110, 24);
            this.kryptonLabel5.TabIndex = 8;
            this.kryptonLabel5.Values.Text = "Numéro de série";
            //
            // txtSerialNumber
            //
            this.txtSerialNumber.Location = new System.Drawing.Point(21, 175);
            this.txtSerialNumber.Name = "txtSerialNumber";
            this.txtSerialNumber.Size = new System.Drawing.Size(400, 27);
            this.txtSerialNumber.TabIndex = 9;
            //
            // kryptonLabel6
            //
            this.kryptonLabel6.Location = new System.Drawing.Point(427, 145);
            this.kryptonLabel6.Name = "kryptonLabel6";
            this.kryptonLabel6.Size = new System.Drawing.Size(39, 24);
            this.kryptonLabel6.TabIndex = 10;
            this.kryptonLabel6.Values.Text = "IMEI";
            //
            // txtIMEI
            //
            this.txtIMEI.Location = new System.Drawing.Point(427, 175);
            this.txtIMEI.Name = "txtIMEI";
            this.txtIMEI.Size = new System.Drawing.Size(400, 27);
            this.txtIMEI.TabIndex = 11;
            //
            // kryptonLabel7
            //
            this.kryptonLabel7.Location = new System.Drawing.Point(21, 208);
            this.kryptonLabel7.Name = "kryptonLabel7";
            this.kryptonLabel7.Size = new System.Drawing.Size(76, 24);
            this.kryptonLabel7.TabIndex = 12;
            this.kryptonLabel7.Values.Text = "Problème *";
            //
            // txtProblem
            //
            this.txtProblem.Location = new System.Drawing.Point(21, 238);
            this.txtProblem.Multiline = true;
            this.txtProblem.Name = "txtProblem";
            this.txtProblem.Size = new System.Drawing.Size(806, 60);
            this.txtProblem.TabIndex = 13;
            //
            // kryptonLabel8
            //
            this.kryptonLabel8.Location = new System.Drawing.Point(21, 304);
            this.kryptonLabel8.Name = "kryptonLabel8";
            this.kryptonLabel8.Size = new System.Drawing.Size(113, 24);
            this.kryptonLabel8.TabIndex = 14;
            this.kryptonLabel8.Values.Text = "État de l\'appareil";
            //
            // txtCondition
            //
            this.txtCondition.Location = new System.Drawing.Point(21, 334);
            this.txtCondition.Multiline = true;
            this.txtCondition.Name = "txtCondition";
            this.txtCondition.Size = new System.Drawing.Size(400, 60);
            this.txtCondition.TabIndex = 15;
            //
            // kryptonLabel9
            //
            this.kryptonLabel9.Location = new System.Drawing.Point(427, 304);
            this.kryptonLabel9.Name = "kryptonLabel9";
            this.kryptonLabel9.Size = new System.Drawing.Size(133, 24);
            this.kryptonLabel9.TabIndex = 16;
            this.kryptonLabel9.Values.Text = "Mot de passe (si fourni)";
            //
            // txtPassword
            //
            this.txtPassword.Location = new System.Drawing.Point(427, 334);
            this.txtPassword.Name = "txtPassword";
            this.txtPassword.Size = new System.Drawing.Size(400, 27);
            this.txtPassword.TabIndex = 17;
            //
            // kryptonLabel10
            //
            this.kryptonLabel10.Location = new System.Drawing.Point(427, 367);
            this.kryptonLabel10.Name = "kryptonLabel10";
            this.kryptonLabel10.Size = new System.Drawing.Size(153, 24);
            this.kryptonLabel10.TabIndex = 18;
            this.kryptonLabel10.Values.Text = "Date de livraison prévue";
            //
            // dtpExpectedDelivery
            //
            this.dtpExpectedDelivery.Location = new System.Drawing.Point(427, 397);
            this.dtpExpectedDelivery.Name = "dtpExpectedDelivery";
            this.dtpExpectedDelivery.Size = new System.Drawing.Size(400, 25);
            this.dtpExpectedDelivery.TabIndex = 19;
            //
            // kryptonLabel11
            //
            this.kryptonLabel11.Location = new System.Drawing.Point(21, 400);
            this.kryptonLabel11.Name = "kryptonLabel11";
            this.kryptonLabel11.Size = new System.Drawing.Size(82, 24);
            this.kryptonLabel11.TabIndex = 20;
            this.kryptonLabel11.Values.Text = "Accessoires";
            //
            // txtAccessories
            //
            this.txtAccessories.Location = new System.Drawing.Point(21, 430);
            this.txtAccessories.Multiline = true;
            this.txtAccessories.Name = "txtAccessories";
            this.txtAccessories.Size = new System.Drawing.Size(400, 60);
            this.txtAccessories.TabIndex = 21;
            //
            // kryptonLabel12
            //
            this.kryptonLabel12.Location = new System.Drawing.Point(21, 496);
            this.kryptonLabel12.Name = "kryptonLabel12";
            this.kryptonLabel12.Size = new System.Drawing.Size(125, 24);
            this.kryptonLabel12.TabIndex = 22;
            this.kryptonLabel12.Values.Text = "Images de l\'appareil";
            //
            // btnUploadImages
            //
            this.btnUploadImages.Location = new System.Drawing.Point(21, 526);
            this.btnUploadImages.Name = "btnUploadImages";
            this.btnUploadImages.Size = new System.Drawing.Size(150, 30);
            this.btnUploadImages.TabIndex = 23;
            this.btnUploadImages.Values.Text = "Télécharger des images";
            //
            // btnWebcamCapture
            //
            this.btnWebcamCapture.Location = new System.Drawing.Point(177, 526);
            this.btnWebcamCapture.Name = "btnWebcamCapture";
            this.btnWebcamCapture.Size = new System.Drawing.Size(150, 30);
            this.btnWebcamCapture.TabIndex = 28;
            this.btnWebcamCapture.Values.Text = "Capturer via webcam";
            this.btnWebcamCapture.Click += new System.EventHandler(this.BtnWebcamCapture_Click);
            //
            // upload_images_help
            //
            this.upload_images_help.Location = new System.Drawing.Point(333, 532);
            this.upload_images_help.Name = "upload_images_help";
            this.upload_images_help.Size = new System.Drawing.Size(300, 20);
            this.upload_images_help.TabIndex = 24;
            this.upload_images_help.Values.Text = "Formats supportés: JPG, PNG, GIF - Taille max: 5MB";
            //
            // flowLayoutPanel1
            //
            this.flowLayoutPanel1.AutoScroll = true;
            this.flowLayoutPanel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.flowLayoutPanel1.Location = new System.Drawing.Point(21, 562);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            this.flowLayoutPanel1.Size = new System.Drawing.Size(806, 150);
            this.flowLayoutPanel1.TabIndex = 25;
            //
            // btnSave
            //
            this.btnSave.Location = new System.Drawing.Point(677, 718);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(150, 30);
            this.btnSave.TabIndex = 26;
            this.btnSave.Values.Text = "Enregistrer";
            this.btnSave.Click += new System.EventHandler(this.BtnSave_Click);
            //
            // btnCancel
            //
            this.btnCancel.Location = new System.Drawing.Point(521, 718);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(150, 30);
            this.btnCancel.TabIndex = 27;
            this.btnCancel.Values.Text = "Annuler";
            this.btnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            //
            // FRM_DEVICE
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(852, 760);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.flowLayoutPanel1);
            this.Controls.Add(this.upload_images_help);
            this.Controls.Add(this.btnWebcamCapture);
            this.Controls.Add(this.btnUploadImages);
            this.Controls.Add(this.kryptonLabel12);
            this.Controls.Add(this.txtAccessories);
            this.Controls.Add(this.kryptonLabel11);
            this.Controls.Add(this.dtpExpectedDelivery);
            this.Controls.Add(this.kryptonLabel10);
            this.Controls.Add(this.txtPassword);
            this.Controls.Add(this.kryptonLabel9);
            this.Controls.Add(this.txtCondition);
            this.Controls.Add(this.kryptonLabel8);
            this.Controls.Add(this.txtProblem);
            this.Controls.Add(this.kryptonLabel7);
            this.Controls.Add(this.txtIMEI);
            this.Controls.Add(this.kryptonLabel6);
            this.Controls.Add(this.txtSerialNumber);
            this.Controls.Add(this.kryptonLabel5);
            this.Controls.Add(this.txtModel);
            this.Controls.Add(this.kryptonLabel4);
            this.Controls.Add(this.txtBrand);
            this.Controls.Add(this.kryptonLabel3);
            this.Controls.Add(this.cmbDeviceType);
            this.Controls.Add(this.kryptonLabel2);
            this.Controls.Add(this.cmbCustomer);
            this.Controls.Add(this.kryptonLabel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FRM_DEVICE";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Appareil";
            this.Load += new System.EventHandler(this.FRM_DEVICE_Load);
            ((System.ComponentModel.ISupportInitialize)(this.cmbCustomer)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDeviceType)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Krypton.Toolkit.KryptonLabel kryptonLabel1;
        private Krypton.Toolkit.KryptonComboBox cmbCustomer;
        private Krypton.Toolkit.ButtonSpecAny btnAddCustomer;
        private Krypton.Toolkit.KryptonLabel kryptonLabel2;
        private Krypton.Toolkit.KryptonComboBox cmbDeviceType;
        private Krypton.Toolkit.KryptonLabel kryptonLabel3;
        private Krypton.Toolkit.KryptonTextBox txtBrand;
        private Krypton.Toolkit.KryptonLabel kryptonLabel4;
        private Krypton.Toolkit.KryptonTextBox txtModel;
        private Krypton.Toolkit.KryptonLabel kryptonLabel5;
        private Krypton.Toolkit.KryptonTextBox txtSerialNumber;
        private Krypton.Toolkit.KryptonLabel kryptonLabel6;
        private Krypton.Toolkit.KryptonTextBox txtIMEI;
        private Krypton.Toolkit.KryptonLabel kryptonLabel7;
        private Krypton.Toolkit.KryptonTextBox txtProblem;
        private Krypton.Toolkit.KryptonLabel kryptonLabel8;
        private Krypton.Toolkit.KryptonTextBox txtCondition;
        private Krypton.Toolkit.KryptonLabel kryptonLabel9;
        private Krypton.Toolkit.KryptonTextBox txtPassword;
        private Krypton.Toolkit.KryptonLabel kryptonLabel10;
        private Krypton.Toolkit.KryptonDateTimePicker dtpExpectedDelivery;
        private Krypton.Toolkit.KryptonLabel kryptonLabel11;
        private Krypton.Toolkit.KryptonTextBox txtAccessories;
        private Krypton.Toolkit.KryptonLabel kryptonLabel12;
        private Krypton.Toolkit.KryptonButton btnUploadImages;
        private Krypton.Toolkit.KryptonButton btnWebcamCapture;
        private Krypton.Toolkit.KryptonLabel upload_images_help;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private Krypton.Toolkit.KryptonButton btnSave;
        private Krypton.Toolkit.KryptonButton btnCancel;
    }
}
