﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Enums
{
    public enum RepairOrderStatus
    {
        [Display(Name = "Re<PERSON><PERSON>")]
        received,

        [Display(Name = "Diagnostiqué")]
        diagnosed,

        [Display(Name = "En attente de pièces")]
        waiting_parts,

        [Display(Name = "En cours")]
        in_progress,

        [<PERSON><PERSON>lay(Name = "<PERSON><PERSON><PERSON><PERSON>")]
        repaired,

        [<PERSON>splay(Name = "Prê<PERSON>")]
        ready,

        [<PERSON>splay(Name = "<PERSON><PERSON>")]
        delivered,

        [<PERSON><PERSON>lay(Name = "<PERSON><PERSON><PERSON>")]
        cancelled,

        [Display(Name = "Non réparable")]
        unrepairable
    }
}
