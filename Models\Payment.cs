﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class Payment
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Identifiant de la commande de réparation")]
        public int RepairOrderId { get; set; }

        [Display(Name = "Montant")]
        public decimal Amount { get; set; }

        [Display(Name = "Méthode de paiement")]
        public string PaymentMethod { get; set; }

        [Display(Name = "Notes")]
        public string Notes { get; set; }

        [Display(Name = "Identifiant de l'utilisateur")]
        public int? UserId { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        // Navigation properties
        [Display(Name = "Commande de réparation")]
        public RepairOrder RepairOrder { get; set; }

        [Display(Name = "Utilisateur")]
        public User User { get; set; }
    }
}
