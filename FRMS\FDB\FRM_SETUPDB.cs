﻿using Dapper;
using IRepairIT.Data;
using IRepairIT.DB;
using IRepairIT.FRMS.FLOGIN;
using Krypton.Toolkit;
using MySql.Data.MySqlClient;
using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FDB
{
    public partial class FRM_SETUPDB : KryptonForm
    {
        private readonly DatabaseManager _dbManager;
        private string _databaseName;
        private string _logoPath;
        private int _currentStep = 1;
        private int _totalSteps = 2;
        private int _year;

        private string _shopName;
        private string _activity;
        private string _address;
        private string _phone;
        private string _email;
        private string _website;
        public FRM_SETUPDB()
        {
            InitializeComponent();
            _dbManager = new DatabaseManager();
            _logoPath = string.Empty;
            numYear.Value = DateTime.Now.Year;

            UpdateWizardButtons();
            ShowCurrentStep();
        }
        private void UpdateWizardButtons()
        {
            btnBack.Enabled = (_currentStep > 1);
            btnNext.Visible = (_currentStep < _totalSteps);
            btnFinish.Visible = (_currentStep == _totalSteps);
        }
        private void ShowCurrentStep()
        {
            panelStep1.Visible = false;
            panelStep2.Visible = false;

            switch (_currentStep)
            {
                case 1:
                    panelStep1.Visible = true;
                    lblStepTitle.Text = "Étape 1: Année du Dossier";
                    lblStepDescription.Text = "Veuillez sélectionner l'année pour ce dossier.";
                    break;
                case 2:
                    panelStep2.Visible = true;
                    lblStepTitle.Text = "Étape 2: Informations de l'Atelier";
                    lblStepDescription.Text = "Veuillez saisir les informations de l'entreprise.";
                    break;
            }
            kryptonProgressBar1.Value = (_currentStep * 100) / _totalSteps;
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (!ValidateCurrentStep())
                return;

            SaveCurrentStepData();

            _currentStep++;
            ShowCurrentStep();
            UpdateWizardButtons();
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            _currentStep--;
            ShowCurrentStep();
            UpdateWizardButtons();
        }

        private void btnFinish_Click(object sender, EventArgs e)
        {
            if (!ValidateCurrentStep())
                return;

            SaveCurrentStepData();

            if (CreateDatabase())
            {
                KryptonMessageBox.Show("Configuration terminée avec succès!", "Succès", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                var loginForm = new FRM_LOGIN();
                this.Hide();
                loginForm.ShowDialog();
                this.Close();
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {

            if (KryptonMessageBox.Show("Êtes-vous sûr de vouloir annuler la configuration? Toutes les informations saisies seront perdues.",
                "Confirmation", KryptonMessageBoxButtons.YesNo, KryptonMessageBoxIcon.Question) == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {

            if (MessageBox.Show("Êtes-vous sûr de vouloir annuler la configuration? Toutes les informations saisies seront perdues.",
                "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private bool ValidateCurrentStep()
        {
            switch (_currentStep)
            {
                case 1:
                    if (numYear.Value < 2000 || numYear.Value > 2100)
                    {
                        KryptonMessageBox.Show("Veuillez saisir une année valide (entre 2000 et 2100).",
                            "Erreur de Validation", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                        return false;
                    }
                    return true;

                case 2:
                    if (string.IsNullOrWhiteSpace(txtShopName.Text))
                    {
                        errorProvider1.SetError(txtShopName, "Veuillez saisir le nom de l'entreprise");
                        return false;
                    }
                    return true;

                default:
                    return true;
            }
        }

        private void SaveCurrentStepData()
        {
            switch (_currentStep)
            {
                case 1:
                    _year = (int)numYear.Value;
                    _databaseName = $"irepairit{_year}";
                    break;

                case 2:
                    _shopName = txtShopName.Text.Trim();
                    _activity = txtActivity.Text.Trim();
                    _address = txtAddress.Text.Trim();
                    _phone = txtPhone.Text.Trim();
                    _email = txtEmail.Text.Trim();
                    _website = txtWebsite.Text.Trim();
                    break;
            }
        }

        private bool CreateDatabase()
        {
            try
            {
                Cursor = Cursors.WaitCursor;
                lblStepDescription.Text = "Création du dossier en cours...";
                Application.DoEvents();

                if (!_dbManager.CreateDatabase(_year.ToString()))
                {
                    KryptonMessageBox.Show("Impossible de créer le dossier.", "Erreur", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    return false;
                }

                if (!SaveShopInfo())
                {
                    KryptonMessageBox.Show("Impossible d'enregistrer les informations de l'atelier.", "Erreur", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la création du dossier: {ex.Message}", "Erreur", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                return false;
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private bool SaveShopInfo()
        {
            try
            {
                string connectionString = ConnectionSettings.LoadFromJson(
                    Path.Combine(Application.StartupPath, "connection.json"))
                    .GetConnectionString(_databaseName);

                string savedLogoPath = string.Empty;
                if (!string.IsNullOrEmpty(_logoPath))
                {
                    string logoDirectory = Path.Combine(Application.StartupPath, "logos");
                    if (!Directory.Exists(logoDirectory))
                    {
                        Directory.CreateDirectory(logoDirectory);
                    }

                    string fileName = $"logo_{DateTime.Now.ToString("yyyyMMddHHmmss")}{Path.GetExtension(_logoPath)}";
                    savedLogoPath = Path.Combine("logos", fileName);

                    File.Copy(_logoPath, Path.Combine(Application.StartupPath, savedLogoPath), true);
                }

                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();

                    var insertSql = @"
                        INSERT INTO shop_info (
                            name, activity, address, phone, email, website,
                            logo_path,created_at
                        ) VALUES (
                            @Name, @Activity, @Address, @Phone, @Email, @Website,
                            @LogoPath, NOW()
                        )";

                    connection.Execute(insertSql, new
                    {
                        Name = _shopName,
                        Activity = _activity,
                        Address = _address,
                        Phone = _phone,
                        Email = _email,
                        Website = _website,
                        LogoPath = savedLogoPath,
                    });
                }

                return true;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'enregistrement des informations: {ex.Message}", "Erreur", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                return false;
            }
        }

        private void kryptonButton1_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Fichiers image (*.jpg;*.jpeg;*.png;*.gif)|*.jpg;*.jpeg;*.png;*.gif";
                openFileDialog.Title = "Sélectionner un logo";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        pictureBoxLogo.Image = Image.FromFile(openFileDialog.FileName);
                        pictureBoxLogo.SizeMode = PictureBoxSizeMode.Zoom;

                        _logoPath = openFileDialog.FileName;
                    }
                    catch (Exception ex)
                    {
                        KryptonMessageBox.Show($"Erreur lors du chargement de l'image: {ex.Message}", "Erreur", KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }
                }
            }
        }

        private void pictureBoxLogo_DragDrop(object sender, DragEventArgs e)
        {

        }

        private void pictureBoxLogo_DragEnter(object sender, DragEventArgs e)
        {

        }
    }
}
