﻿using System.ComponentModel;

namespace IRepairIT.Models.DTO
{
    public class LatestCustomerDto
    {
        [DisplayName("#")]
        public int id { get; set; }

        [DisplayName("Nom")]
        public string name { get; set; }

        [Display<PERSON>ame("Téléphone")]
        public string phone { get; set; }

        [DisplayName("Date d'inscription")]
        public string created_at { get; set; }
    }
}
