﻿using Dapper;
using IRepairIT.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IRepairIT.Data.Common
{
    public class CustomerCommands
    {
        private readonly DataAccess _db;
        public CustomerCommands()
        {
            _db = new DataAccess();
        }
        public async Task<int> GetCount(string searchTerm)
        {
            const string sql = @"customers WHERE (name LIKE CONCAT('%', @searchTerm, '%') OR phone LIKE CONCAT('%', @searchTerm, '%'))";
            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            return await _db.CountQuery(sql, parameters);
        }
        public async Task<IEnumerable<Customer>> GetALL(string searchTerm, int offset, int pageSize)
        {
            const string sql = @"SELECT * FROM customers WHERE (name LIKE CONCAT('%', @searchTerm, '%') OR phone LIKE CONCAT('%', @searchTerm, '%')) ORDER BY name ASC LIMIT @pageSize OFFSET @offsetVal";

            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            parameters.Add("offsetVal", offset);
            parameters.Add("pageSize", pageSize);
            var results = await _db.QueryQuery<Customer>(sql, parameters);
            return results;
        }

        public async Task<Customer> GetByIdAsync(int id)
        {
            const string sql = @"
                SELECT
                    id,
                    name,
                    email,
                    phone,
                    address,
                    notes,
                    created_at,
                    updated_at
                FROM customers
                WHERE id = @Id";
            return await _db.QuerySingleOrDefaultQuery<Customer>(sql, new { Id = id });
        }

        public async Task<int> InsertAsync(Customer customer)
        {
            const string sql = @"
                INSERT INTO customers (name, email, phone, address, notes, created_at)
                VALUES (@name, @email, @phone, @address, @notes, @created_at)";

            customer.created_at = DateTime.Now;

            return await _db.InsertAndGetIdAsync(sql, customer);
        }

        public async Task<bool> UpdateAsync(Customer customer)
        {
            const string sql = @"
                UPDATE customers
                SET name = @name,
                    email = @email,
                    phone = @phone,
                    address = @address,
                    notes = @notes,
                    updated_at = @updated_at
                WHERE id = @id";

            customer.updated_at = DateTime.Now;

            int rowsAffected = await _db.ExecuteQuery(sql, customer);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            const string sql = @"DELETE FROM customers WHERE id = @Id";
            int rowsAffected = await _db.ExecuteQuery(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<bool> EmailExistsAsync(string email, int excludeId = 0)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            const string sql = @"
                SELECT COUNT(*) FROM customers
                WHERE email = @Email AND id != @ExcludeId";

            int count = await _db.ExecuteScalerQuery<int>(sql, new { Email = email, ExcludeId = excludeId });
            return count > 0;
        }

        public async Task<bool> PhoneExistsAsync(string phone, int excludeId = 0)
        {
            if (string.IsNullOrWhiteSpace(phone))
                return false;

            const string sql = @"
                SELECT COUNT(*) FROM customers
                WHERE phone = @Phone AND id != @ExcludeId";

            int count = await _db.ExecuteScalerQuery<int>(sql, new { Phone = phone, ExcludeId = excludeId });
            return count > 0;
        }
    }
}
