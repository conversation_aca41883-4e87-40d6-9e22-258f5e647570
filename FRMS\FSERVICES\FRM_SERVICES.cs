﻿using IRepairIT.Data.Common;
using IRepairIT.Models;
using Krypton.Toolkit;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FSERVICES
{
    public partial class FRM_SERVICES : KryptonForm
    {
        private ServiceCommands _cmd;
        private Service _service;
        private bool _isEditMode = false;
        private int _serviceId = 0;

        // Propriété pour pré-remplir le nom du service
        public string ServiceName
        {
            get { return txtUsername.Text; }
            set { txtUsername.Text = value; }
        }

        public FRM_SERVICES()
        {
            InitializeComponent();
            _cmd = new ServiceCommands();
            SetupForm();
        }

        public FRM_SERVICES(int serviceId)
        {
            InitializeComponent();
            _cmd = new ServiceCommands();
            _serviceId = serviceId;
            _isEditMode = true;
            SetupForm();
        }

        private void SetupForm()
        {
            // Set form title based on mode
            this.Text = _isEditMode ? "Modifier un service" : "Ajouter un service";

            // Set panel colors
            kryptonPanel1.StateCommon.Color1 = Color.FromArgb(250, 252, 252);

            // Set group box header style
            kryptonGroupBox1.StateCommon.Back.Color1 = Color.FromArgb(250, 252, 252);
            kryptonGroupBox1.StateCommon.Border.Color1 = Color.FromArgb(52, 152, 219);
            kryptonGroupBox1.StateCommon.Border.DrawBorders = Krypton.Toolkit.PaletteDrawBorders.Top | Krypton.Toolkit.PaletteDrawBorders.Bottom | Krypton.Toolkit.PaletteDrawBorders.Left | Krypton.Toolkit.PaletteDrawBorders.Right;
            kryptonGroupBox1.StateCommon.Border.Rounding = 6;
            kryptonGroupBox1.StateCommon.Border.Width = 1;
            kryptonGroupBox1.StateCommon.Content.ShortText.Color1 = Color.FromArgb(52, 152, 219);
            kryptonGroupBox1.StateCommon.Content.ShortText.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

            // Set label styles
            SetLabelStyle(lblUsername);
            SetLabelStyle(lblFullName);
            SetLabelStyle(lblEmail);
            SetLabelStyle(lblPhone);

            // Set text box styles
            SetTextBoxStyle(txtUsername);
            SetTextBoxStyle(txtPhone);

            // Set rich text box style
            txtDescription.StateCommon.Border.Color1 = Color.FromArgb(224, 224, 224);
            txtDescription.StateCommon.Border.DrawBorders = Krypton.Toolkit.PaletteDrawBorders.Top | Krypton.Toolkit.PaletteDrawBorders.Bottom | Krypton.Toolkit.PaletteDrawBorders.Left | Krypton.Toolkit.PaletteDrawBorders.Right;
            txtDescription.StateCommon.Border.Rounding = 6;
            txtDescription.StateCommon.Border.Width = 1;
            txtDescription.StateCommon.Content.Font = new Font("Segoe UI", 9.75F);

            // Set numeric up down style
            kryptonNumericUpDown1.StateCommon.Border.Color1 = Color.FromArgb(224, 224, 224);
            kryptonNumericUpDown1.StateCommon.Border.DrawBorders = Krypton.Toolkit.PaletteDrawBorders.Top | Krypton.Toolkit.PaletteDrawBorders.Bottom | Krypton.Toolkit.PaletteDrawBorders.Left | Krypton.Toolkit.PaletteDrawBorders.Right;
            kryptonNumericUpDown1.StateCommon.Border.Rounding = 6;
            kryptonNumericUpDown1.StateCommon.Border.Width = 1;
            kryptonNumericUpDown1.StateCommon.Content.Font = new Font("Segoe UI", 9.75F);

            // Set button event handlers
            btnSave.Click += BtnSave_Click;
            btnSaveAndNew.Click += BtnSaveAndNew_Click;
            btnSaveAndClose.Click += BtnSaveAndClose_Click;
            btnCancel.Click += BtnCancel_Click;

            // Load service data if in edit mode
            if (_isEditMode)
            {
                LoadServiceData().ConfigureAwait(false);
            }
        }

        private void SetLabelStyle(KryptonLabel label)
        {
            label.StateCommon.ShortText.Color1 = Color.FromArgb(64, 64, 64);
            label.StateCommon.ShortText.Font = new Font("Segoe UI", 9.75F, FontStyle.Bold);
            label.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Far;
        }

        private void SetTextBoxStyle(KryptonTextBox textBox)
        {
            textBox.StateCommon.Border.Color1 = Color.FromArgb(224, 224, 224);
            textBox.StateCommon.Border.DrawBorders = Krypton.Toolkit.PaletteDrawBorders.Top | Krypton.Toolkit.PaletteDrawBorders.Bottom | Krypton.Toolkit.PaletteDrawBorders.Left | Krypton.Toolkit.PaletteDrawBorders.Right;
            textBox.StateCommon.Border.Rounding = 6;
            textBox.StateCommon.Border.Width = 1;
            textBox.StateCommon.Content.Font = new Font("Segoe UI", 9.75F);
        }

        private async Task LoadServiceData()
        {
            try
            {
                // Load service data from database
                _service = await _cmd.GetByIdAsync(_serviceId);

                if (_service != null)
                {
                    // Populate form fields
                    txtUsername.Text = _service.name;
                    txtDescription.Text = _service.description;
                    kryptonNumericUpDown1.Value = _service.price;
                    txtPhone.Text = _service.duration.ToString();
                }
                else
                {
                    KryptonMessageBox.Show("Le service demandé n'existe pas.", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des données: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async Task SaveService(bool closeForm, bool clearForm)
        {
            try
            {
                // Validate form
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    KryptonMessageBox.Show("Veuillez saisir le nom du service.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (kryptonNumericUpDown1.Value <= 0)
                {
                    KryptonMessageBox.Show("Le prix doit être supérieur à zéro.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    kryptonNumericUpDown1.Focus();
                    return;
                }

                // Create service object
                var service = new Service
                {
                    name = txtUsername.Text.Trim(),
                    description = txtDescription.Text.Trim(),
                    price = kryptonNumericUpDown1.Value,
                    duration = string.IsNullOrWhiteSpace(txtPhone.Text) ? 0 : Convert.ToInt32(txtPhone.Text)
                };

                if (_isEditMode)
                {
                    // Update existing service
                    service.id = _serviceId;
                    await _cmd.UpdateAsync(service);
                    KryptonMessageBox.Show("Le service a été mis à jour avec succès.", "Succès",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                    // Set DialogResult to OK to indicate success
                    this.DialogResult = DialogResult.OK;
                }
                else
                {
                    // Insert new service
                    int newId = await _cmd.InsertAsync(service);
                    KryptonMessageBox.Show("Le service a été ajouté avec succès.", "Succès",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                    // Set DialogResult to OK to indicate success
                    this.DialogResult = DialogResult.OK;

                    // Switch to edit mode if not clearing form
                    if (!clearForm)
                    {
                        _serviceId = newId;
                        _isEditMode = true;
                        this.Text = "Modifier un service";
                    }
                }

                // Clear form if requested
                if (clearForm)
                {
                    ClearForm();
                }

                // Close form if requested
                if (closeForm)
                {
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void ClearForm()
        {
            txtUsername.Text = string.Empty;
            txtDescription.Text = string.Empty;
            kryptonNumericUpDown1.Value = 0;
            txtPhone.Text = string.Empty;

            _isEditMode = false;
            _serviceId = 0;
            this.Text = "Ajouter un service";

            txtUsername.Focus();
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            await SaveService(false, false);
        }

        private async void BtnSaveAndNew_Click(object sender, EventArgs e)
        {
            await SaveService(false, true);
        }

        private async void BtnSaveAndClose_Click(object sender, EventArgs e)
        {
            await SaveService(true, false);
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void FRM_SERVICES_Load(object sender, EventArgs e)
        {
            // Set focus to the first field
            txtUsername.Focus();
        }

        private void txtPhone_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Only allow digits and control characters (like backspace)
            if (!char.IsDigit(e.KeyChar) && !char.IsControl(e.KeyChar))
            {
                e.Handled = true;
            }
        }
    }
}
