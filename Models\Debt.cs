﻿using IRepairIT.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class Debt
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Identifiant du client")]
        public int CustomerId { get; set; }

        [Display(Name = "Identifiant de la commande de réparation")]
        public int? RepairOrderId { get; set; }

        [Display(Name = "Montant")]
        public decimal Amount { get; set; }

        [Display(Name = "Montant payé")]
        public decimal PaidAmount { get; set; }

        [Display(Name = "Date de la dette")]
        public DateTime DebtDate { get; set; }

        [Display(Name = "Date d'échéance")]
        public DateTime? DueDate { get; set; }

        [Display(Name = "Statut")]
        public PaymentStatus Status { get; set; }

        [Display(Name = "Notes")]
        public string Notes { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Date de mise à jour")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        [Display(Name = "Client")]
        public Customer Customer { get; set; }

        [Display(Name = "Commande de réparation")]
        public RepairOrder RepairOrder { get; set; }
    }
}
