# Assistant de Création de Client - FRM_CUSTOMERS_NEW_WIZARD

## Description
Ce formulaire est un assistant (wizard) en 3 étapes qui permet de créer un nouveau client, son appareil et un ordre de réparation en une seule opération fluide.

## Fonctionnalités

### Étape 1: Informations du Client
- **Nom** (obligatoire): Nom complet du client
- **Téléphone** (obligatoire): Numéro de téléphone unique
- **Email** (optionnel): Adresse email avec validation de format et unicité
- **Adresse** (optionnel): Adresse physique du client
- **Notes** (optionnel): Notes supplémentaires sur le client

### Étape 2: Informations de l'Appareil
- **Type** (obligatoire): Type d'appareil (Smartphone, Tablette, etc.)
- **Marque** (obligatoire): Marque de l'appareil
- **Modèle** (obligatoire): Modèle de l'appareil
- **N° série** (optionnel): Num<PERSON>ro de série
- **IMEI** (optionnel): Code IMEI pour les appareils mobiles
- **Problème** (obligatoire): Description du problème
- **État** (optionnel): État physique de l'appareil
- **Mot de passe** (optionnel): Mot de passe de déverrouillage
- **Accessoires** (optionnel): Liste des accessoires fournis
- **Livraison prévue**: Date de livraison estimée

### Étape 3: Ordre de Réparation
- **N° commande**: Généré automatiquement
- **Technicien** (optionnel): Technicien assigné
- **Garantie**: Période de garantie en jours (défaut: 30)
- **Notes de réparation**: Notes sur la réparation
- **Notes techniques**: Notes techniques détaillées
- **Services**: Liste des services à effectuer
- **Pièces**: Liste des pièces nécessaires
- **Total**: Calcul automatique du coût total

## Validation

### Étape 1 - Client
- Nom obligatoire
- Téléphone obligatoire et unique
- Email valide et unique (si fourni)

### Étape 2 - Appareil
- Type obligatoire
- Marque obligatoire
- Modèle obligatoire
- Description du problème obligatoire

### Étape 3 - Ordre de Réparation
- Période de garantie doit être un nombre positif

## Navigation
- **Suivant >**: Passe à l'étape suivante après validation
- **< Retour**: Retourne à l'étape précédente
- **Terminer**: Sauvegarde toutes les données (étape 3 uniquement)
- **Annuler**: Ferme l'assistant sans sauvegarder

## Utilisation

### Depuis le Menu Principal
1. Aller dans le menu **Clients**
2. Cliquer sur **Nouveau client (Assistant)**
3. Suivre les 3 étapes
4. Cliquer sur **Terminer** pour sauvegarder

### Résultat
Après completion réussie:
- Un nouveau client est créé
- Un nouvel appareil est associé au client
- Un nouvel ordre de réparation est créé
- Toutes les données sont liées automatiquement

## Avantages
- **Processus guidé**: Interface intuitive en étapes
- **Validation en temps réel**: Vérification des données à chaque étape
- **Intégrité des données**: Toutes les relations sont créées automatiquement
- **Efficacité**: Création complète en une seule opération
- **Flexibilité**: Possibilité d'ajouter services et pièces directement

## Notes Techniques
- Utilise des transactions pour garantir la cohérence des données
- Validation asynchrone pour les vérifications d'unicité
- Interface responsive avec Krypton Toolkit
- Gestion d'erreurs complète avec messages utilisateur clairs
