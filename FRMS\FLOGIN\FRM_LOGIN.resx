﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="kryptonPictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAABGdBTUEAALGPC/xhBQAADaRJREFUeF7t
        nHmQnEUZhwMooHJZglIW/sdVggpmZyIoFlClSKklKohXlYiIJDNBKa3Cg0jk8karrLLAAlSkBCEEAjkg
        IeQAcpD7vghJgCRsQsh9bpb2fXu7Jz2zvZOd2Z35vpl5nqpfzUx3fz39ff2+/euebGUAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        APSRVatWnaByHwEgZO3atcNEt7iPAOBR55Dk2CLaumbNmpNcMQAozj2MEy4C4AncwycILgLgUccIksML
        FwGIuIcXLgIgiRCePUqFi0DrUsY9vHARaF0kAYrOHvNXrLAKy0S4CLQeMfcY+vgT5saRT4TJocJFoPWQ
        wC86eyxcudKccPtd5rjb7sRFoLXpyT0GDBtuhYtASyMBH3UPnyC4CLQsh3MPL1wEWhIJ9LLu4YWLQMvR
        W/fwwkWgpZAA75V7eOEi0DJU6h5euAi0BBLYFbmHFy4CTU+17uGFi0BTIwFdlXt44SLQtPTVPbxwEWhK
        JJD75B5euAg0Hf3lHl64CDQVEsD94h5euAg0Df3tHl64CDQFErj96h5euAg0PLVyDy9cBBoaCdiauIcX
        LgINS63dwwsXgYZEArWm7uGFi0DDUS/38MJFoKGQAK2Le3jhItAw1Ns9vHARaAgkMOvqHl64CKSepNzD
        CxeBVCMBmYh7eOEikFqSdg8vXARSiQRiou7hhYtA6kiLe3jhIpAqJABT4R5euAikhrS5hxcuAqlAAi9V
        7uGFi0DipNU9vHARSBQJuFS6hxcuAomRdvfwwkUgESTQUu0eXrgI1J1GcQ8vXATqigRYQ7iHFy4CdaMe
        7qEBrYrVVSsdYzhm0Ra9F3dbAP2DBFbN3UO3RLVIOlwEakq93EMDuVaJF45dxFkE+g8JqLq4h+8fF4GG
        oZ7u4fvHRaBhkEC6JQwsDeQ6HKRxEUg/SbiHFy4CqUcCqK5nj1LhIpBaknQPL1wEUosEThJnD03IJJIS
        F4Hek6B7aFImsa3DRaD3SMAkFqRJJqe7fYCeSUOAyntcBNKJBEriwYmLQCpJU2BKGS4C6UICJDVBiYtA
        qkhjQEodLgLpQAIjdcGIi0AqSHMgShtcBJJFAiK1QYiLQKI0QgBKW1wEkkECIfXBh4tAIjRS4Mk1uAjU
        FwmAhgk6XATqSiMGnFyLi0B9kIlvuGDDRaAuNHKgSR+4CNQWmfCGDTJcBGpKMwSY9IWLQG2QiW744MJF
        oCY0U2BJn7gI9C8ywU0TVLgI9CvNGFDSNy4C/YNMbNMFEy4C/UIzB5J8By4CfUMmtGmDCBeBPtEKASTf
        hYtAdchENn3w4CJQFa0UOPKduAhUhkxgywQNLgIV0YoBI9+Ni0DvkIlruWDBRaBXtHKgyBhwESiPTFjL
        BgkuAmUhQHARKINMVMsHB4sERCEwDiFjwkWgGJkggsLBYgFFEBDdkbGxYEAXMjEEQwksGmAhEHpGxsjC
        0erIhBAEPcDi0eIQAIdHxsoC0qrIRBRNfp3UUJMfW0TqJFwkadatW/cZmYjJJRNTazXcxOuYS+6hHtqi
        yemGAEkSmZxaqSEnHRdpcSITUys17ITr2EvupR7iLAIAAAAAAAAAAAAAAAAAAAAAAAAAAADQ/5hc9kyT
        a/uzyWdmm1xmi6jDvtrPUn7joDNc0ygmP/BsaW+K1bbUDBhwhGtSwAw9/Ripb+/W/ifnVfzn4nLdU936
        uaHtXFcN0DfM8AFHSnLcKYHV2S3QitUpyXKbtneXFhFPEFG+7XLXpIB833XRthUmiBmSOVWu00Qu7esv
        rglA39BgKgku1TyRrsz6Wlw3JPNHd2kRPSZILjPeNSkgZYtL2nSp0gTJZW+O9pPLbDbDzznaNQOoDgn2
        TElgbTdDBl3iqi0mn73UloftBg8a6KoLRBLkYOF9sOWR7/xCtI2q4gTJrAiuD99LIrdd6ZoBVIcE0gNF
        QZXL/MJVFSFB/fPidtn7XVWBbgmSzzxeeD+k7T7XTL9zfKE8lxkRvK8oQczQ7EVF1+YzV8vr8qBsnGsK
        UB0SRKuDgJKV/sIPuqoizPUDTy5ql8uuclUFuidI9gZ5Xeg+7zVDzz9FnaRQr4mSzw4PPleWILnMv4Jr
        d5ibLniPJOKtQVmnGTroNNccoHIkiPYEAbXLFUeR+m1B292uuEA0QfKZ7wdlvxYdcizdalWZIObaTx8v
        7Xcfujb7oC2/cdAZh8qs+J9GoHokgIIEye50xVGkTXgO6V2CyEFZ3m90ZZtE+9z7Jfrzb9UJks9cX3Rd
        PnuZq9JxzgrqVsd+ZgboFTaAwkDr5y2WLR+SGVZUbpW9ruuaKhMkl50RXNdurrrqKFcl39d2U1AnTlX8
        owNAr5EAquEh3SVIV3KFW7lN5pqLj+26pvIEkXbnFF1zeD3kLgWoDDO0LVsSTNtNvu1iV23RFdiWh+3y
        2TZXXaCnBFHk871B+XBXXF2C5LN3F11zeO2RJD3RXQ5QGeIOf+0WVEMyc+V1lHstrstl/uQuLaJsgui/
        eOezV1gFSVBpgrgzzebgmnbp+5FuymUmBm1Ug10XAJVh/9Qkn/mtBNE7JUFVqk7Z39/R6z81CRKkJypO
        kCFtVxa172lLeP3A90pd+KvbLFcFUB1mcNtZ9o8Sc5k5oq0i/VdufZXPWp490zWNUpcEyWXGBe07xC1O
        dVXdkPq/B231X/8/7qoAAAAAAAAAANLMkw8bhFAPihYihLoULUxYHxg70vxz3atm8/59Zn9np5m/bau5
        8uUXo217q+/MnmZW7dpp+2vft9eMeXN9tF29dLLco+c3yxcXyu9+ZbkrNebYpx4tuqa/lV8w232TMWc9
        N7pQfvZzY2zZPWtWFbXvq2rVb00VLUxYU97aZB/kqI1vmLtWLjGb9u2zny998flo+3eNeiRa7nXas6NM
        xzvvmGU7t5tfLV1gg3DR9m3RtvVSmCCv7dltjpR7OPqp/9lFwVNpghzuOZRqqjznTnkuyrBlCwvltQhk
        Hdsp40aa361caq6Y+UK0TSoVLUxQF7840U7OpM3thbJLXNlkV+YncOybG8wLMslrd+8y7x/zuFmwfavZ
        2dFhdh3sMC9t2Ww+MWmcbX/RC8/Z9v9Y+4o5KhJEXxd3WijX7u08aNZJsJ436Rlb/m1xHU2q3QcPmnnb
        3jafe2lS4Zqe6vzYxrVvsAG47cABG2il3+sTZLW4mnLZtMnmG7NeMhqur8r9KJog5e4r9hzUfZ9t32jb
        qubI2LQs/G7Vh5950ibHw2+sM+v37ilaMEoT5JeyqLwliat93b9uta3zQf4teQ5Ld3Q9B63/rDzrsI9w
        bGG/5e4rVYoWJqihC+fYhxiuaLq6qgPoJOln/6APSpluT26YP8ucMHqEuWnRXPP5aZPMV2XytuzfbydG
        2x8/+jGzQYJA2d5xwIyW7dUXp0+xdedLMmg/Gqg/mDfTOswnpaxt8rM2gGZt3WKunTvTBq0GgbpRuTo/
        trcP7DdfmTHVPCBbReWauTMK96PyCaJjmf72W+bR9a+Z8Zs2WulCoGiClLuv2HPw26acvOo93rFiSTRB
        fuye89ekz7+9utK+/+jEMUX9aiB/asp4+16DWfvW7amiCTLQPQcd+5flXvU+9L5PkuCPjS3st9x9pUrR
        wgRVSYJocPk2x0kS/Oe1NXa7oquSTpyu3r5eV8zbVyw2L0tQd20qjLlw6gSbEMrVsnr7tqpbli605X6l
        1MlU9CxTrs6P7aHX19q60yc8bT8/KGPzfavCBLlu3svmgJyNdMw6jjBByt1X7DlooCqLd2wz961dbfs7
        wtWFmiYrtp7HNCm+O2e6vWb48kW2LgxkdQ/lKncGvE2CXdF7989Bk0Dln6vuAmJjC/s93HylRtHCBBXb
        YunZQyndYumD9m1+tnieLdPEOmPCaLNy1w6zTwJA69739GN2f+/b3rxkvm2ryVirBHlMVlWtO2fiWPv5
        32USRINFg0QXgGNknGGClLuv2HNQ6XbvD6uWmdmyGCilP3B8RJzOB3OIbhm1PuzXPx/dhmpdLEH0OZ77
        /Firjz0/zrpDbGxhWbn7SpWihQnLH9Kf3rje/F4Odf7g6g/psYfvJ0sf/JdmTLErkn/guk3YKFsDPYPc
        umyRXV0V7c9vsXSb9CPZBuiE6RbKbx80yH4oK/waqde9sm6jytX5se2RLddPZSzPyFlE+d6cnrdY+lnP
        Hz7hwgQpd1+x56BnGf3ey6dPtlsbZfCCWYV6ldYr+gOIfqdKn7WiAR7267dYeq+6bXszssXSOnUufX56
        DtGEj40tLCt3X6lStDBh+Z95dUWN/cwbe/gfGveE3QPvkJVYf/1avvPQiqTbq0fkMPq62Ln2p7auWwd/
        bblDuvajwa4Hcd0v+2t6qvNj0zFooOu24d413X8cKE2QUGGClLuv2HPQcSyRQ7Pei563RoiT6RnM16tm
        Sn/qIPqrki/TBFX0zFLarz4rPSPoPPxXto6KOr3WfVOu0wVHv0/Pefqc3y0uGBtbWFbuvlKlaCGqWrHA
        aHTp4VydSRcS/TFDF64Tx4yItm06RQtR1WrGBBm54XW7hdTVXn+6vmDqhGi7plS0ECHUpWghQqhL0UKE
        kOhh838+5iulnMni5wAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="errorProvider1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>