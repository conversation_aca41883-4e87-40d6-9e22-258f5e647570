﻿namespace IRepairIT.FRMS.FMAIN
{
    partial class FRM_MAIN
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FRM_MAIN));
            this.miniToolStrip = new Krypton.Toolkit.KryptonToolStrip();
            this.kryptonPanel1 = new Krypton.Toolkit.KryptonPanel();
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.tableauDeBordToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.clientsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.nouveauClientWizardToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.appareilsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ordresDeRéparationToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.inventaireToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.servicesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dettesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rapportsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rapportDesRéparationsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rapportDesDettesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rapportDesVentesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rapportDesRevenusToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rapportDesDépensesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rapportDesBénéficesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rapportDesPiècesDétachéesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rapportDesClientsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.utilisateursToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.paramètresToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.logToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.lblUserInfo = new System.Windows.Forms.ToolStripStatusLabel();
            this.lblSeparator1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.lblDateTime = new System.Windows.Forms.ToolStripStatusLabel();
            this.lblSeparator2 = new System.Windows.Forms.ToolStripStatusLabel();
            this.lblDatabaseInfo = new System.Windows.Forms.ToolStripStatusLabel();
            this.timerDateTime = new System.Windows.Forms.Timer(this.components);
            this.kryptonNavigator1 = new Krypton.Navigator.KryptonNavigator();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).BeginInit();
            this.kryptonPanel1.SuspendLayout();
            this.menuStrip1.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonNavigator1)).BeginInit();
            this.kryptonNavigator1.SuspendLayout();
            this.SuspendLayout();
            //
            // miniToolStrip
            //
            this.miniToolStrip.AccessibleName = "New item selection";
            this.miniToolStrip.AccessibleRole = System.Windows.Forms.AccessibleRole.ButtonDropDown;
            this.miniToolStrip.AutoSize = false;
            this.miniToolStrip.CanOverflow = false;
            this.miniToolStrip.Dock = System.Windows.Forms.DockStyle.None;
            this.miniToolStrip.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.miniToolStrip.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            this.miniToolStrip.Location = new System.Drawing.Point(9, 3);
            this.miniToolStrip.Name = "miniToolStrip";
            this.miniToolStrip.Size = new System.Drawing.Size(910, 25);
            this.miniToolStrip.TabIndex = 0;
            //
            // kryptonPanel1
            //
            this.kryptonPanel1.Controls.Add(this.kryptonNavigator1);
            this.kryptonPanel1.Controls.Add(this.menuStrip1);
            this.kryptonPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel1.Location = new System.Drawing.Point(0, 0);
            this.kryptonPanel1.Name = "kryptonPanel1";
            this.kryptonPanel1.Size = new System.Drawing.Size(994, 639);
            this.kryptonPanel1.TabIndex = 1;
            //
            // menuStrip1
            //
            this.menuStrip1.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tableauDeBordToolStripMenuItem,
            this.clientsToolStripMenuItem,
            this.appareilsToolStripMenuItem,
            this.ordresDeRéparationToolStripMenuItem,
            this.inventaireToolStripMenuItem,
            this.servicesToolStripMenuItem,
            this.dettesToolStripMenuItem,
            this.rapportsToolStripMenuItem,
            this.utilisateursToolStripMenuItem,
            this.paramètresToolStripMenuItem,
            this.logToolStripMenuItem});
            this.menuStrip1.Location = new System.Drawing.Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Size = new System.Drawing.Size(994, 24);
            this.menuStrip1.TabIndex = 0;
            this.menuStrip1.Text = "menuStrip1";
            //
            // tableauDeBordToolStripMenuItem
            //
            this.tableauDeBordToolStripMenuItem.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.tableauDeBordToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tableauDeBordToolStripMenuItem.Name = "tableauDeBordToolStripMenuItem";
            this.tableauDeBordToolStripMenuItem.Size = new System.Drawing.Size(103, 20);
            this.tableauDeBordToolStripMenuItem.Text = "&Tableau de bord";
            this.tableauDeBordToolStripMenuItem.Click += new System.EventHandler(this.tableauDeBordToolStripMenuItem_Click);
            //
            // clientsToolStripMenuItem
            //
            this.clientsToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.nouveauClientWizardToolStripMenuItem});
            this.clientsToolStripMenuItem.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.clientsToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.clientsToolStripMenuItem.Name = "clientsToolStripMenuItem";
            this.clientsToolStripMenuItem.Size = new System.Drawing.Size(55, 20);
            this.clientsToolStripMenuItem.Text = "&Clients";
            this.clientsToolStripMenuItem.Click += new System.EventHandler(this.clientsToolStripMenuItem_Click);
            //
            // nouveauClientWizardToolStripMenuItem
            //
            this.nouveauClientWizardToolStripMenuItem.Name = "nouveauClientWizardToolStripMenuItem";
            this.nouveauClientWizardToolStripMenuItem.Size = new System.Drawing.Size(250, 22);
            this.nouveauClientWizardToolStripMenuItem.Text = "Nouveau client (Assistant)";
            this.nouveauClientWizardToolStripMenuItem.Click += new System.EventHandler(this.nouveauClientWizardToolStripMenuItem_Click);
            //
            // appareilsToolStripMenuItem
            //
            this.appareilsToolStripMenuItem.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.appareilsToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.appareilsToolStripMenuItem.Name = "appareilsToolStripMenuItem";
            this.appareilsToolStripMenuItem.Size = new System.Drawing.Size(68, 20);
            this.appareilsToolStripMenuItem.Text = "&Appareils";
            this.appareilsToolStripMenuItem.Click += new System.EventHandler(this.appareilsToolStripMenuItem_Click);
            //
            // ordresDeRéparationToolStripMenuItem
            //
            this.ordresDeRéparationToolStripMenuItem.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.ordresDeRéparationToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.ordresDeRéparationToolStripMenuItem.Name = "ordresDeRéparationToolStripMenuItem";
            this.ordresDeRéparationToolStripMenuItem.Size = new System.Drawing.Size(127, 20);
            this.ordresDeRéparationToolStripMenuItem.Text = "&Ordres de réparation";
            this.ordresDeRéparationToolStripMenuItem.Click += new System.EventHandler(this.ordresDeRéparationToolStripMenuItem_Click);
            //
            // inventaireToolStripMenuItem
            //
            this.inventaireToolStripMenuItem.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.inventaireToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.inventaireToolStripMenuItem.Name = "inventaireToolStripMenuItem";
            this.inventaireToolStripMenuItem.Size = new System.Drawing.Size(71, 20);
            this.inventaireToolStripMenuItem.Text = "&Inventaire";
            this.inventaireToolStripMenuItem.Click += new System.EventHandler(this.inventaireToolStripMenuItem_Click);
            //
            // servicesToolStripMenuItem
            //
            this.servicesToolStripMenuItem.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.servicesToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.servicesToolStripMenuItem.Name = "servicesToolStripMenuItem";
            this.servicesToolStripMenuItem.Size = new System.Drawing.Size(61, 20);
            this.servicesToolStripMenuItem.Text = "&Services";
            this.servicesToolStripMenuItem.Click += new System.EventHandler(this.servicesToolStripMenuItem_Click);
            //
            // dettesToolStripMenuItem
            //
            this.dettesToolStripMenuItem.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.dettesToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.dettesToolStripMenuItem.Name = "dettesToolStripMenuItem";
            this.dettesToolStripMenuItem.Size = new System.Drawing.Size(52, 20);
            this.dettesToolStripMenuItem.Text = "&Dettes";
            this.dettesToolStripMenuItem.Click += new System.EventHandler(this.dettesToolStripMenuItem_Click);
            //
            // rapportsToolStripMenuItem
            //
            this.rapportsToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.rapportDesRéparationsToolStripMenuItem,
            this.rapportDesDettesToolStripMenuItem,
            this.rapportDesVentesToolStripMenuItem,
            this.rapportDesRevenusToolStripMenuItem,
            this.rapportDesDépensesToolStripMenuItem,
            this.rapportDesBénéficesToolStripMenuItem,
            this.rapportDesPiècesDétachéesToolStripMenuItem,
            this.rapportDesClientsToolStripMenuItem});
            this.rapportsToolStripMenuItem.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.rapportsToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.rapportsToolStripMenuItem.Name = "rapportsToolStripMenuItem";
            this.rapportsToolStripMenuItem.Size = new System.Drawing.Size(66, 20);
            this.rapportsToolStripMenuItem.Text = "&Rapports";
            //
            // rapportDesRéparationsToolStripMenuItem
            //
            this.rapportDesRéparationsToolStripMenuItem.Name = "rapportDesRéparationsToolStripMenuItem";
            this.rapportDesRéparationsToolStripMenuItem.Size = new System.Drawing.Size(229, 22);
            this.rapportDesRéparationsToolStripMenuItem.Text = "Rapport des réparations";
            this.rapportDesRéparationsToolStripMenuItem.Click += new System.EventHandler(this.rapportDesRéparationsToolStripMenuItem_Click);
            //
            // rapportDesDettesToolStripMenuItem
            //
            this.rapportDesDettesToolStripMenuItem.Name = "rapportDesDettesToolStripMenuItem";
            this.rapportDesDettesToolStripMenuItem.Size = new System.Drawing.Size(229, 22);
            this.rapportDesDettesToolStripMenuItem.Text = "Rapport des dettes";
            this.rapportDesDettesToolStripMenuItem.Click += new System.EventHandler(this.rapportDesDettesToolStripMenuItem_Click);
            //
            // rapportDesVentesToolStripMenuItem
            //
            this.rapportDesVentesToolStripMenuItem.Name = "rapportDesVentesToolStripMenuItem";
            this.rapportDesVentesToolStripMenuItem.Size = new System.Drawing.Size(229, 22);
            this.rapportDesVentesToolStripMenuItem.Text = "Rapport des ventes";
            this.rapportDesVentesToolStripMenuItem.Click += new System.EventHandler(this.rapportDesVentesToolStripMenuItem_Click);
            //
            // rapportDesRevenusToolStripMenuItem
            //
            this.rapportDesRevenusToolStripMenuItem.Name = "rapportDesRevenusToolStripMenuItem";
            this.rapportDesRevenusToolStripMenuItem.Size = new System.Drawing.Size(229, 22);
            this.rapportDesRevenusToolStripMenuItem.Text = "Rapport des revenus";
            //
            // rapportDesDépensesToolStripMenuItem
            //
            this.rapportDesDépensesToolStripMenuItem.Name = "rapportDesDépensesToolStripMenuItem";
            this.rapportDesDépensesToolStripMenuItem.Size = new System.Drawing.Size(229, 22);
            this.rapportDesDépensesToolStripMenuItem.Text = "Rapport des dépenses";
            //
            // rapportDesBénéficesToolStripMenuItem
            //
            this.rapportDesBénéficesToolStripMenuItem.Name = "rapportDesBénéficesToolStripMenuItem";
            this.rapportDesBénéficesToolStripMenuItem.Size = new System.Drawing.Size(229, 22);
            this.rapportDesBénéficesToolStripMenuItem.Text = "Rapport des bénéfices";
            //
            // rapportDesPiècesDétachéesToolStripMenuItem
            //
            this.rapportDesPiècesDétachéesToolStripMenuItem.Name = "rapportDesPiècesDétachéesToolStripMenuItem";
            this.rapportDesPiècesDétachéesToolStripMenuItem.Size = new System.Drawing.Size(229, 22);
            this.rapportDesPiècesDétachéesToolStripMenuItem.Text = "Rapport des pièces détachées";
            //
            // rapportDesClientsToolStripMenuItem
            //
            this.rapportDesClientsToolStripMenuItem.Name = "rapportDesClientsToolStripMenuItem";
            this.rapportDesClientsToolStripMenuItem.Size = new System.Drawing.Size(229, 22);
            this.rapportDesClientsToolStripMenuItem.Text = "Rapport des clients";
            //
            // utilisateursToolStripMenuItem
            //
            this.utilisateursToolStripMenuItem.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.utilisateursToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.utilisateursToolStripMenuItem.Name = "utilisateursToolStripMenuItem";
            this.utilisateursToolStripMenuItem.Size = new System.Drawing.Size(77, 20);
            this.utilisateursToolStripMenuItem.Text = "&Utilisateurs";
            this.utilisateursToolStripMenuItem.Click += new System.EventHandler(this.utilisateursToolStripMenuItem_Click);
            //
            // paramètresToolStripMenuItem
            //
            this.paramètresToolStripMenuItem.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.paramètresToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.paramètresToolStripMenuItem.Name = "paramètresToolStripMenuItem";
            this.paramètresToolStripMenuItem.Size = new System.Drawing.Size(78, 20);
            this.paramètresToolStripMenuItem.Text = "&Paramètres";
            this.paramètresToolStripMenuItem.Click += new System.EventHandler(this.paramètresToolStripMenuItem_Click);
            //
            // logToolStripMenuItem
            //
            this.logToolStripMenuItem.Name = "logToolStripMenuItem";
            this.logToolStripMenuItem.Size = new System.Drawing.Size(39, 20);
            this.logToolStripMenuItem.Text = "Log";
            this.logToolStripMenuItem.Click += new System.EventHandler(this.logToolStripMenuItem_Click);
            //
            // statusStrip1
            //
            this.statusStrip1.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.lblUserInfo,
            this.lblSeparator1,
            this.lblDateTime,
            this.lblSeparator2,
            this.lblDatabaseInfo});
            this.statusStrip1.Location = new System.Drawing.Point(0, 639);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new System.Drawing.Size(994, 22);
            this.statusStrip1.TabIndex = 1;
            this.statusStrip1.Text = "statusStrip1";
            //
            // lblUserInfo
            //
            this.lblUserInfo.Name = "lblUserInfo";
            this.lblUserInfo.Size = new System.Drawing.Size(60, 17);
            this.lblUserInfo.Text = "Utilisateur";
            //
            // lblSeparator1
            //
            this.lblSeparator1.Name = "lblSeparator1";
            this.lblSeparator1.Size = new System.Drawing.Size(10, 17);
            this.lblSeparator1.Text = "|";
            //
            // lblDateTime
            //
            this.lblDateTime.Name = "lblDateTime";
            this.lblDateTime.Size = new System.Drawing.Size(77, 17);
            this.lblDateTime.Text = "Date et heure";
            //
            // lblSeparator2
            //
            this.lblSeparator2.Name = "lblSeparator2";
            this.lblSeparator2.Size = new System.Drawing.Size(10, 17);
            this.lblSeparator2.Text = "|";
            //
            // lblDatabaseInfo
            //
            this.lblDatabaseInfo.Name = "lblDatabaseInfo";
            this.lblDatabaseInfo.Size = new System.Drawing.Size(95, 17);
            this.lblDatabaseInfo.Text = "Base de données";
            //
            // timerDateTime
            //
            this.timerDateTime.Interval = 1000;
            //
            // kryptonNavigator1
            //
            this.kryptonNavigator1.AllowTabFocus = false;
            this.kryptonNavigator1.Bar.BarMapExtraText = Krypton.Navigator.MapKryptonPageText.None;
            this.kryptonNavigator1.Bar.BarMapImage = Krypton.Navigator.MapKryptonPageImage.Small;
            this.kryptonNavigator1.Bar.BarMapText = Krypton.Navigator.MapKryptonPageText.TextTitle;
            this.kryptonNavigator1.Bar.BarMultiline = Krypton.Navigator.BarMultiline.Singleline;
            this.kryptonNavigator1.Bar.BarOrientation = Krypton.Toolkit.VisualOrientation.Top;
            this.kryptonNavigator1.Bar.CheckButtonStyle = Krypton.Toolkit.ButtonStyle.Standalone;
            this.kryptonNavigator1.Bar.ItemAlignment = Krypton.Toolkit.RelativePositionAlign.Near;
            this.kryptonNavigator1.Bar.ItemMaximumSize = new System.Drawing.Size(200, 200);
            this.kryptonNavigator1.Bar.ItemMinimumSize = new System.Drawing.Size(20, 20);
            this.kryptonNavigator1.Bar.ItemOrientation = Krypton.Toolkit.ButtonOrientation.Auto;
            this.kryptonNavigator1.Bar.ItemSizing = Krypton.Navigator.BarItemSizing.SameWidthAndHeight;
            this.kryptonNavigator1.Bar.TabBorderStyle = Krypton.Toolkit.TabBorderStyle.SlantEqualFar;
            this.kryptonNavigator1.Bar.TabStyle = Krypton.Toolkit.TabStyle.Dock;
            this.kryptonNavigator1.Button.ButtonDisplayLogic = Krypton.Navigator.ButtonDisplayLogic.None;
            this.kryptonNavigator1.Button.CloseButtonAction = Krypton.Navigator.CloseButtonAction.RemovePageAndDispose;
            this.kryptonNavigator1.Button.CloseButtonDisplay = Krypton.Navigator.ButtonDisplay.Logic;
            this.kryptonNavigator1.Button.ContextButtonAction = Krypton.Navigator.ContextButtonAction.SelectPage;
            this.kryptonNavigator1.Button.ContextButtonDisplay = Krypton.Navigator.ButtonDisplay.Logic;
            this.kryptonNavigator1.Button.ContextMenuMapImage = Krypton.Navigator.MapKryptonPageImage.Small;
            this.kryptonNavigator1.Button.ContextMenuMapText = Krypton.Navigator.MapKryptonPageText.TextTitle;
            this.kryptonNavigator1.Button.NextButtonAction = Krypton.Navigator.DirectionButtonAction.ModeAppropriateAction;
            this.kryptonNavigator1.Button.NextButtonDisplay = Krypton.Navigator.ButtonDisplay.Logic;
            this.kryptonNavigator1.Button.PreviousButtonAction = Krypton.Navigator.DirectionButtonAction.ModeAppropriateAction;
            this.kryptonNavigator1.Button.PreviousButtonDisplay = Krypton.Navigator.ButtonDisplay.Logic;
            this.kryptonNavigator1.ControlKryptonFormFeatures = false;
            this.kryptonNavigator1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonNavigator1.Location = new System.Drawing.Point(0, 24);
            this.kryptonNavigator1.Name = "kryptonNavigator1";
            this.kryptonNavigator1.NavigatorMode = Krypton.Navigator.NavigatorMode.BarTabGroup;
            this.kryptonNavigator1.Owner = null;
            this.kryptonNavigator1.PageBackStyle = Krypton.Toolkit.PaletteBackStyle.ControlClient;
            this.kryptonNavigator1.Size = new System.Drawing.Size(994, 615);
            this.kryptonNavigator1.TabIndex = 8;
            this.kryptonNavigator1.Text = "kryptonNavigator1";
            //
            // FRM_MAIN
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(994, 661);
            this.Controls.Add(this.kryptonPanel1);
            this.Controls.Add(this.statusStrip1);
            this.HeaderStyle = Krypton.Toolkit.HeaderStyle.Calendar;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "FRM_MAIN";
            this.Text = " IRepairIT - Système de gestion des ateliers de réparation";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FRM_MAIN_FormClosing);
            this.Load += new System.EventHandler(this.FRM_MAIN_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).EndInit();
            this.kryptonPanel1.ResumeLayout(false);
            this.kryptonPanel1.PerformLayout();
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonNavigator1)).EndInit();
            this.kryptonNavigator1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Krypton.Toolkit.KryptonToolStrip miniToolStrip;
        private Krypton.Toolkit.KryptonPanel kryptonPanel1;
        private System.Windows.Forms.MenuStrip menuStrip1;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripStatusLabel lblUserInfo;
        private System.Windows.Forms.ToolStripStatusLabel lblSeparator1;
        private System.Windows.Forms.ToolStripStatusLabel lblDateTime;
        private System.Windows.Forms.ToolStripStatusLabel lblSeparator2;
        private System.Windows.Forms.ToolStripStatusLabel lblDatabaseInfo;
        private System.Windows.Forms.ToolStripMenuItem tableauDeBordToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem clientsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem appareilsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ordresDeRéparationToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem inventaireToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem servicesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dettesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rapportsToolStripMenuItem;
        private System.Windows.Forms.Timer timerDateTime;
        private System.Windows.Forms.ToolStripMenuItem utilisateursToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem paramètresToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem logToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rapportDesRéparationsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rapportDesDettesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rapportDesVentesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rapportDesRevenusToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rapportDesDépensesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rapportDesBénéficesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rapportDesPiècesDétachéesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rapportDesClientsToolStripMenuItem;
        private Krypton.Navigator.KryptonNavigator kryptonNavigator1;
        private System.Windows.Forms.ToolStripMenuItem nouveauClientWizardToolStripMenuItem;
    }
}