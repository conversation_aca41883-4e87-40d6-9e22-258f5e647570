﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="IRepairIT.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="CurrentConnectionString" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="CurrentDatabase" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ShowDatabaseSelector" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="DefaultDatabase" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="StartingWindow" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="CurrentUserId" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
  </Settings>
</SettingsFile>