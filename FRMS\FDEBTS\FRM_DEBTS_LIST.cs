﻿﻿using IRepairIT.Data.Common;
using IRepairIT.Enums;
using IRepairIT.Models;
using Krypton.Toolkit;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FDEBTS
{
    public partial class FRM_DEBTS_LIST : KryptonForm
    {
        private readonly DebtCommands _cmd;
        private readonly CustomerCommands _customerCmd;
        private readonly RepairOrderCommands _repairOrderCmd;
        private int _currentPage = 1;
        private int _pageSize = 20;
        private int _totalPages = 0;
        private int _totalRecords = 0;
        private string _searchTerm = "";

        public FRM_DEBTS_LIST()
        {
            InitializeComponent();
            _cmd = new DebtCommands();
            _customerCmd = new CustomerCommands();
            _repairOrderCmd = new RepairOrderCommands();
        }

        private async void FRM_DEBTS_LIST_Load(object sender, EventArgs e)
        {
            try
            {
                // Configure DataGridView
                ConfigureDataGridView();

                // Load debts
                await LoadDebts();

                // Set up context menu
                SetupContextMenu();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des dettes: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void ConfigureDataGridView()
        {
            // Set up columns for debts grid
            kryptonDataGridView1.AutoGenerateColumns = false;
            kryptonDataGridView1.AllowUserToAddRows = false;
            kryptonDataGridView1.AllowUserToDeleteRows = false;
            kryptonDataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            kryptonDataGridView1.MultiSelect = false;
            kryptonDataGridView1.RowHeadersVisible = false;
            kryptonDataGridView1.AlternatingRowsDefaultCellStyle.BackColor = Color.AliceBlue;
            kryptonDataGridView1.DefaultCellStyle.SelectionBackColor = Color.LightBlue;
            kryptonDataGridView1.DefaultCellStyle.SelectionForeColor = Color.Black;
            kryptonDataGridView1.EnableHeadersVisualStyles = false;
            kryptonDataGridView1.ColumnHeadersDefaultCellStyle.BackColor = Color.LightBlue;
            kryptonDataGridView1.ColumnHeadersDefaultCellStyle.ForeColor = Color.Black;
            kryptonDataGridView1.ColumnHeadersDefaultCellStyle.Font = new Font(kryptonDataGridView1.Font, FontStyle.Bold);
            kryptonDataGridView1.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;

            // Clear existing columns
            kryptonDataGridView1.Columns.Clear();

            // Add columns
            kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "id",
                DataPropertyName = "Id",
                HeaderText = "ID",
                Width = 50,
                ReadOnly = true
            });

            kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerName",
                DataPropertyName = "CustomerName",
                HeaderText = "Client",
                Width = 150,
                ReadOnly = true
            });

            kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "OrderNumber",
                DataPropertyName = "OrderNumber",
                HeaderText = "N° Commande",
                Width = 100,
                ReadOnly = true
            });

            kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Amount",
                DataPropertyName = "Amount",
                HeaderText = "Montant",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "C2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                },
                ReadOnly = true
            });

            kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PaidAmount",
                DataPropertyName = "PaidAmount",
                HeaderText = "Payé",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "C2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                },
                ReadOnly = true
            });

            kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RemainingAmount",
                DataPropertyName = "RemainingAmount",
                HeaderText = "Reste",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "C2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                },
                ReadOnly = true
            });

            kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DebtDate",
                DataPropertyName = "DebtDate",
                HeaderText = "Date",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "dd/MM/yyyy"
                },
                ReadOnly = true
            });

            kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DueDate",
                DataPropertyName = "DueDate",
                HeaderText = "Échéance",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "dd/MM/yyyy"
                },
                ReadOnly = true
            });

            kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                DataPropertyName = "Status",
                HeaderText = "Statut",
                Width = 100,
                ReadOnly = true
            });

            kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Notes",
                DataPropertyName = "Notes",
                HeaderText = "Notes",
                Width = 200,
                ReadOnly = true
            });

            // Add event handlers
            kryptonDataGridView1.CellFormatting += KryptonDataGridView1_CellFormatting;
            kryptonDataGridView1.CellDoubleClick += KryptonDataGridView1_CellDoubleClick;
        }

        private void KryptonDataGridView1_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex >= 0 && kryptonDataGridView1.Columns[e.ColumnIndex].Name == "Status" && e.Value != null)
            {
                // Format status column
                string status = e.Value.ToString();
                if (Enum.TryParse(status, out PaymentStatus statusEnum))
                {
                    // Set display name
                    e.Value = GetPaymentStatusDisplayName(statusEnum);

                    // Set cell color based on status
                    switch (statusEnum)
                    {
                        case PaymentStatus.unpaid:
                            e.CellStyle.BackColor = Color.LightCoral;
                            break;
                        case PaymentStatus.partially_paid:
                            e.CellStyle.BackColor = Color.LightYellow;
                            break;
                        case PaymentStatus.paid:
                            e.CellStyle.BackColor = Color.LightGreen;
                            break;
                    }
                }
            }

            // Highlight overdue debts
            if (e.ColumnIndex >= 0 && kryptonDataGridView1.Columns[e.ColumnIndex].Name == "DueDate" && e.Value != null)
            {
                if (e.Value is DateTime dueDate)
                {
                    if (dueDate < DateTime.Today)
                    {
                        e.CellStyle.BackColor = Color.LightCoral;
                        e.CellStyle.ForeColor = Color.Black;
                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                    }
                }
            }
        }

        private void KryptonDataGridView1_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                // Get the debt ID
                int debtId = Convert.ToInt32(kryptonDataGridView1.Rows[e.RowIndex].Cells["id"].Value);

                // Open debt details form
                OpenDebtDetails(debtId);
            }
        }

        private void OpenDebtDetails(int debtId)
        {
            try
            {
                // Open debt details form
                var frm = new FRM_DEBT(debtId);
                if (frm.ShowDialog() == DialogResult.OK)
                {
                    // Reload debts if changes were made
                    LoadDebts().ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'ouverture des détails de la dette: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void SetupContextMenu()
        {
            try
            {
                // Create context menu
                KryptonContextMenu contextMenu = new KryptonContextMenu();

                // Create menu items
                KryptonContextMenuItems items = new KryptonContextMenuItems();

                // View details item
                KryptonContextMenuItem viewItem = new KryptonContextMenuItem("Voir les détails");
                viewItem.Image = Properties.Resources.eye;
                viewItem.Click += (s, e) => ViewDebtDetails();

                // Make payment item
                KryptonContextMenuItem paymentItem = new KryptonContextMenuItem("Effectuer un paiement");
                paymentItem.Image = Properties.Resources.give_money_2;
                paymentItem.Click += (s, e) => MakePayment();

                // Edit item
                KryptonContextMenuItem editItem = new KryptonContextMenuItem("Modifier");
                editItem.Image = Properties.Resources.tool_pencil;
                editItem.Click += (s, e) => EditDebt();

                // Delete item
                KryptonContextMenuItem deleteItem = new KryptonContextMenuItem("Supprimer");
                deleteItem.Image = Properties.Resources.trash;
                deleteItem.Click += (s, e) => DeleteDebt();

                // Add items to menu
                items.Items.Add(viewItem);
                items.Items.Add(paymentItem);
                items.Items.Add(editItem);
                items.Items.Add(deleteItem);

                // Add items to context menu
                contextMenu.Items.Add(items);

                // Set context menu
                kryptonDataGridView1.ContextMenuStrip = new ContextMenuStrip();
                kryptonDataGridView1.ContextMenuStrip.Opening += (s, e) =>
                {
                    if (kryptonDataGridView1.CurrentRow == null)
                    {
                        e.Cancel = true;
                    }
                    else
                    {
                        contextMenu.Show(kryptonDataGridView1.PointToScreen(kryptonDataGridView1.ContextMenuStrip.Location));
                    }
                };
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la configuration du menu contextuel: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void ViewDebtDetails()
        {
            if (kryptonDataGridView1.CurrentRow != null)
            {
                int debtId = Convert.ToInt32(kryptonDataGridView1.CurrentRow.Cells["id"].Value);
                OpenDebtDetails(debtId);
            }
        }

        private async void MakePayment()
        {
            if (kryptonDataGridView1.CurrentRow != null)
            {
                try
                {
                    int debtId = Convert.ToInt32(kryptonDataGridView1.CurrentRow.Cells["id"].Value);
                    decimal amount = Convert.ToDecimal(kryptonDataGridView1.CurrentRow.Cells["Amount"].Value);
                    decimal paidAmount = Convert.ToDecimal(kryptonDataGridView1.CurrentRow.Cells["PaidAmount"].Value);
                    decimal remainingAmount = amount - paidAmount;

                    if (remainingAmount <= 0)
                    {
                        KryptonMessageBox.Show("Cette dette est déjà entièrement payée.", "Information",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                        return;
                    }

                    // Show payment form
                    using (var paymentForm = new KryptonForm())
                    {
                        paymentForm.Text = "Effectuer un paiement";
                        paymentForm.Size = new Size(400, 250);
                        paymentForm.StartPosition = FormStartPosition.CenterParent;
                        paymentForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                        paymentForm.MaximizeBox = false;
                        paymentForm.MinimizeBox = false;

                        var amountLabel = new KryptonLabel
                        {
                            Text = "Montant à payer:",
                            Location = new Point(20, 20),
                            Size = new Size(150, 20)
                        };

                        var amountTextBox = new KryptonTextBox
                        {
                            Text = remainingAmount.ToString("F2"),
                            Location = new Point(180, 20),
                            Size = new Size(180, 25)
                        };

                        var methodLabel = new KryptonLabel
                        {
                            Text = "Méthode de paiement:",
                            Location = new Point(20, 60),
                            Size = new Size(150, 20)
                        };

                        var methodComboBox = new KryptonComboBox
                        {
                            Location = new Point(180, 60),
                            Size = new Size(180, 25),
                            DropDownWidth = 180
                        };
                        methodComboBox.Items.AddRange(new object[] { "Espèces", "Carte bancaire", "Chèque", "Virement" });
                        methodComboBox.SelectedIndex = 0;

                        var notesLabel = new KryptonLabel
                        {
                            Text = "Notes:",
                            Location = new Point(20, 100),
                            Size = new Size(150, 20)
                        };

                        var notesTextBox = new KryptonTextBox
                        {
                            Location = new Point(180, 100),
                            Size = new Size(180, 25),
                            Multiline = true,
                            Height = 60
                        };

                        var okButton = new KryptonButton
                        {
                            Text = "OK",
                            Location = new Point(180, 180),
                            Size = new Size(80, 30),
                            DialogResult = DialogResult.OK
                        };

                        var cancelButton = new KryptonButton
                        {
                            Text = "Annuler",
                            Location = new Point(280, 180),
                            Size = new Size(80, 30),
                            DialogResult = DialogResult.Cancel
                        };

                        paymentForm.Controls.Add(amountLabel);
                        paymentForm.Controls.Add(amountTextBox);
                        paymentForm.Controls.Add(methodLabel);
                        paymentForm.Controls.Add(methodComboBox);
                        paymentForm.Controls.Add(notesLabel);
                        paymentForm.Controls.Add(notesTextBox);
                        paymentForm.Controls.Add(okButton);
                        paymentForm.Controls.Add(cancelButton);
                        paymentForm.AcceptButton = okButton;
                        paymentForm.CancelButton = cancelButton;

                        if (paymentForm.ShowDialog() == DialogResult.OK)
                        {
                            // Get payment amount
                            if (!decimal.TryParse(amountTextBox.Text, out decimal paymentAmount))
                            {
                                KryptonMessageBox.Show("Veuillez entrer un montant valide.", "Erreur",
                                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                                return;
                            }

                            if (paymentAmount <= 0 || paymentAmount > remainingAmount)
                            {
                                KryptonMessageBox.Show($"Veuillez entrer un montant entre 0 et {remainingAmount:C}.", "Erreur",
                                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                                return;
                            }

                            // Get payment method
                            string paymentMethod = methodComboBox.SelectedItem.ToString();

                            // Get notes
                            string notes = notesTextBox.Text;

                            // Update debt
                            bool success = await _cmd.UpdatePaymentAsync(debtId, paymentAmount);
                            if (success)
                            {
                                KryptonMessageBox.Show($"Paiement de {paymentAmount:C} effectué avec succès.", "Succès",
                                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                                // Reload debts
                                await LoadDebts();
                            }
                            else
                            {
                                KryptonMessageBox.Show("Erreur lors de l'enregistrement du paiement.", "Erreur",
                                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    KryptonMessageBox.Show($"Erreur lors du paiement: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
            }
        }

        private void EditDebt()
        {
            if (kryptonDataGridView1.CurrentRow != null)
            {
                int debtId = Convert.ToInt32(kryptonDataGridView1.CurrentRow.Cells["id"].Value);
                OpenDebtDetails(debtId);
            }
        }

        private async void DeleteDebt()
        {
            if (kryptonDataGridView1.CurrentRow != null)
            {
                try
                {
                    int debtId = Convert.ToInt32(kryptonDataGridView1.CurrentRow.Cells["id"].Value);
                    string customerName = kryptonDataGridView1.CurrentRow.Cells["CustomerName"].Value.ToString();

                    // Ask for confirmation
                    var result = KryptonMessageBox.Show(
                        $"Êtes-vous sûr de vouloir supprimer la dette pour {customerName}?",
                        "Confirmation",
                        KryptonMessageBoxButtons.YesNo,
                        KryptonMessageBoxIcon.Question
                    );

                    if (result == DialogResult.Yes)
                    {
                        // Delete debt
                        bool success = await _cmd.DeleteAsync(debtId);
                        if (success)
                        {
                            KryptonMessageBox.Show("Dette supprimée avec succès.", "Succès",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);

                            // Reload debts
                            await LoadDebts();
                        }
                        else
                        {
                            KryptonMessageBox.Show("Erreur lors de la suppression de la dette.", "Erreur",
                                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                        }
                    }
                }
                catch (Exception ex)
                {
                    KryptonMessageBox.Show($"Erreur lors de la suppression de la dette: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
            }
        }

        private async Task LoadDebts()
        {
            try
            {
                // Show loading indicator
                Cursor = Cursors.WaitCursor;

                // Get total count
                _totalRecords = await _cmd.GetCount(_searchTerm);
                _totalPages = (_totalRecords + _pageSize - 1) / _pageSize;

                // Adjust current page if needed
                if (_currentPage > _totalPages && _totalPages > 0)
                {
                    _currentPage = _totalPages;
                }
                else if (_currentPage < 1)
                {
                    _currentPage = 1;
                }

                // Calculate offset
                int offset = (_currentPage - 1) * _pageSize;

                // Get debts
                var debts = await _cmd.GetAll(_searchTerm, offset, _pageSize);

                // Create DataTable
                DataTable dt = new DataTable();
                dt.Columns.Add("Id", typeof(int));
                dt.Columns.Add("CustomerId", typeof(int));
                dt.Columns.Add("RepairOrderId", typeof(int));
                dt.Columns.Add("CustomerName", typeof(string));
                dt.Columns.Add("OrderNumber", typeof(string));
                dt.Columns.Add("Amount", typeof(decimal));
                dt.Columns.Add("PaidAmount", typeof(decimal));
                dt.Columns.Add("RemainingAmount", typeof(decimal));
                dt.Columns.Add("DebtDate", typeof(DateTime));
                dt.Columns.Add("DueDate", typeof(DateTime));
                dt.Columns.Add("Status", typeof(string));
                dt.Columns.Add("Notes", typeof(string));

                // Add data
                foreach (var debt in debts)
                {
                    // Get customer name from the database if not available
                    string customerName = "";
                    if (debt.Customer != null)
                    {
                        customerName = debt.Customer.name;
                    }

                    // Get order number from the database if not available
                    string orderNumber = "";
                    if (debt.RepairOrderId.HasValue && debt.RepairOrder != null)
                    {
                        orderNumber = debt.RepairOrder.OrderNumber;
                    }

                    dt.Rows.Add(
                        debt.Id,
                        debt.CustomerId,
                        debt.RepairOrderId,
                        customerName,
                        orderNumber,
                        debt.Amount,
                        debt.PaidAmount,
                        debt.Amount - debt.PaidAmount,
                        debt.DebtDate,
                        debt.DueDate,
                        debt.Status.ToString(),
                        debt.Notes
                    );
                }

                // Set DataSource
                kryptonDataGridView1.DataSource = dt;

                // Update pagination controls
                UpdatePaginationControls();

                // Update status label
                lblStatus.Text = $"Total: {_totalRecords} dette(s)";
            }
            finally
            {
                // Hide loading indicator
                Cursor = Cursors.Default;
            }
        }

        private void UpdatePaginationControls()
        {
            // Update page label
            lblPage.Text = $"Page {_currentPage} / {_totalPages}";

            // Enable/disable navigation buttons
            btnFirst.Enabled = _currentPage > 1;
            btnPrevious.Enabled = _currentPage > 1;
            btnNext.Enabled = _currentPage < _totalPages;
            btnLast.Enabled = _currentPage < _totalPages;
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            _searchTerm = txtSearch.Text.Trim();
            _currentPage = 1;
            await LoadDebts();
        }

        private async void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.SuppressKeyPress = true;
                _searchTerm = txtSearch.Text.Trim();
                _currentPage = 1;
                await LoadDebts();
            }
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadDebts();
        }

        private async void BtnFirst_Click(object sender, EventArgs e)
        {
            _currentPage = 1;
            await LoadDebts();
        }

        private async void BtnPrevious_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadDebts();
            }
        }

        private async void BtnNext_Click(object sender, EventArgs e)
        {
            if (_currentPage < _totalPages)
            {
                _currentPage++;
                await LoadDebts();
            }
        }

        private async void BtnLast_Click(object sender, EventArgs e)
        {
            _currentPage = _totalPages;
            await LoadDebts();
        }

        private string GetPaymentStatusDisplayName(PaymentStatus status)
        {
            switch (status)
            {
                case PaymentStatus.unpaid:
                    return "Non payé";
                case PaymentStatus.partially_paid:
                    return "Partiellement payé";
                case PaymentStatus.paid:
                    return "Payé";
                default:
                    return status.ToString();
            }
        }
    }
}
