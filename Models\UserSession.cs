﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class UserSession
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Identifiant de l'utilisateur")]
        public int UserId { get; set; }

        [Display(Name = "Identifiant de session")]
        public string SessionId { get; set; }

        [Display(Name = "Adresse IP")]
        public string IpAddress { get; set; }

        [Display(Name = "Nom d'hôte")]
        public string Hostname { get; set; }

        [Display(Name = "Heure de connexion")]
        public DateTime LoginTime { get; set; }

        [Display(Name = "Dernière activité")]
        public DateTime LastActivity { get; set; }

        [Display(Name = "Heure de déconnexion")]
        public DateTime? LogoutTime { get; set; }

        [Display(Name = "Est actif")]
        public bool IsActive { get; set; }

        // Navigation property
        [Display(Name = "Utilisateur")]
        public User User { get; set; }
    }
}
