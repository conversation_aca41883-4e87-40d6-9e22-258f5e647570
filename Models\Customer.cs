﻿using IRepairIT.utilities;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class Customer : EntityValidator
    {
        [DisplayName("#")]
        public int id { get; set; }

        [Display<PERSON><PERSON>("Nom"), Required(ErrorMessage = "Le champ [ Nom ] doit être renseigné")]
        public string name { get; set; }

        [DisplayName("Téléphone"), Required(ErrorMessage = "Le champ [ Téléphone ] doit être renseigné")]
        public string phone { get; set; }

        [Display<PERSON>ame("Email"), EmailAddress(ErrorMessage = "Le format de l'email n'est pas valide")]
        public string email { get; set; }

        [DisplayName("Adresse")]
        public string address { get; set; }

        [DisplayName("Notes")]
        public string notes { get; set; }

        [DisplayName("Date d'inscription")]
        public DateTime created_at { get; set; }

        [DisplayName("Date de mise à jour")]
        public DateTime? updated_at { get; set; }
    }
}
