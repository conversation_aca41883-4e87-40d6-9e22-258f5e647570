﻿using Dapper;
using MySql.Data.MySqlClient;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace IRepairIT.Data.Common
{
    public class DataAccess
    {
        public async Task<T> QuerySingleOrDefaultQuery<T>(string sql, object param = null)
        {
            using (IDbConnection con = new MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
            {
                try
                {
                    if (con.State != ConnectionState.Open)
                        con.Open();

                    var result = await con.QuerySingleOrDefaultAsync<T>(sql, param);
                    return result;
                }
                finally
                {
                    if (con.State == ConnectionState.Open)
                        con.Close();
                }
            }
        }

        public async Task<T> QueryFirstOrDefaultQuery<T>(string sql, object param = null)
        {
            using (IDbConnection con = new MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
            {
                try
                {
                    if (con.State != ConnectionState.Open)
                        con.Open();

                    return await con.QueryFirstOrDefaultAsync<T>(sql, param);
                }
                finally
                {
                    if (con.State == ConnectionState.Open)
                        con.Close();
                }
            }
        }

        public async Task<IEnumerable<T>> QueryQuery<T>(string sql, object param = null)
        {
            using (IDbConnection con = new MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
            {
                try
                {
                    if (con.State != ConnectionState.Open)
                        con.Open();

                    return await con.QueryAsync<T>(sql, param);
                }
                finally
                {
                    if (con.State == ConnectionState.Open)
                        con.Close();
                }
            }
        }

        public async Task<int> CountQuery(string sql, object param = null)
        {
            string countSql = $"SELECT COUNT(*) FROM {sql}";

            using (IDbConnection con = new MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
            {
                try
                {
                    if (con.State != ConnectionState.Open)
                        con.Open();

                    return await con.ExecuteScalarAsync<int>(countSql, param);
                }
                finally
                {
                    if (con.State == ConnectionState.Open)
                        con.Close();
                }
            }
        }
        public async Task<decimal> CountCustomQuery(string sql, object param = null)
        {
            using (IDbConnection con = new MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
            {
                try
                {
                    if (con.State != ConnectionState.Open)
                        con.Open();

                    return await con.ExecuteScalarAsync<decimal>(sql, param);
                }
                finally
                {
                    if (con.State == ConnectionState.Open)
                        con.Close();
                }
            }
        }
        public async Task<int> ExecuteQuery(string sql, object param = null)
        {
            using (IDbConnection con = new MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
            {
                try
                {
                    if (con.State != ConnectionState.Open)
                        con.Open();

                    return await con.ExecuteAsync(sql, param);
                }
                finally
                {
                    if (con.State == ConnectionState.Open)
                        con.Close();
                }
            }
        }

        public async Task<int> ExecuteQuery(string sql, object param = null, IDbTransaction transaction = null)
        {
            if (transaction != null)
            {
                // Use the connection from the transaction
                return await transaction.Connection.ExecuteAsync(sql, param, transaction);
            }
            else
            {
                // Use a new connection
                using (IDbConnection con = new MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
                {
                    try
                    {
                        if (con.State != ConnectionState.Open)
                            con.Open();

                        return await con.ExecuteAsync(sql, param);
                    }
                    finally
                    {
                        if (con.State == ConnectionState.Open)
                            con.Close();
                    }
                }
            }
        }

        public async Task<T> ExecuteScalerQuery<T>(string sql, object param = null)
        {
            using (IDbConnection con = new MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
            {
                try
                {
                    if (con.State != ConnectionState.Open)
                        con.Open();

                    return await con.ExecuteScalarAsync<T>(sql, param);
                }
                finally
                {
                    if (con.State == ConnectionState.Open)
                        con.Close();
                }
            }
        }

        public async Task<int> InsertAndGetIdAsync(string sql, object param = null)
        {
            using (IDbConnection con = new MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
            {
                try
                {
                    if (con.State != ConnectionState.Open)
                        con.Open();

                    await con.ExecuteAsync(sql, param);
                    return await con.ExecuteScalarAsync<int>("SELECT LAST_INSERT_ID()");
                }
                finally
                {
                    if (con.State == ConnectionState.Open)
                        con.Close();
                }
            }
        }

        public async Task<int> InsertAndGetIdAsync(string sql, object param = null, IDbTransaction transaction = null)
        {
            if (transaction != null)
            {
                // Use the connection from the transaction
                await transaction.Connection.ExecuteAsync(sql, param, transaction);
                return await transaction.Connection.ExecuteScalarAsync<int>("SELECT LAST_INSERT_ID()", null, transaction);
            }
            else
            {
                // Use a new connection
                using (IDbConnection con = new MySqlConnection(Properties.Settings.Default.CurrentConnectionString))
                {
                    try
                    {
                        if (con.State != ConnectionState.Open)
                            con.Open();

                        await con.ExecuteAsync(sql, param);
                        return await con.ExecuteScalarAsync<int>("SELECT LAST_INSERT_ID()");
                    }
                    finally
                    {
                        if (con.State == ConnectionState.Open)
                            con.Close();
                    }
                }
            }
        }

    }
}
