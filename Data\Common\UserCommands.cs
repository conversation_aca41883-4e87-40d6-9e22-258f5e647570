﻿using Dapper;
using IRepairIT.Models;
using IRepairIT.Models.DTO;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace IRepairIT.Data.Common
{
    public class UserCommands
    {
        private readonly DataAccess _db;
        public UserCommands()
        {
            _db = new DataAccess();
        }
        public async Task<PagedUsers> GetALL(string searchTerm, int offset, int pageSize)
        {
            const string sql = @"SELECT COUNT(*)
    INTO totalRows
    FROM `customers`
    WHERE
        (
            `first_name` LIKE CONCAT('%', searchTerm, '%') OR
            `last_name` LIKE CONCAT('%', searchTerm, '%') OR
            `email` LIKE CONCAT('%', searchTerm, '%') OR
            `phone` LIKE CONCAT('%', searchTerm, '%') OR
            `address` LIKE CONCAT('%', searchTerm, '%') OR
            `city` LIKE CONCAT('%', searchTerm, '%')
        );

    SELECT
        *
    FROM
        `customers`
    WHERE
        (
            `first_name` LIKE CONCAT('%', searchTerm, '%') OR
            `last_name` LIKE CONCAT('%', searchTerm, '%') OR
            `email` LIKE CONCAT('%', searchTerm, '%') OR
            `phone` LIKE CONCAT('%', searchTerm, '%') OR
            `address` LIKE CONCAT('%', searchTerm, '%') OR
            `city` LIKE CONCAT('%', searchTerm, '%')
        )
    ORDER BY `id`
    LIMIT pageSize OFFSET offsetVal;";

            var parameters = new DynamicParameters();
            parameters.Add("searchTerm", searchTerm);
            parameters.Add("offsetVal", offset);
            parameters.Add("pageSize", pageSize);
            parameters.Add("totalRows", dbType: DbType.Int32, direction: ParameterDirection.Output);
            var results = await _db.QueryQuery<User>(sql,parameters);
            int TotalRows = parameters.Get<int>("totalRows");
            return new PagedUsers
            {
                Results = results,
                TotalRows = TotalRows
            };
        }
        public async Task<IEnumerable<User>> GetAllAsync()
        {
            const string sql = @"
                SELECT * FROM users
                ORDER BY username";
            return await _db.QueryQuery<User>(sql);
        }

        public async Task<bool> EmailExistsAsync(string email, int excludeUserId = 0)
        {
            string sql = @"
                SELECT COUNT(*) FROM users
                WHERE email = @Email";

            if (excludeUserId > 0)
            {
                sql += " AND id != @ExcludeUserId";
                return await _db.ExecuteScalerQuery<int>(sql, new { Email = email, ExcludeUserId = excludeUserId }) > 0;
            }
            else
            {
                return await _db.ExecuteScalerQuery<int>(sql, new { Email = email }) > 0;
            }
        }

        public async Task<int> GetCountAsync(string searchTerm)
        {
            const string sql = @"users WHERE
                username LIKE @Search
                OR full_name LIKE @Search
                OR email LIKE @Search";

            return await _db.CountQuery(sql, new { Search = $"%{searchTerm}%" });
        }

        public async Task<IEnumerable<User>> SearchAsync(string searchTerm)
        {
            const string sql = @"
                SELECT * FROM users
                WHERE username LIKE @Search
                OR full_name LIKE @Search
                OR email LIKE @Search
                ORDER BY username";

            return await _db.QueryQuery<User>(sql, new { Search = $"%{searchTerm}%" });
        }

        public async Task<IEnumerable<User>> SearchAsync(string searchTerm, int offset, int pageSize)
        {
            const string sql = @"
                SELECT * FROM users
                WHERE username LIKE @Search
                OR full_name LIKE @Search
                OR email LIKE @Search
                ORDER BY username
                LIMIT @PageSize OFFSET @Offset";

            return await _db.QueryQuery<User>(sql, new {
                Search = $"%{searchTerm}%",
                Offset = offset,
                PageSize = pageSize
            });
        }

        public async Task<int> InsertAsync(User user)
        {
            const string sql = @"
                INSERT INTO users (
                    username, password, full_name, email, phone,
                    role, status, created_at, updated_at
                ) VALUES (
                    @Username, @Password, @Full_Name, @Email, @Phone,
                    @Role, @Status, @CreatedAt, @UpdatedAt
                );";

            var parameters = new
            {
                user.Username,
                user.Password,
                user.Full_Name,
                user.Email,
                user.Phone,
                Role = user.Role.ToString(),
                Status = user.Status.ToString(),
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            return await _db.InsertAndGetIdAsync(sql, parameters);
        }

        public async Task<int> UpdateAsync(User user)
        {
            const string sql = @"
                UPDATE users SET
                    username = @Username,
                    full_name = @Full_Name,
                    email = @Email,
                    phone = @Phone,
                    role = @Role,
                    status = @Status,
                    updated_at = @UpdatedAt
                WHERE id = @Id";

            var parameters = new
            {
                user.Id,
                user.Username,
                user.Full_Name,
                user.Email,
                user.Phone,
                Role = user.Role.ToString(),
                Status = user.Status.ToString(),
                UpdatedAt = DateTime.Now
            };

            return await _db.ExecuteQuery(sql, parameters);
        }

        public async Task<int> ChangePasswordAsync(int userId, string newPassword)
        {
            const string sql = @"
                UPDATE users SET
                    password = @Password,
                    updated_at = @UpdatedAt
                WHERE id = @Id";

            var parameters = new
            {
                Id = userId,
                Password = newPassword,
                UpdatedAt = DateTime.Now
            };

            return await _db.ExecuteQuery(sql, parameters);
        }

        public async Task<int> DeleteAsync(int id)
        {
            const string sql = @"DELETE FROM users WHERE id = @Id";
            return await _db.ExecuteQuery(sql, new { Id = id });
        }

        public async Task<User> AuthenticateAsync(string username, string password)
        {
            const string sql = @"
                SELECT * FROM users
                WHERE username = @Username
                AND password = @Password
                AND status = 'active'";

            return await _db.QuerySingleOrDefaultQuery<User>(sql, new { Username = username, Password = password });
        }

        public async Task<User> GetByIdAsync(int id)
        {
            const string sql = @"
                SELECT * FROM users
                WHERE id = @Id";

            return await _db.QuerySingleOrDefaultQuery<User>(sql, new { Id = id });
        }

        public async Task<int> CreateUserSessionAsync(int userId)
        {
            const string sql = @"
                INSERT INTO user_sessions (
                    user_id, session_id, ip_address, hostname,
                    login_time, last_activity, is_active
                ) VALUES (
                    @UserId, @SessionId, @IpAddress, @Hostname,
                    @LoginTime, @LastActivity, @IsActive
                );";

            var parameters = new
            {
                UserId = userId,
                SessionId = Guid.NewGuid().ToString(),
                IpAddress = System.Net.Dns.GetHostAddresses(System.Net.Dns.GetHostName())[0].ToString(),
                Hostname = System.Net.Dns.GetHostName(),
                LoginTime = DateTime.Now,
                LastActivity = DateTime.Now,
                IsActive = true
            };

            return await _db.InsertAndGetIdAsync(sql, parameters);
        }

        public async Task<int> CloseUserSessionAsync(int userId)
        {
            const string sql = @"
                UPDATE user_sessions SET
                    logout_time = @LogoutTime,
                    is_active = @IsActive
                WHERE user_id = @UserId
                AND is_active = 1";

            var parameters = new
            {
                UserId = userId,
                LogoutTime = DateTime.Now,
                IsActive = false
            };

            return await _db.ExecuteQuery(sql, parameters);
        }

        public async Task<UserSession> GetActiveSessionAsync(int userId)
        {
            const string sql = @"
                SELECT * FROM user_sessions
                WHERE user_id = @UserId
                AND is_active = 1
                ORDER BY login_time DESC
                LIMIT 1";

            return await _db.QuerySingleOrDefaultQuery<UserSession>(sql, new { UserId = userId });
        }

        public async Task<int> UpdateLastActivityAsync(int userId)
        {
            const string sql = @"
                UPDATE user_sessions SET
                    last_activity = @LastActivity
                WHERE user_id = @UserId
                AND is_active = 1";

            var parameters = new
            {
                UserId = userId,
                LastActivity = DateTime.Now
            };

            return await _db.ExecuteQuery(sql, parameters);
        }
    }
}
