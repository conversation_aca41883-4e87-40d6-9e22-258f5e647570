﻿using IRepairIT.Data.Common;
using IRepairIT.Models;
using Krypton.Toolkit;
using System;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FSHOP
{
    public partial class FRM_SHOP : KryptonForm
    {
        private readonly Data.Common.ShopInfoCommands _cmd;
        private ShopInfo _shopInfo;
        private string _selectedLogoPath;
        private bool _isLogoChanged = false;

        public FRM_SHOP()
        {
            InitializeComponent();
            _cmd = new Data.Common.ShopInfoCommands();
            SetupForm();
        }

        private void SetupForm()
        {
            // Update label text
            lblUsername.Values.Text = "Nom de l'entreprise *:";
            lblFullName.Values.Text = "Activité *:";
            lblEmail.Values.Text = "Email:";
            lblPhone.Values.Text = "Téléphone:";
            lblRole.Values.Text = "Adresse:";
            lblStatus.Values.Text = "Site web:";

            // Set text box styles
            SetTextBoxStyle(txtUsername);
            SetTextBoxStyle(txtFullName);
            SetTextBoxStyle(txtEmail);
            SetTextBoxStyle(txtPhone);
            SetTextBoxStyle(kryptonTextBox6);

            // Set rich text box style
            kryptonRichTextBox1.StateCommon.Border.Color1 = Color.FromArgb(224, 224, 224);
            kryptonRichTextBox1.StateCommon.Border.DrawBorders = Krypton.Toolkit.PaletteDrawBorders.Top | Krypton.Toolkit.PaletteDrawBorders.Bottom | Krypton.Toolkit.PaletteDrawBorders.Left | Krypton.Toolkit.PaletteDrawBorders.Right;
            kryptonRichTextBox1.StateCommon.Border.Rounding = 6;
            kryptonRichTextBox1.StateCommon.Border.Width = 1;
            kryptonRichTextBox1.StateCommon.Content.Font = new Font("Segoe UI", 9.75F);

            // Set header group style
            kryptonHeaderGroup1.StateCommon.HeaderPrimary.Back.Color1 = Color.FromArgb(52, 152, 219);
            kryptonHeaderGroup1.StateCommon.HeaderPrimary.Back.Color2 = Color.FromArgb(41, 128, 185);
            kryptonHeaderGroup1.StateCommon.HeaderPrimary.Content.ShortText.Color1 = Color.White;
            kryptonHeaderGroup1.StateCommon.HeaderPrimary.Content.ShortText.Font = new Font("Segoe UI", 9.75F, FontStyle.Bold);
            kryptonHeaderGroup1.ValuesPrimary.Heading = "Logo";

            // Set button event handlers
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            kryptonButton1.Click += BtnBrowseLogo_Click;

            // Set navigator style
            kryptonNavigator1.StateCommon.Tab.Back.Color1 = Color.FromArgb(52, 152, 219);
            kryptonNavigator1.StateCommon.Tab.Back.Color2 = Color.FromArgb(41, 128, 185);
            kryptonNavigator1.StateCommon.Tab.Content.ShortText.Color1 = Color.White;
            kryptonNavigator1.StateCommon.Tab.Content.ShortText.Font = new Font("Segoe UI", 9.75F, FontStyle.Bold);

            // Load shop info
            LoadShopInfo().ConfigureAwait(false);
        }

        private void SetTextBoxStyle(KryptonTextBox textBox)
        {
            textBox.StateCommon.Border.Color1 = Color.FromArgb(224, 224, 224);
            textBox.StateCommon.Border.DrawBorders = Krypton.Toolkit.PaletteDrawBorders.Top | Krypton.Toolkit.PaletteDrawBorders.Bottom | Krypton.Toolkit.PaletteDrawBorders.Left | Krypton.Toolkit.PaletteDrawBorders.Right;
            textBox.StateCommon.Border.Rounding = 6;
            textBox.StateCommon.Border.Width = 1;
            textBox.StateCommon.Content.Font = new Font("Segoe UI", 9.75F);
        }

        private async Task LoadShopInfo()
        {
            try
            {
                // Load shop info from database
                _shopInfo = await _cmd.GetShopInfoAsync();

                if (_shopInfo != null)
                {
                    // Populate form fields
                    txtUsername.Text = _shopInfo.name;
                    txtFullName.Text = _shopInfo.activity;
                    txtEmail.Text = _shopInfo.email;
                    txtPhone.Text = _shopInfo.phone;
                    kryptonRichTextBox1.Text = _shopInfo.address;
                    kryptonTextBox6.Text = _shopInfo.website;

                    // Load logo if exists
                    if (!string.IsNullOrEmpty(_shopInfo.logo_path))
                    {
                        string logoPath = Path.Combine(Application.StartupPath, _shopInfo.logo_path);
                        if (File.Exists(logoPath))
                        {
                            kryptonPictureBox1.Image = Image.FromFile(logoPath);
                            kryptonPictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show("Erreur lors du chargement des données: " + ex.Message, "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async Task SaveShopInfo()
        {
            try
            {
                // Validate form
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    KryptonMessageBox.Show("Veuillez saisir le nom de l'entreprise.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtFullName.Text))
                {
                    KryptonMessageBox.Show("Veuillez saisir l'activité de l'entreprise.", "Validation",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Warning);
                    txtFullName.Focus();
                    return;
                }

                // Save logo if changed
                string logoPath = _shopInfo?.logo_path;
                if (_isLogoChanged && !string.IsNullOrEmpty(_selectedLogoPath))
                {
                    string logoDirectory = Path.Combine(Application.StartupPath, "logos");
                    if (!Directory.Exists(logoDirectory))
                    {
                        Directory.CreateDirectory(logoDirectory);
                    }

                    string fileName = "logo_" + DateTime.Now.ToString("yyyyMMddHHmmss") + Path.GetExtension(_selectedLogoPath);
                    logoPath = Path.Combine("logos", fileName);

                    File.Copy(_selectedLogoPath, Path.Combine(Application.StartupPath, logoPath), true);
                }

                // Create shop info object
                var shopInfo = new ShopInfo
                {
                    name = txtUsername.Text.Trim(),
                    activity = txtFullName.Text.Trim(),
                    email = txtEmail.Text.Trim(),
                    phone = txtPhone.Text.Trim(),
                    address = kryptonRichTextBox1.Text.Trim(),
                    website = kryptonTextBox6.Text.Trim(),
                    logo_path = logoPath
                };

                if (_shopInfo != null)
                {
                    // Update existing shop info
                    shopInfo.id = _shopInfo.id;
                    await _cmd.UpdateShopInfoAsync(shopInfo);
                    KryptonMessageBox.Show("Les informations ont été mises à jour avec succès.", "Succès",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                }
                else
                {
                    // Insert new shop info
                    await _cmd.InsertShopInfoAsync(shopInfo);
                    KryptonMessageBox.Show("Les informations ont été enregistrées avec succès.", "Succès",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                }

                // Reload shop info
                await LoadShopInfo();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show("Erreur lors de l'enregistrement: " + ex.Message, "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            await SaveShopInfo();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void BtnBrowseLogo_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Image Files (*.png;*.jpg;*.jpeg;*.gif;*.bmp)|*.png;*.jpg;*.jpeg;*.gif;*.bmp";
                openFileDialog.Title = "Sélectionner un logo";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    _selectedLogoPath = openFileDialog.FileName;
                    _isLogoChanged = true;

                    // Display selected image
                    kryptonPictureBox1.Image = Image.FromFile(_selectedLogoPath);
                    kryptonPictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
                }
            }
        }

        private void FRM_SHOP_Load(object sender, EventArgs e)
        {
            // Set focus to the first field
            txtUsername.Focus();
        }
    }
}
