namespace IRepairIT.FRMS.FCOMMON
{
    partial class FRM_SELECT_CUSTOMER : Krypton.Toolkit.KryptonForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.kryptonPanel1 = new Krypton.Toolkit.KryptonPanel();
            this.lblTitle = new Krypton.Toolkit.KryptonLabel();
            this.cmbCustomer = new Krypton.Toolkit.KryptonComboBox();
            this.dgvCustomers = new Krypton.Toolkit.KryptonDataGridView();
            this.btnSelect = new Krypton.Toolkit.KryptonButton();
            this.btnCancel = new Krypton.Toolkit.KryptonButton();
            this.txtSearch = new Krypton.Toolkit.KryptonTextBox();
            this.btnSearch = new Krypton.Toolkit.KryptonButton();
            this.btnClearSearch = new Krypton.Toolkit.KryptonButton();
            this.pnlPagination = new Krypton.Toolkit.KryptonPanel();
            this.btnFirst = new Krypton.Toolkit.KryptonButton();
            this.btnPrev = new Krypton.Toolkit.KryptonButton();
            this.btnNext = new Krypton.Toolkit.KryptonButton();
            this.btnLast = new Krypton.Toolkit.KryptonButton();
            this.lblPagination = new Krypton.Toolkit.KryptonLabel();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).BeginInit();
            this.kryptonPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbCustomer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCustomers)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pnlPagination)).BeginInit();
            this.pnlPagination.SuspendLayout();
            this.SuspendLayout();
            //
            // kryptonPanel1
            //
            this.kryptonPanel1.Controls.Add(this.pnlPagination);
            this.kryptonPanel1.Controls.Add(this.btnClearSearch);
            this.kryptonPanel1.Controls.Add(this.btnSearch);
            this.kryptonPanel1.Controls.Add(this.txtSearch);
            this.kryptonPanel1.Controls.Add(this.btnCancel);
            this.kryptonPanel1.Controls.Add(this.btnSelect);
            this.kryptonPanel1.Controls.Add(this.dgvCustomers);
            this.kryptonPanel1.Controls.Add(this.cmbCustomer);
            this.kryptonPanel1.Controls.Add(this.lblTitle);
            this.kryptonPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonPanel1.Location = new System.Drawing.Point(0, 0);
            this.kryptonPanel1.Name = "kryptonPanel1";
            this.kryptonPanel1.Size = new System.Drawing.Size(484, 461);
            this.kryptonPanel1.TabIndex = 0;
            //
            // lblTitle
            //
            this.lblTitle.Location = new System.Drawing.Point(12, 12);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(460, 24);
            this.lblTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Values.Text = "Nom complet:";
            //
            // cmbCustomer
            //
            this.cmbCustomer.DropDownWidth = 460;
            this.cmbCustomer.Location = new System.Drawing.Point(12, 42);
            this.cmbCustomer.Name = "cmbCustomer";
            this.cmbCustomer.Size = new System.Drawing.Size(460, 25);
            this.cmbCustomer.TabIndex = 1;
            //
            // dgvCustomers
            //
            this.dgvCustomers.AllowUserToAddRows = false;
            this.dgvCustomers.AllowUserToDeleteRows = false;
            this.dgvCustomers.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvCustomers.Location = new System.Drawing.Point(12, 112);
            this.dgvCustomers.Name = "dgvCustomers";
            this.dgvCustomers.ReadOnly = true;
            this.dgvCustomers.Size = new System.Drawing.Size(460, 300);
            this.dgvCustomers.TabIndex = 2;
            //
            // btnSelect
            //
            this.btnSelect.Location = new System.Drawing.Point(266, 420);
            this.btnSelect.Name = "btnSelect";
            this.btnSelect.Size = new System.Drawing.Size(100, 29);
            this.btnSelect.TabIndex = 3;
            this.btnSelect.Values.Text = "Sélectionner";
            this.btnSelect.Click += new System.EventHandler(this.BtnSelect_Click);
            //
            // btnCancel
            //
            this.btnCancel.Location = new System.Drawing.Point(372, 420);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(100, 29);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Values.Text = "Annuler";
            this.btnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            //
            // txtSearch
            //
            this.txtSearch.Location = new System.Drawing.Point(12, 81);
            this.txtSearch.Name = "txtSearch";
            this.txtSearch.Size = new System.Drawing.Size(300, 25);
            this.txtSearch.TabIndex = 5;
            this.txtSearch.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.TxtSearch_KeyPress);
            //
            // btnSearch
            //
            this.btnSearch.Location = new System.Drawing.Point(318, 81);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(75, 25);
            this.btnSearch.TabIndex = 6;
            this.btnSearch.Values.Text = "Rechercher";
            this.btnSearch.Click += new System.EventHandler(this.BtnSearch_Click);
            //
            // btnClearSearch
            //
            this.btnClearSearch.Location = new System.Drawing.Point(399, 81);
            this.btnClearSearch.Name = "btnClearSearch";
            this.btnClearSearch.Size = new System.Drawing.Size(73, 25);
            this.btnClearSearch.TabIndex = 7;
            this.btnClearSearch.Values.Text = "Effacer";
            this.btnClearSearch.Click += new System.EventHandler(this.BtnClearSearch_Click);
            //
            // pnlPagination
            //
            this.pnlPagination.Controls.Add(this.lblPagination);
            this.pnlPagination.Controls.Add(this.btnLast);
            this.pnlPagination.Controls.Add(this.btnNext);
            this.pnlPagination.Controls.Add(this.btnPrev);
            this.pnlPagination.Controls.Add(this.btnFirst);
            this.pnlPagination.Location = new System.Drawing.Point(12, 418);
            this.pnlPagination.Name = "pnlPagination";
            this.pnlPagination.Size = new System.Drawing.Size(248, 31);
            this.pnlPagination.TabIndex = 8;
            //
            // btnFirst
            //
            this.btnFirst.Location = new System.Drawing.Point(3, 3);
            this.btnFirst.Name = "btnFirst";
            this.btnFirst.Size = new System.Drawing.Size(40, 25);
            this.btnFirst.TabIndex = 0;
            this.btnFirst.Values.Text = "<<";
            this.btnFirst.Click += new System.EventHandler(this.BtnFirst_Click);
            //
            // btnPrev
            //
            this.btnPrev.Location = new System.Drawing.Point(49, 3);
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.Size = new System.Drawing.Size(40, 25);
            this.btnPrev.TabIndex = 1;
            this.btnPrev.Values.Text = "<";
            this.btnPrev.Click += new System.EventHandler(this.BtnPrev_Click);
            //
            // btnNext
            //
            this.btnNext.Location = new System.Drawing.Point(159, 3);
            this.btnNext.Name = "btnNext";
            this.btnNext.Size = new System.Drawing.Size(40, 25);
            this.btnNext.TabIndex = 2;
            this.btnNext.Values.Text = ">";
            this.btnNext.Click += new System.EventHandler(this.BtnNext_Click);
            //
            // btnLast
            //
            this.btnLast.Location = new System.Drawing.Point(205, 3);
            this.btnLast.Name = "btnLast";
            this.btnLast.Size = new System.Drawing.Size(40, 25);
            this.btnLast.TabIndex = 3;
            this.btnLast.Values.Text = ">>";
            this.btnLast.Click += new System.EventHandler(this.BtnLast_Click);
            //
            // lblPagination
            //
            this.lblPagination.Location = new System.Drawing.Point(95, 3);
            this.lblPagination.Name = "lblPagination";
            this.lblPagination.Size = new System.Drawing.Size(58, 24);
            this.lblPagination.TabIndex = 4;
            this.lblPagination.Values.Text = "0/0";
            //
            // FRM_SELECT_CUSTOMER
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(484, 461);
            this.Controls.Add(this.kryptonPanel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FRM_SELECT_CUSTOMER";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Sélectionner un client";
            this.Load += new System.EventHandler(this.FRM_SELECT_CUSTOMER_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonPanel1)).EndInit();
            this.kryptonPanel1.ResumeLayout(false);
            this.kryptonPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbCustomer)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCustomers)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pnlPagination)).EndInit();
            this.pnlPagination.ResumeLayout(false);
            this.pnlPagination.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonPanel kryptonPanel1;
        private Krypton.Toolkit.KryptonLabel lblTitle;
        private Krypton.Toolkit.KryptonComboBox cmbCustomer;
        private Krypton.Toolkit.KryptonDataGridView dgvCustomers;
        private Krypton.Toolkit.KryptonButton btnSelect;
        private Krypton.Toolkit.KryptonButton btnCancel;
        private Krypton.Toolkit.KryptonTextBox txtSearch;
        private Krypton.Toolkit.KryptonButton btnSearch;
        private Krypton.Toolkit.KryptonButton btnClearSearch;
        private Krypton.Toolkit.KryptonPanel pnlPagination;
        private Krypton.Toolkit.KryptonLabel lblPagination;
        private Krypton.Toolkit.KryptonButton btnLast;
        private Krypton.Toolkit.KryptonButton btnNext;
        private Krypton.Toolkit.KryptonButton btnPrev;
        private Krypton.Toolkit.KryptonButton btnFirst;
    }
}
