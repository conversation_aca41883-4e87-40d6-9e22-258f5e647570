﻿namespace IRepairIT.FRMS.FCUSTOMERS
{
    partial class FRM_CUSTOMERS_NEW_WIZARD
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panelHeader = new Krypton.Toolkit.KryptonPanel();
            this.lblStepDescription = new Krypton.Toolkit.KryptonLabel();
            this.lblStepTitle = new Krypton.Toolkit.KryptonLabel();
            this.panelContent = new Krypton.Toolkit.KryptonPanel();
            this.panelStep3 = new Krypton.Toolkit.KryptonPanel();
            this.lblTotalCost = new Krypton.Toolkit.KryptonLabel();
            this.btnRemovePart = new Krypton.Toolkit.KryptonButton();
            this.btnAddPart = new Krypton.Toolkit.KryptonButton();
            this.lblParts = new Krypton.Toolkit.KryptonLabel();
            this.dgvParts = new Krypton.Toolkit.KryptonDataGridView();
            this.btnRemoveService = new Krypton.Toolkit.KryptonButton();
            this.btnAddService = new Krypton.Toolkit.KryptonButton();
            this.lblServices = new Krypton.Toolkit.KryptonLabel();
            this.dgvServices = new Krypton.Toolkit.KryptonDataGridView();
            this.txtTechnicalNotes = new Krypton.Toolkit.KryptonTextBox();
            this.lblTechnicalNotes = new Krypton.Toolkit.KryptonLabel();
            this.txtRepairNotes = new Krypton.Toolkit.KryptonTextBox();
            this.lblRepairNotes = new Krypton.Toolkit.KryptonLabel();
            this.txtWarrantyPeriod = new Krypton.Toolkit.KryptonTextBox();
            this.lblWarrantyPeriod = new Krypton.Toolkit.KryptonLabel();
            this.cmbTechnician = new Krypton.Toolkit.KryptonComboBox();
            this.lblTechnician = new Krypton.Toolkit.KryptonLabel();
            this.txtOrderNumber = new Krypton.Toolkit.KryptonTextBox();
            this.lblOrderNumber = new Krypton.Toolkit.KryptonLabel();
            this.lblRepairOrderTitle = new Krypton.Toolkit.KryptonLabel();
            this.panelStep2 = new Krypton.Toolkit.KryptonPanel();
            this.dtpExpectedDelivery = new Krypton.Toolkit.KryptonDateTimePicker();
            this.lblExpectedDelivery = new Krypton.Toolkit.KryptonLabel();
            this.txtAccessories = new Krypton.Toolkit.KryptonTextBox();
            this.lblAccessories = new Krypton.Toolkit.KryptonLabel();
            this.txtDevicePassword = new Krypton.Toolkit.KryptonTextBox();
            this.lblDevicePassword = new Krypton.Toolkit.KryptonLabel();
            this.txtDeviceCondition = new Krypton.Toolkit.KryptonTextBox();
            this.lblDeviceCondition = new Krypton.Toolkit.KryptonLabel();
            this.txtDeviceProblem = new Krypton.Toolkit.KryptonTextBox();
            this.lblDeviceProblem = new Krypton.Toolkit.KryptonLabel();
            this.txtIMEI = new Krypton.Toolkit.KryptonTextBox();
            this.lblIMEI = new Krypton.Toolkit.KryptonLabel();
            this.txtSerialNumber = new Krypton.Toolkit.KryptonTextBox();
            this.lblSerialNumber = new Krypton.Toolkit.KryptonLabel();
            this.txtDeviceModel = new Krypton.Toolkit.KryptonTextBox();
            this.lblDeviceModel = new Krypton.Toolkit.KryptonLabel();
            this.txtDeviceBrand = new Krypton.Toolkit.KryptonTextBox();
            this.lblDeviceBrand = new Krypton.Toolkit.KryptonLabel();
            this.cmbDeviceType = new Krypton.Toolkit.KryptonComboBox();
            this.lblDeviceType = new Krypton.Toolkit.KryptonLabel();
            this.lblDeviceTitle = new Krypton.Toolkit.KryptonLabel();
            this.panelStep1 = new Krypton.Toolkit.KryptonPanel();
            this.cmbPaymentStatus = new Krypton.Toolkit.KryptonComboBox();
            this.lblPaymentStatus = new Krypton.Toolkit.KryptonLabel();
            this.txtCustomerNotes = new Krypton.Toolkit.KryptonTextBox();
            this.lblCustomerNotes = new Krypton.Toolkit.KryptonLabel();
            this.txtCustomerAddress = new Krypton.Toolkit.KryptonTextBox();
            this.lblCustomerAddress = new Krypton.Toolkit.KryptonLabel();
            this.txtCustomerEmail = new Krypton.Toolkit.KryptonTextBox();
            this.lblCustomerEmail = new Krypton.Toolkit.KryptonLabel();
            this.txtCustomerPhone = new Krypton.Toolkit.KryptonTextBox();
            this.lblCustomerPhone = new Krypton.Toolkit.KryptonLabel();
            this.txtCustomerName = new Krypton.Toolkit.KryptonTextBox();
            this.lblCustomerName = new Krypton.Toolkit.KryptonLabel();
            this.lblCustomerTitle = new Krypton.Toolkit.KryptonLabel();
            this.panelFooter = new Krypton.Toolkit.KryptonPanel();
            this.kryptonProgressBar1 = new Krypton.Toolkit.KryptonProgressBar();
            this.btnCancel = new Krypton.Toolkit.KryptonButton();
            this.btnFinish = new Krypton.Toolkit.KryptonButton();
            this.btnNext = new Krypton.Toolkit.KryptonButton();
            this.btnBack = new Krypton.Toolkit.KryptonButton();
            ((System.ComponentModel.ISupportInitialize)(this.panelHeader)).BeginInit();
            this.panelHeader.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelContent)).BeginInit();
            this.panelContent.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelStep3)).BeginInit();
            this.panelStep3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvParts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvServices)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbTechnician)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelStep2)).BeginInit();
            this.panelStep2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDeviceType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelStep1)).BeginInit();
            this.panelStep1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPaymentStatus)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelFooter)).BeginInit();
            this.panelFooter.SuspendLayout();
            this.SuspendLayout();
            // 
            // panelHeader
            // 
            this.panelHeader.Controls.Add(this.lblStepDescription);
            this.panelHeader.Controls.Add(this.lblStepTitle);
            this.panelHeader.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelHeader.Location = new System.Drawing.Point(0, 0);
            this.panelHeader.Name = "panelHeader";
            this.panelHeader.Padding = new System.Windows.Forms.Padding(20, 15, 20, 15);
            this.panelHeader.Size = new System.Drawing.Size(804, 85);
            this.panelHeader.StateCommon.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(248)))), ((int)(((byte)(255)))));
            this.panelHeader.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(240)))), ((int)(((byte)(250)))));
            this.panelHeader.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.panelHeader.TabIndex = 1;
            // 
            // lblStepDescription
            // 
            this.lblStepDescription.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.lblStepDescription.Location = new System.Drawing.Point(20, 49);
            this.lblStepDescription.Name = "lblStepDescription";
            this.lblStepDescription.Size = new System.Drawing.Size(764, 21);
            this.lblStepDescription.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblStepDescription.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblStepDescription.TabIndex = 1;
            this.lblStepDescription.Values.Text = "Saisissez les informations du client.";
            // 
            // lblStepTitle
            // 
            this.lblStepTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblStepTitle.Location = new System.Drawing.Point(20, 15);
            this.lblStepTitle.Name = "lblStepTitle";
            this.lblStepTitle.Size = new System.Drawing.Size(764, 34);
            this.lblStepTitle.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.lblStepTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 16F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblStepTitle.TabIndex = 0;
            this.lblStepTitle.Values.Text = "Étape 1: Informations du client";
            // 
            // panelContent
            // 
            this.panelContent.Controls.Add(this.panelStep3);
            this.panelContent.Controls.Add(this.panelStep2);
            this.panelContent.Controls.Add(this.panelStep1);
            this.panelContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelContent.Location = new System.Drawing.Point(0, 85);
            this.panelContent.Name = "panelContent";
            this.panelContent.Padding = new System.Windows.Forms.Padding(25, 20, 25, 20);
            this.panelContent.Size = new System.Drawing.Size(804, 512);
            this.panelContent.StateCommon.Color1 = System.Drawing.Color.White;
            this.panelContent.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(255)))));
            this.panelContent.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.panelContent.TabIndex = 2;
            // 
            // panelStep3
            // 
            this.panelStep3.Controls.Add(this.lblTotalCost);
            this.panelStep3.Controls.Add(this.btnRemovePart);
            this.panelStep3.Controls.Add(this.btnAddPart);
            this.panelStep3.Controls.Add(this.lblParts);
            this.panelStep3.Controls.Add(this.dgvParts);
            this.panelStep3.Controls.Add(this.btnRemoveService);
            this.panelStep3.Controls.Add(this.btnAddService);
            this.panelStep3.Controls.Add(this.lblServices);
            this.panelStep3.Controls.Add(this.dgvServices);
            this.panelStep3.Controls.Add(this.txtTechnicalNotes);
            this.panelStep3.Controls.Add(this.lblTechnicalNotes);
            this.panelStep3.Controls.Add(this.txtRepairNotes);
            this.panelStep3.Controls.Add(this.lblRepairNotes);
            this.panelStep3.Controls.Add(this.txtWarrantyPeriod);
            this.panelStep3.Controls.Add(this.lblWarrantyPeriod);
            this.panelStep3.Controls.Add(this.cmbTechnician);
            this.panelStep3.Controls.Add(this.lblTechnician);
            this.panelStep3.Controls.Add(this.txtOrderNumber);
            this.panelStep3.Controls.Add(this.lblOrderNumber);
            this.panelStep3.Controls.Add(this.lblRepairOrderTitle);
            this.panelStep3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelStep3.Location = new System.Drawing.Point(25, 20);
            this.panelStep3.Name = "panelStep3";
            this.panelStep3.Padding = new System.Windows.Forms.Padding(20);
            this.panelStep3.Size = new System.Drawing.Size(754, 472);
            this.panelStep3.StateCommon.Color1 = System.Drawing.Color.White;
            this.panelStep3.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(254)))), ((int)(((byte)(255)))));
            this.panelStep3.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.panelStep3.TabIndex = 2;
            this.panelStep3.Visible = false;
            // 
            // lblTotalCost
            // 
            this.lblTotalCost.Location = new System.Drawing.Point(20, 447);
            this.lblTotalCost.Name = "lblTotalCost";
            this.lblTotalCost.Size = new System.Drawing.Size(140, 30);
            this.lblTotalCost.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.lblTotalCost.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTotalCost.TabIndex = 19;
            this.lblTotalCost.Values.Text = "💰 Total: 0.00 €";
            // 
            // btnRemovePart
            // 
            this.btnRemovePart.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnRemovePart.Location = new System.Drawing.Point(454, 408);
            this.btnRemovePart.Name = "btnRemovePart";
            this.btnRemovePart.Size = new System.Drawing.Size(140, 35);
            this.btnRemovePart.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(53)))), ((int)(((byte)(69)))));
            this.btnRemovePart.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(33)))), ((int)(((byte)(49)))));
            this.btnRemovePart.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(53)))), ((int)(((byte)(69)))));
            this.btnRemovePart.StateCommon.Border.Rounding = 6F;
            this.btnRemovePart.StateCommon.Border.Width = 1;
            this.btnRemovePart.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnRemovePart.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRemovePart.TabIndex = 18;
            this.btnRemovePart.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnRemovePart.Values.Text = "🗑️ Supprimer";
            // 
            // btnAddPart
            // 
            this.btnAddPart.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnAddPart.Location = new System.Drawing.Point(304, 408);
            this.btnAddPart.Name = "btnAddPart";
            this.btnAddPart.Size = new System.Drawing.Size(140, 35);
            this.btnAddPart.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(40)))), ((int)(((byte)(167)))), ((int)(((byte)(69)))));
            this.btnAddPart.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(147)))), ((int)(((byte)(49)))));
            this.btnAddPart.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(40)))), ((int)(((byte)(167)))), ((int)(((byte)(69)))));
            this.btnAddPart.StateCommon.Border.Rounding = 6F;
            this.btnAddPart.StateCommon.Border.Width = 1;
            this.btnAddPart.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnAddPart.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAddPart.TabIndex = 17;
            this.btnAddPart.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnAddPart.Values.Text = "➕ Ajouter pièce";
            // 
            // lblParts
            // 
            this.lblParts.Location = new System.Drawing.Point(304, 248);
            this.lblParts.Name = "lblParts";
            this.lblParts.Size = new System.Drawing.Size(148, 24);
            this.lblParts.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblParts.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblParts.TabIndex = 15;
            this.lblParts.Values.Text = "🔩 Pièces détachées:";
            // 
            // dgvParts
            // 
            this.dgvParts.AllowUserToAddRows = false;
            this.dgvParts.AllowUserToDeleteRows = false;
            this.dgvParts.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvParts.Location = new System.Drawing.Point(304, 278);
            this.dgvParts.MultiSelect = false;
            this.dgvParts.Name = "dgvParts";
            this.dgvParts.ReadOnly = true;
            this.dgvParts.RowHeadersVisible = false;
            this.dgvParts.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvParts.Size = new System.Drawing.Size(350, 120);
            this.dgvParts.StateCommon.Background.Color1 = System.Drawing.Color.White;
            this.dgvParts.StateCommon.BackStyle = Krypton.Toolkit.PaletteBackStyle.GridBackgroundList;
            this.dgvParts.StateCommon.DataCell.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgvParts.StateCommon.DataCell.Border.DrawBorders = ((Krypton.Toolkit.PaletteDrawBorders)((((Krypton.Toolkit.PaletteDrawBorders.Top | Krypton.Toolkit.PaletteDrawBorders.Bottom) 
            | Krypton.Toolkit.PaletteDrawBorders.Left) 
            | Krypton.Toolkit.PaletteDrawBorders.Right)));
            this.dgvParts.StateCommon.HeaderColumn.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.dgvParts.StateCommon.HeaderColumn.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.dgvParts.StateCommon.HeaderColumn.Content.Color1 = System.Drawing.Color.White;
            this.dgvParts.StateCommon.HeaderColumn.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dgvParts.TabIndex = 16;
            // 
            // btnRemoveService
            // 
            this.btnRemoveService.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnRemoveService.Location = new System.Drawing.Point(158, 414);
            this.btnRemoveService.Name = "btnRemoveService";
            this.btnRemoveService.Size = new System.Drawing.Size(140, 35);
            this.btnRemoveService.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(53)))), ((int)(((byte)(69)))));
            this.btnRemoveService.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(33)))), ((int)(((byte)(49)))));
            this.btnRemoveService.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(53)))), ((int)(((byte)(69)))));
            this.btnRemoveService.StateCommon.Border.Rounding = 6F;
            this.btnRemoveService.StateCommon.Border.Width = 1;
            this.btnRemoveService.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnRemoveService.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRemoveService.TabIndex = 14;
            this.btnRemoveService.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnRemoveService.Values.Text = "🗑️ Supprimer";
            // 
            // btnAddService
            // 
            this.btnAddService.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnAddService.Location = new System.Drawing.Point(12, 414);
            this.btnAddService.Name = "btnAddService";
            this.btnAddService.Size = new System.Drawing.Size(140, 35);
            this.btnAddService.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(40)))), ((int)(((byte)(167)))), ((int)(((byte)(69)))));
            this.btnAddService.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(147)))), ((int)(((byte)(49)))));
            this.btnAddService.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(40)))), ((int)(((byte)(167)))), ((int)(((byte)(69)))));
            this.btnAddService.StateCommon.Border.Rounding = 6F;
            this.btnAddService.StateCommon.Border.Width = 1;
            this.btnAddService.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnAddService.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAddService.TabIndex = 13;
            this.btnAddService.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnAddService.Values.Text = "➕ Ajouter service";
            // 
            // lblServices
            // 
            this.lblServices.Location = new System.Drawing.Point(20, 250);
            this.lblServices.Name = "lblServices";
            this.lblServices.Size = new System.Drawing.Size(132, 24);
            this.lblServices.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblServices.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblServices.TabIndex = 11;
            this.lblServices.Values.Text = "🔧 Services inclus:";
            // 
            // dgvServices
            // 
            this.dgvServices.AllowUserToAddRows = false;
            this.dgvServices.AllowUserToDeleteRows = false;
            this.dgvServices.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvServices.Location = new System.Drawing.Point(20, 280);
            this.dgvServices.MultiSelect = false;
            this.dgvServices.Name = "dgvServices";
            this.dgvServices.ReadOnly = true;
            this.dgvServices.RowHeadersVisible = false;
            this.dgvServices.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvServices.Size = new System.Drawing.Size(278, 120);
            this.dgvServices.StateCommon.Background.Color1 = System.Drawing.Color.White;
            this.dgvServices.StateCommon.BackStyle = Krypton.Toolkit.PaletteBackStyle.GridBackgroundList;
            this.dgvServices.StateCommon.DataCell.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgvServices.StateCommon.DataCell.Border.DrawBorders = ((Krypton.Toolkit.PaletteDrawBorders)((((Krypton.Toolkit.PaletteDrawBorders.Top | Krypton.Toolkit.PaletteDrawBorders.Bottom) 
            | Krypton.Toolkit.PaletteDrawBorders.Left) 
            | Krypton.Toolkit.PaletteDrawBorders.Right)));
            this.dgvServices.StateCommon.HeaderColumn.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.dgvServices.StateCommon.HeaderColumn.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.dgvServices.StateCommon.HeaderColumn.Content.Color1 = System.Drawing.Color.White;
            this.dgvServices.StateCommon.HeaderColumn.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dgvServices.TabIndex = 12;
            // 
            // txtTechnicalNotes
            // 
            this.txtTechnicalNotes.CueHint.CueHintText = "Détails techniques et observations";
            this.txtTechnicalNotes.Location = new System.Drawing.Point(420, 165);
            this.txtTechnicalNotes.Multiline = true;
            this.txtTechnicalNotes.Name = "txtTechnicalNotes";
            this.txtTechnicalNotes.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtTechnicalNotes.Size = new System.Drawing.Size(380, 60);
            this.txtTechnicalNotes.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtTechnicalNotes.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtTechnicalNotes.StateCommon.Border.Rounding = 4F;
            this.txtTechnicalNotes.StateCommon.Border.Width = 1;
            this.txtTechnicalNotes.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtTechnicalNotes.TabIndex = 10;
            // 
            // lblTechnicalNotes
            // 
            this.lblTechnicalNotes.Location = new System.Drawing.Point(420, 140);
            this.lblTechnicalNotes.Name = "lblTechnicalNotes";
            this.lblTechnicalNotes.Size = new System.Drawing.Size(117, 21);
            this.lblTechnicalNotes.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblTechnicalNotes.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTechnicalNotes.TabIndex = 9;
            this.lblTechnicalNotes.Values.Text = "Notes techniques:";
            // 
            // txtRepairNotes
            // 
            this.txtRepairNotes.CueHint.CueHintText = "Instructions et notes pour la réparation";
            this.txtRepairNotes.Location = new System.Drawing.Point(20, 165);
            this.txtRepairNotes.Multiline = true;
            this.txtRepairNotes.Name = "txtRepairNotes";
            this.txtRepairNotes.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtRepairNotes.Size = new System.Drawing.Size(380, 60);
            this.txtRepairNotes.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtRepairNotes.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtRepairNotes.StateCommon.Border.Rounding = 4F;
            this.txtRepairNotes.StateCommon.Border.Width = 1;
            this.txtRepairNotes.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtRepairNotes.TabIndex = 8;
            // 
            // lblRepairNotes
            // 
            this.lblRepairNotes.Location = new System.Drawing.Point(20, 140);
            this.lblRepairNotes.Name = "lblRepairNotes";
            this.lblRepairNotes.Size = new System.Drawing.Size(132, 21);
            this.lblRepairNotes.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblRepairNotes.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblRepairNotes.TabIndex = 7;
            this.lblRepairNotes.Values.Text = "Notes de réparation:";
            // 
            // txtWarrantyPeriod
            // 
            this.txtWarrantyPeriod.CueHint.CueHintText = "Ex: 30, 90, 365";
            this.txtWarrantyPeriod.Location = new System.Drawing.Point(430, 95);
            this.txtWarrantyPeriod.Name = "txtWarrantyPeriod";
            this.txtWarrantyPeriod.Size = new System.Drawing.Size(100, 27);
            this.txtWarrantyPeriod.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtWarrantyPeriod.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtWarrantyPeriod.StateCommon.Border.Rounding = 4F;
            this.txtWarrantyPeriod.StateCommon.Border.Width = 1;
            this.txtWarrantyPeriod.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtWarrantyPeriod.TabIndex = 6;
            // 
            // lblWarrantyPeriod
            // 
            this.lblWarrantyPeriod.Location = new System.Drawing.Point(430, 70);
            this.lblWarrantyPeriod.Name = "lblWarrantyPeriod";
            this.lblWarrantyPeriod.Size = new System.Drawing.Size(170, 21);
            this.lblWarrantyPeriod.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblWarrantyPeriod.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblWarrantyPeriod.TabIndex = 5;
            this.lblWarrantyPeriod.Values.Text = "Période de garantie (jours):";
            // 
            // cmbTechnician
            // 
            this.cmbTechnician.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbTechnician.DropDownWidth = 198;
            this.cmbTechnician.IntegralHeight = false;
            this.cmbTechnician.Location = new System.Drawing.Point(200, 95);
            this.cmbTechnician.Name = "cmbTechnician";
            this.cmbTechnician.Size = new System.Drawing.Size(200, 26);
            this.cmbTechnician.StateCommon.ComboBox.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.cmbTechnician.StateCommon.ComboBox.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.cmbTechnician.StateCommon.ComboBox.Border.Rounding = 4F;
            this.cmbTechnician.StateCommon.ComboBox.Border.Width = 1;
            this.cmbTechnician.StateCommon.ComboBox.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cmbTechnician.TabIndex = 4;
            // 
            // lblTechnician
            // 
            this.lblTechnician.Location = new System.Drawing.Point(200, 70);
            this.lblTechnician.Name = "lblTechnician";
            this.lblTechnician.Size = new System.Drawing.Size(124, 21);
            this.lblTechnician.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblTechnician.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTechnician.TabIndex = 3;
            this.lblTechnician.Values.Text = "Technicien assigné:";
            // 
            // txtOrderNumber
            // 
            this.txtOrderNumber.Location = new System.Drawing.Point(20, 95);
            this.txtOrderNumber.Name = "txtOrderNumber";
            this.txtOrderNumber.ReadOnly = true;
            this.txtOrderNumber.Size = new System.Drawing.Size(150, 27);
            this.txtOrderNumber.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(248)))), ((int)(((byte)(248)))));
            this.txtOrderNumber.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtOrderNumber.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtOrderNumber.StateCommon.Border.Rounding = 4F;
            this.txtOrderNumber.StateCommon.Border.Width = 1;
            this.txtOrderNumber.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtOrderNumber.TabIndex = 2;
            // 
            // lblOrderNumber
            // 
            this.lblOrderNumber.Location = new System.Drawing.Point(20, 70);
            this.lblOrderNumber.Name = "lblOrderNumber";
            this.lblOrderNumber.Size = new System.Drawing.Size(151, 21);
            this.lblOrderNumber.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblOrderNumber.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblOrderNumber.TabIndex = 1;
            this.lblOrderNumber.Values.Text = "Numéro de commande:";
            // 
            // lblRepairOrderTitle
            // 
            this.lblRepairOrderTitle.Location = new System.Drawing.Point(20, 20);
            this.lblRepairOrderTitle.Name = "lblRepairOrderTitle";
            this.lblRepairOrderTitle.Size = new System.Drawing.Size(226, 30);
            this.lblRepairOrderTitle.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.lblRepairOrderTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblRepairOrderTitle.TabIndex = 0;
            this.lblRepairOrderTitle.Values.Text = "⚙️ Ordre de réparation";
            // 
            // panelStep2
            // 
            this.panelStep2.Controls.Add(this.dtpExpectedDelivery);
            this.panelStep2.Controls.Add(this.lblExpectedDelivery);
            this.panelStep2.Controls.Add(this.txtAccessories);
            this.panelStep2.Controls.Add(this.lblAccessories);
            this.panelStep2.Controls.Add(this.txtDevicePassword);
            this.panelStep2.Controls.Add(this.lblDevicePassword);
            this.panelStep2.Controls.Add(this.txtDeviceCondition);
            this.panelStep2.Controls.Add(this.lblDeviceCondition);
            this.panelStep2.Controls.Add(this.txtDeviceProblem);
            this.panelStep2.Controls.Add(this.lblDeviceProblem);
            this.panelStep2.Controls.Add(this.txtIMEI);
            this.panelStep2.Controls.Add(this.lblIMEI);
            this.panelStep2.Controls.Add(this.txtSerialNumber);
            this.panelStep2.Controls.Add(this.lblSerialNumber);
            this.panelStep2.Controls.Add(this.txtDeviceModel);
            this.panelStep2.Controls.Add(this.lblDeviceModel);
            this.panelStep2.Controls.Add(this.txtDeviceBrand);
            this.panelStep2.Controls.Add(this.lblDeviceBrand);
            this.panelStep2.Controls.Add(this.cmbDeviceType);
            this.panelStep2.Controls.Add(this.lblDeviceType);
            this.panelStep2.Controls.Add(this.lblDeviceTitle);
            this.panelStep2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelStep2.Location = new System.Drawing.Point(25, 20);
            this.panelStep2.Name = "panelStep2";
            this.panelStep2.Padding = new System.Windows.Forms.Padding(20);
            this.panelStep2.Size = new System.Drawing.Size(754, 472);
            this.panelStep2.StateCommon.Color1 = System.Drawing.Color.White;
            this.panelStep2.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(254)))), ((int)(((byte)(255)))));
            this.panelStep2.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.panelStep2.TabIndex = 1;
            this.panelStep2.Visible = false;
            // 
            // dtpExpectedDelivery
            // 
            this.dtpExpectedDelivery.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.dtpExpectedDelivery.Location = new System.Drawing.Point(370, 404);
            this.dtpExpectedDelivery.Name = "dtpExpectedDelivery";
            this.dtpExpectedDelivery.Size = new System.Drawing.Size(200, 25);
            this.dtpExpectedDelivery.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.dtpExpectedDelivery.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.dtpExpectedDelivery.StateCommon.Border.Rounding = 4F;
            this.dtpExpectedDelivery.StateCommon.Border.Width = 1;
            this.dtpExpectedDelivery.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dtpExpectedDelivery.TabIndex = 20;
            // 
            // lblExpectedDelivery
            // 
            this.lblExpectedDelivery.Location = new System.Drawing.Point(370, 377);
            this.lblExpectedDelivery.Name = "lblExpectedDelivery";
            this.lblExpectedDelivery.Size = new System.Drawing.Size(157, 21);
            this.lblExpectedDelivery.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblExpectedDelivery.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblExpectedDelivery.TabIndex = 19;
            this.lblExpectedDelivery.Values.Text = "Date de livraison prévue:";
            // 
            // txtAccessories
            // 
            this.txtAccessories.CueHint.CueHintText = "Chargeur, étui, écouteurs, etc.";
            this.txtAccessories.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtAccessories.Location = new System.Drawing.Point(20, 404);
            this.txtAccessories.Multiline = true;
            this.txtAccessories.Name = "txtAccessories";
            this.txtAccessories.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtAccessories.Size = new System.Drawing.Size(330, 60);
            this.txtAccessories.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtAccessories.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtAccessories.StateCommon.Border.Rounding = 4F;
            this.txtAccessories.StateCommon.Border.Width = 1;
            this.txtAccessories.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtAccessories.TabIndex = 18;
            // 
            // lblAccessories
            // 
            this.lblAccessories.Location = new System.Drawing.Point(20, 377);
            this.lblAccessories.Name = "lblAccessories";
            this.lblAccessories.Size = new System.Drawing.Size(119, 21);
            this.lblAccessories.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblAccessories.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAccessories.TabIndex = 17;
            this.lblAccessories.Values.Text = "Accessoires inclus:";
            // 
            // txtDevicePassword
            // 
            this.txtDevicePassword.CueHint.CueHintText = "Code de déverrouillage";
            this.txtDevicePassword.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtDevicePassword.Location = new System.Drawing.Point(370, 311);
            this.txtDevicePassword.Name = "txtDevicePassword";
            this.txtDevicePassword.Size = new System.Drawing.Size(170, 27);
            this.txtDevicePassword.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtDevicePassword.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtDevicePassword.StateCommon.Border.Rounding = 4F;
            this.txtDevicePassword.StateCommon.Border.Width = 1;
            this.txtDevicePassword.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDevicePassword.TabIndex = 16;
            // 
            // lblDevicePassword
            // 
            this.lblDevicePassword.Location = new System.Drawing.Point(370, 284);
            this.lblDevicePassword.Name = "lblDevicePassword";
            this.lblDevicePassword.Size = new System.Drawing.Size(130, 21);
            this.lblDevicePassword.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblDevicePassword.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDevicePassword.TabIndex = 15;
            this.lblDevicePassword.Values.Text = "Mot de passe/Code:";
            // 
            // txtDeviceCondition
            // 
            this.txtDeviceCondition.CueHint.CueHintText = "État physique, rayures, etc.";
            this.txtDeviceCondition.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtDeviceCondition.Location = new System.Drawing.Point(20, 311);
            this.txtDeviceCondition.Multiline = true;
            this.txtDeviceCondition.Name = "txtDeviceCondition";
            this.txtDeviceCondition.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtDeviceCondition.Size = new System.Drawing.Size(330, 60);
            this.txtDeviceCondition.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtDeviceCondition.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtDeviceCondition.StateCommon.Border.Rounding = 4F;
            this.txtDeviceCondition.StateCommon.Border.Width = 1;
            this.txtDeviceCondition.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDeviceCondition.TabIndex = 14;
            // 
            // lblDeviceCondition
            // 
            this.lblDeviceCondition.Location = new System.Drawing.Point(20, 284);
            this.lblDeviceCondition.Name = "lblDeviceCondition";
            this.lblDeviceCondition.Size = new System.Drawing.Size(112, 21);
            this.lblDeviceCondition.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblDeviceCondition.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDeviceCondition.TabIndex = 13;
            this.lblDeviceCondition.Values.Text = "État de l\'appareil:";
            // 
            // txtDeviceProblem
            // 
            this.txtDeviceProblem.CueHint.CueHintText = "Décrivez le problème en détail";
            this.txtDeviceProblem.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtDeviceProblem.Location = new System.Drawing.Point(20, 198);
            this.txtDeviceProblem.Multiline = true;
            this.txtDeviceProblem.Name = "txtDeviceProblem";
            this.txtDeviceProblem.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtDeviceProblem.Size = new System.Drawing.Size(690, 80);
            this.txtDeviceProblem.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtDeviceProblem.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtDeviceProblem.StateCommon.Border.Rounding = 4F;
            this.txtDeviceProblem.StateCommon.Border.Width = 1;
            this.txtDeviceProblem.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDeviceProblem.TabIndex = 12;
            // 
            // lblDeviceProblem
            // 
            this.lblDeviceProblem.Location = new System.Drawing.Point(20, 171);
            this.lblDeviceProblem.Name = "lblDeviceProblem";
            this.lblDeviceProblem.Size = new System.Drawing.Size(181, 21);
            this.lblDeviceProblem.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblDeviceProblem.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDeviceProblem.TabIndex = 11;
            this.lblDeviceProblem.Values.Text = "Description du problème *:";
            // 
            // txtIMEI
            // 
            this.txtIMEI.CueHint.CueHintText = "IMEI ou code d\'identification";
            this.txtIMEI.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtIMEI.Location = new System.Drawing.Point(290, 135);
            this.txtIMEI.Name = "txtIMEI";
            this.txtIMEI.Size = new System.Drawing.Size(200, 27);
            this.txtIMEI.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtIMEI.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtIMEI.StateCommon.Border.Rounding = 4F;
            this.txtIMEI.StateCommon.Border.Width = 1;
            this.txtIMEI.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtIMEI.TabIndex = 10;
            // 
            // lblIMEI
            // 
            this.lblIMEI.Location = new System.Drawing.Point(290, 108);
            this.lblIMEI.Name = "lblIMEI";
            this.lblIMEI.Size = new System.Drawing.Size(76, 21);
            this.lblIMEI.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblIMEI.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblIMEI.TabIndex = 9;
            this.lblIMEI.Values.Text = "IMEI/Code:";
            // 
            // txtSerialNumber
            // 
            this.txtSerialNumber.CueHint.CueHintText = "Numéro de série de l\'appareil";
            this.txtSerialNumber.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtSerialNumber.Location = new System.Drawing.Point(20, 135);
            this.txtSerialNumber.Name = "txtSerialNumber";
            this.txtSerialNumber.Size = new System.Drawing.Size(250, 27);
            this.txtSerialNumber.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtSerialNumber.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtSerialNumber.StateCommon.Border.Rounding = 4F;
            this.txtSerialNumber.StateCommon.Border.Width = 1;
            this.txtSerialNumber.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtSerialNumber.TabIndex = 8;
            // 
            // lblSerialNumber
            // 
            this.lblSerialNumber.Location = new System.Drawing.Point(20, 108);
            this.lblSerialNumber.Name = "lblSerialNumber";
            this.lblSerialNumber.Size = new System.Drawing.Size(112, 21);
            this.lblSerialNumber.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblSerialNumber.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblSerialNumber.TabIndex = 7;
            this.lblSerialNumber.Values.Text = "Numéro de série:";
            // 
            // txtDeviceModel
            // 
            this.txtDeviceModel.CueHint.CueHintText = "Ex: Galaxy S21, iPhone 13";
            this.txtDeviceModel.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtDeviceModel.Location = new System.Drawing.Point(510, 76);
            this.txtDeviceModel.Name = "txtDeviceModel";
            this.txtDeviceModel.Size = new System.Drawing.Size(200, 27);
            this.txtDeviceModel.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtDeviceModel.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtDeviceModel.StateCommon.Border.Rounding = 4F;
            this.txtDeviceModel.StateCommon.Border.Width = 1;
            this.txtDeviceModel.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDeviceModel.TabIndex = 6;
            // 
            // lblDeviceModel
            // 
            this.lblDeviceModel.Location = new System.Drawing.Point(510, 49);
            this.lblDeviceModel.Name = "lblDeviceModel";
            this.lblDeviceModel.Size = new System.Drawing.Size(72, 21);
            this.lblDeviceModel.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblDeviceModel.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDeviceModel.TabIndex = 5;
            this.lblDeviceModel.Values.Text = "Modèle *:";
            // 
            // txtDeviceBrand
            // 
            this.txtDeviceBrand.CueHint.CueHintText = "Ex: Samsung, Apple, HP";
            this.txtDeviceBrand.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtDeviceBrand.Location = new System.Drawing.Point(289, 76);
            this.txtDeviceBrand.Name = "txtDeviceBrand";
            this.txtDeviceBrand.Size = new System.Drawing.Size(200, 27);
            this.txtDeviceBrand.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtDeviceBrand.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtDeviceBrand.StateCommon.Border.Rounding = 4F;
            this.txtDeviceBrand.StateCommon.Border.Width = 1;
            this.txtDeviceBrand.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDeviceBrand.TabIndex = 4;
            // 
            // lblDeviceBrand
            // 
            this.lblDeviceBrand.Location = new System.Drawing.Point(290, 49);
            this.lblDeviceBrand.Name = "lblDeviceBrand";
            this.lblDeviceBrand.Size = new System.Drawing.Size(73, 21);
            this.lblDeviceBrand.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblDeviceBrand.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDeviceBrand.TabIndex = 3;
            this.lblDeviceBrand.Values.Text = "Marque *:";
            // 
            // cmbDeviceType
            // 
            this.cmbDeviceType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbDeviceType.DropDownWidth = 248;
            this.cmbDeviceType.IntegralHeight = false;
            this.cmbDeviceType.Location = new System.Drawing.Point(20, 76);
            this.cmbDeviceType.Name = "cmbDeviceType";
            this.cmbDeviceType.Size = new System.Drawing.Size(250, 26);
            this.cmbDeviceType.StateCommon.ComboBox.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.cmbDeviceType.StateCommon.ComboBox.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.cmbDeviceType.StateCommon.ComboBox.Border.Rounding = 4F;
            this.cmbDeviceType.StateCommon.ComboBox.Border.Width = 1;
            this.cmbDeviceType.StateCommon.ComboBox.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cmbDeviceType.TabIndex = 2;
            // 
            // lblDeviceType
            // 
            this.lblDeviceType.Location = new System.Drawing.Point(20, 49);
            this.lblDeviceType.Name = "lblDeviceType";
            this.lblDeviceType.Size = new System.Drawing.Size(122, 21);
            this.lblDeviceType.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblDeviceType.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDeviceType.TabIndex = 1;
            this.lblDeviceType.Values.Text = "Type d\'appareil *:";
            // 
            // lblDeviceTitle
            // 
            this.lblDeviceTitle.Location = new System.Drawing.Point(20, 4);
            this.lblDeviceTitle.Name = "lblDeviceTitle";
            this.lblDeviceTitle.Size = new System.Drawing.Size(265, 30);
            this.lblDeviceTitle.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.lblDeviceTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDeviceTitle.TabIndex = 0;
            this.lblDeviceTitle.Values.Text = "🔧 Informations de l\'appareil";
            // 
            // panelStep1
            // 
            this.panelStep1.Controls.Add(this.cmbPaymentStatus);
            this.panelStep1.Controls.Add(this.lblPaymentStatus);
            this.panelStep1.Controls.Add(this.txtCustomerNotes);
            this.panelStep1.Controls.Add(this.lblCustomerNotes);
            this.panelStep1.Controls.Add(this.txtCustomerAddress);
            this.panelStep1.Controls.Add(this.lblCustomerAddress);
            this.panelStep1.Controls.Add(this.txtCustomerEmail);
            this.panelStep1.Controls.Add(this.lblCustomerEmail);
            this.panelStep1.Controls.Add(this.txtCustomerPhone);
            this.panelStep1.Controls.Add(this.lblCustomerPhone);
            this.panelStep1.Controls.Add(this.txtCustomerName);
            this.panelStep1.Controls.Add(this.lblCustomerName);
            this.panelStep1.Controls.Add(this.lblCustomerTitle);
            this.panelStep1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelStep1.Location = new System.Drawing.Point(25, 20);
            this.panelStep1.Name = "panelStep1";
            this.panelStep1.Padding = new System.Windows.Forms.Padding(20);
            this.panelStep1.Size = new System.Drawing.Size(754, 472);
            this.panelStep1.StateCommon.Color1 = System.Drawing.Color.White;
            this.panelStep1.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(254)))), ((int)(((byte)(255)))));
            this.panelStep1.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.panelStep1.TabIndex = 0;
            // 
            // cmbPaymentStatus
            // 
            this.cmbPaymentStatus.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbPaymentStatus.DropDownWidth = 198;
            this.cmbPaymentStatus.IntegralHeight = false;
            this.cmbPaymentStatus.Location = new System.Drawing.Point(20, 318);
            this.cmbPaymentStatus.Name = "cmbPaymentStatus";
            this.cmbPaymentStatus.Size = new System.Drawing.Size(200, 26);
            this.cmbPaymentStatus.StateCommon.ComboBox.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.cmbPaymentStatus.StateCommon.ComboBox.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.cmbPaymentStatus.StateCommon.ComboBox.Border.Rounding = 4F;
            this.cmbPaymentStatus.StateCommon.ComboBox.Border.Width = 1;
            this.cmbPaymentStatus.StateCommon.ComboBox.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cmbPaymentStatus.TabIndex = 12;
            // 
            // lblPaymentStatus
            // 
            this.lblPaymentStatus.Location = new System.Drawing.Point(20, 291);
            this.lblPaymentStatus.Name = "lblPaymentStatus";
            this.lblPaymentStatus.Size = new System.Drawing.Size(145, 21);
            this.lblPaymentStatus.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblPaymentStatus.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblPaymentStatus.TabIndex = 11;
            this.lblPaymentStatus.Values.Text = "Statut de paiement *:";
            // 
            // txtCustomerNotes
            // 
            this.txtCustomerNotes.CueHint.CueHintText = "Informations additionnelles";
            this.txtCustomerNotes.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtCustomerNotes.Location = new System.Drawing.Point(20, 225);
            this.txtCustomerNotes.Multiline = true;
            this.txtCustomerNotes.Name = "txtCustomerNotes";
            this.txtCustomerNotes.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtCustomerNotes.Size = new System.Drawing.Size(630, 60);
            this.txtCustomerNotes.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtCustomerNotes.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtCustomerNotes.StateCommon.Border.Rounding = 4F;
            this.txtCustomerNotes.StateCommon.Border.Width = 1;
            this.txtCustomerNotes.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCustomerNotes.TabIndex = 10;
            // 
            // lblCustomerNotes
            // 
            this.lblCustomerNotes.Location = new System.Drawing.Point(18, 198);
            this.lblCustomerNotes.Name = "lblCustomerNotes";
            this.lblCustomerNotes.Size = new System.Drawing.Size(150, 21);
            this.lblCustomerNotes.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblCustomerNotes.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerNotes.TabIndex = 9;
            this.lblCustomerNotes.Values.Text = "Notes supplémentaires:";
            // 
            // txtCustomerAddress
            // 
            this.txtCustomerAddress.CueHint.CueHintText = "Adresse, ville, code postal";
            this.txtCustomerAddress.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtCustomerAddress.Location = new System.Drawing.Point(342, 136);
            this.txtCustomerAddress.Multiline = true;
            this.txtCustomerAddress.Name = "txtCustomerAddress";
            this.txtCustomerAddress.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtCustomerAddress.Size = new System.Drawing.Size(300, 60);
            this.txtCustomerAddress.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtCustomerAddress.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtCustomerAddress.StateCommon.Border.Rounding = 4F;
            this.txtCustomerAddress.StateCommon.Border.Width = 1;
            this.txtCustomerAddress.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCustomerAddress.TabIndex = 8;
            // 
            // lblCustomerAddress
            // 
            this.lblCustomerAddress.Location = new System.Drawing.Point(342, 109);
            this.lblCustomerAddress.Name = "lblCustomerAddress";
            this.lblCustomerAddress.Size = new System.Drawing.Size(119, 21);
            this.lblCustomerAddress.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblCustomerAddress.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerAddress.TabIndex = 7;
            this.lblCustomerAddress.Values.Text = "Adresse complète:";
            // 
            // txtCustomerEmail
            // 
            this.txtCustomerEmail.CueHint.CueHintText = "<EMAIL>";
            this.txtCustomerEmail.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtCustomerEmail.Location = new System.Drawing.Point(20, 135);
            this.txtCustomerEmail.Name = "txtCustomerEmail";
            this.txtCustomerEmail.Size = new System.Drawing.Size(300, 27);
            this.txtCustomerEmail.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtCustomerEmail.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtCustomerEmail.StateCommon.Border.Rounding = 4F;
            this.txtCustomerEmail.StateCommon.Border.Width = 1;
            this.txtCustomerEmail.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCustomerEmail.TabIndex = 6;
            // 
            // lblCustomerEmail
            // 
            this.lblCustomerEmail.Location = new System.Drawing.Point(18, 109);
            this.lblCustomerEmail.Name = "lblCustomerEmail";
            this.lblCustomerEmail.Size = new System.Drawing.Size(96, 21);
            this.lblCustomerEmail.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblCustomerEmail.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerEmail.TabIndex = 5;
            this.lblCustomerEmail.Values.Text = "Adresse email:";
            // 
            // txtCustomerPhone
            // 
            this.txtCustomerPhone.CueHint.CueHintText = "Ex: +213 555 123 456";
            this.txtCustomerPhone.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtCustomerPhone.Location = new System.Drawing.Point(342, 76);
            this.txtCustomerPhone.Name = "txtCustomerPhone";
            this.txtCustomerPhone.Size = new System.Drawing.Size(250, 27);
            this.txtCustomerPhone.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtCustomerPhone.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtCustomerPhone.StateCommon.Border.Rounding = 4F;
            this.txtCustomerPhone.StateCommon.Border.Width = 1;
            this.txtCustomerPhone.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCustomerPhone.TabIndex = 4;
            // 
            // lblCustomerPhone
            // 
            this.lblCustomerPhone.Location = new System.Drawing.Point(342, 49);
            this.lblCustomerPhone.Name = "lblCustomerPhone";
            this.lblCustomerPhone.Size = new System.Drawing.Size(162, 21);
            this.lblCustomerPhone.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblCustomerPhone.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerPhone.TabIndex = 3;
            this.lblCustomerPhone.Values.Text = "Numéro de téléphone *:";
            // 
            // txtCustomerName
            // 
            this.txtCustomerName.CueHint.CueHintText = "Saisissez le nom complet";
            this.txtCustomerName.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtCustomerName.Location = new System.Drawing.Point(20, 75);
            this.txtCustomerName.Name = "txtCustomerName";
            this.txtCustomerName.Size = new System.Drawing.Size(300, 27);
            this.txtCustomerName.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtCustomerName.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtCustomerName.StateCommon.Border.Rounding = 4F;
            this.txtCustomerName.StateCommon.Border.Width = 1;
            this.txtCustomerName.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCustomerName.TabIndex = 2;
            // 
            // lblCustomerName
            // 
            this.lblCustomerName.Location = new System.Drawing.Point(18, 49);
            this.lblCustomerName.Name = "lblCustomerName";
            this.lblCustomerName.Size = new System.Drawing.Size(114, 21);
            this.lblCustomerName.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblCustomerName.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerName.TabIndex = 1;
            this.lblCustomerName.Values.Text = "Nom du client *:";
            // 
            // lblCustomerTitle
            // 
            this.lblCustomerTitle.Location = new System.Drawing.Point(23, 4);
            this.lblCustomerTitle.Name = "lblCustomerTitle";
            this.lblCustomerTitle.Size = new System.Drawing.Size(232, 30);
            this.lblCustomerTitle.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.lblCustomerTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerTitle.TabIndex = 0;
            this.lblCustomerTitle.Values.Text = "📋 Informations du client";
            // 
            // panelFooter
            // 
            this.panelFooter.Controls.Add(this.kryptonProgressBar1);
            this.panelFooter.Controls.Add(this.btnCancel);
            this.panelFooter.Controls.Add(this.btnFinish);
            this.panelFooter.Controls.Add(this.btnNext);
            this.panelFooter.Controls.Add(this.btnBack);
            this.panelFooter.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelFooter.Location = new System.Drawing.Point(0, 597);
            this.panelFooter.Name = "panelFooter";
            this.panelFooter.Padding = new System.Windows.Forms.Padding(20, 10, 20, 10);
            this.panelFooter.Size = new System.Drawing.Size(804, 70);
            this.panelFooter.StateCommon.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(245)))), ((int)(((byte)(245)))), ((int)(((byte)(245)))));
            this.panelFooter.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(235)))), ((int)(((byte)(235)))));
            this.panelFooter.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.panelFooter.TabIndex = 4;
            // 
            // kryptonProgressBar1
            // 
            this.kryptonProgressBar1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonProgressBar1.Location = new System.Drawing.Point(150, 30);
            this.kryptonProgressBar1.Name = "kryptonProgressBar1";
            this.kryptonProgressBar1.Size = new System.Drawing.Size(343, 12);
            this.kryptonProgressBar1.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.kryptonProgressBar1.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(150)))), ((int)(((byte)(155)))));
            this.kryptonProgressBar1.StateCommon.Back.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.kryptonProgressBar1.StateNormal.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(220)))), ((int)(((byte)(220)))));
            this.kryptonProgressBar1.StateNormal.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.kryptonProgressBar1.StateNormal.Back.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.kryptonProgressBar1.TabIndex = 17;
            this.kryptonProgressBar1.Value = 33;
            this.kryptonProgressBar1.Values.Text = "";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnCancel.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnCancel.Location = new System.Drawing.Point(20, 20);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(120, 40);
            this.btnCancel.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(220)))), ((int)(((byte)(220)))));
            this.btnCancel.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.btnCancel.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.btnCancel.StateCommon.Border.Rounding = 6F;
            this.btnCancel.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.TabIndex = 16;
            this.btnCancel.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnCancel.Values.Text = "Annuler";
            // 
            // btnFinish
            // 
            this.btnFinish.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnFinish.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnFinish.Location = new System.Drawing.Point(664, 20);
            this.btnFinish.Name = "btnFinish";
            this.btnFinish.Size = new System.Drawing.Size(120, 40);
            this.btnFinish.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.btnFinish.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.btnFinish.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.btnFinish.StateCommon.Border.Rounding = 6F;
            this.btnFinish.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnFinish.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnFinish.TabIndex = 15;
            this.btnFinish.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnFinish.Values.Text = "Terminer";
            // 
            // btnNext
            // 
            this.btnNext.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNext.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnNext.Location = new System.Drawing.Point(664, 20);
            this.btnNext.Name = "btnNext";
            this.btnNext.Size = new System.Drawing.Size(120, 40);
            this.btnNext.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.btnNext.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.btnNext.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.btnNext.StateCommon.Border.Rounding = 6F;
            this.btnNext.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnNext.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnNext.TabIndex = 14;
            this.btnNext.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnNext.Values.Text = "Suivant >";
            // 
            // btnBack
            // 
            this.btnBack.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBack.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnBack.Location = new System.Drawing.Point(534, 20);
            this.btnBack.Name = "btnBack";
            this.btnBack.Size = new System.Drawing.Size(120, 40);
            this.btnBack.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.btnBack.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(220)))), ((int)(((byte)(220)))));
            this.btnBack.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.btnBack.StateCommon.Border.Rounding = 6F;
            this.btnBack.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBack.TabIndex = 13;
            this.btnBack.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnBack.Values.Text = "< Retour";
            // 
            // FRM_CUSTOMERS_NEW_WIZARD
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(804, 667);
            this.Controls.Add(this.panelContent);
            this.Controls.Add(this.panelHeader);
            this.Controls.Add(this.panelFooter);
            this.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FRM_CUSTOMERS_NEW_WIZARD";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Assistant de création - Nouveau client et ordre de réparation";
            this.Load += new System.EventHandler(this.FRM_CUSTOMERS_NEW_WIZARD_Load);
            ((System.ComponentModel.ISupportInitialize)(this.panelHeader)).EndInit();
            this.panelHeader.ResumeLayout(false);
            this.panelHeader.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelContent)).EndInit();
            this.panelContent.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelStep3)).EndInit();
            this.panelStep3.ResumeLayout(false);
            this.panelStep3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvParts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvServices)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbTechnician)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelStep2)).EndInit();
            this.panelStep2.ResumeLayout(false);
            this.panelStep2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDeviceType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelStep1)).EndInit();
            this.panelStep1.ResumeLayout(false);
            this.panelStep1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPaymentStatus)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelFooter)).EndInit();
            this.panelFooter.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonPanel panelHeader;
        private Krypton.Toolkit.KryptonLabel lblStepDescription;
        private Krypton.Toolkit.KryptonLabel lblStepTitle;
        private Krypton.Toolkit.KryptonPanel panelContent;
        private Krypton.Toolkit.KryptonPanel panelFooter;
        private Krypton.Toolkit.KryptonProgressBar kryptonProgressBar1;
        private Krypton.Toolkit.KryptonButton btnCancel;
        private Krypton.Toolkit.KryptonButton btnFinish;
        private Krypton.Toolkit.KryptonButton btnNext;
        private Krypton.Toolkit.KryptonButton btnBack;

        // Customer Panel Controls
        private Krypton.Toolkit.KryptonPanel panelStep1;
        private Krypton.Toolkit.KryptonLabel lblCustomerTitle;
        private Krypton.Toolkit.KryptonLabel lblCustomerName;
        private Krypton.Toolkit.KryptonTextBox txtCustomerName;
        private Krypton.Toolkit.KryptonLabel lblCustomerPhone;
        private Krypton.Toolkit.KryptonTextBox txtCustomerPhone;
        private Krypton.Toolkit.KryptonLabel lblCustomerEmail;
        private Krypton.Toolkit.KryptonTextBox txtCustomerEmail;
        private Krypton.Toolkit.KryptonLabel lblCustomerAddress;
        private Krypton.Toolkit.KryptonTextBox txtCustomerAddress;
        private Krypton.Toolkit.KryptonLabel lblCustomerNotes;
        private Krypton.Toolkit.KryptonTextBox txtCustomerNotes;
        private Krypton.Toolkit.KryptonLabel lblPaymentStatus;
        private Krypton.Toolkit.KryptonComboBox cmbPaymentStatus;

        // Device Panel Controls
        private Krypton.Toolkit.KryptonPanel panelStep2;
        private Krypton.Toolkit.KryptonLabel lblDeviceTitle;
        private Krypton.Toolkit.KryptonLabel lblDeviceType;
        private Krypton.Toolkit.KryptonComboBox cmbDeviceType;
        private Krypton.Toolkit.KryptonLabel lblDeviceBrand;
        private Krypton.Toolkit.KryptonTextBox txtDeviceBrand;
        private Krypton.Toolkit.KryptonLabel lblDeviceModel;
        private Krypton.Toolkit.KryptonTextBox txtDeviceModel;
        private Krypton.Toolkit.KryptonLabel lblSerialNumber;
        private Krypton.Toolkit.KryptonTextBox txtSerialNumber;
        private Krypton.Toolkit.KryptonLabel lblIMEI;
        private Krypton.Toolkit.KryptonTextBox txtIMEI;
        private Krypton.Toolkit.KryptonLabel lblDeviceProblem;
        private Krypton.Toolkit.KryptonTextBox txtDeviceProblem;
        private Krypton.Toolkit.KryptonLabel lblDeviceCondition;
        private Krypton.Toolkit.KryptonTextBox txtDeviceCondition;
        private Krypton.Toolkit.KryptonLabel lblDevicePassword;
        private Krypton.Toolkit.KryptonTextBox txtDevicePassword;
        private Krypton.Toolkit.KryptonLabel lblAccessories;
        private Krypton.Toolkit.KryptonTextBox txtAccessories;
        private Krypton.Toolkit.KryptonLabel lblExpectedDelivery;
        private Krypton.Toolkit.KryptonDateTimePicker dtpExpectedDelivery;

        // Repair Order Panel Controls
        private Krypton.Toolkit.KryptonPanel panelStep3;
        private Krypton.Toolkit.KryptonLabel lblRepairOrderTitle;
        private Krypton.Toolkit.KryptonLabel lblOrderNumber;
        private Krypton.Toolkit.KryptonTextBox txtOrderNumber;
        private Krypton.Toolkit.KryptonLabel lblTechnician;
        private Krypton.Toolkit.KryptonComboBox cmbTechnician;
        private Krypton.Toolkit.KryptonLabel lblWarrantyPeriod;
        private Krypton.Toolkit.KryptonTextBox txtWarrantyPeriod;
        private Krypton.Toolkit.KryptonLabel lblRepairNotes;
        private Krypton.Toolkit.KryptonTextBox txtRepairNotes;
        private Krypton.Toolkit.KryptonLabel lblTechnicalNotes;
        private Krypton.Toolkit.KryptonTextBox txtTechnicalNotes;
        private Krypton.Toolkit.KryptonLabel lblServices;
        private Krypton.Toolkit.KryptonDataGridView dgvServices;
        private Krypton.Toolkit.KryptonButton btnAddService;
        private Krypton.Toolkit.KryptonButton btnRemoveService;
        private Krypton.Toolkit.KryptonLabel lblParts;
        private Krypton.Toolkit.KryptonDataGridView dgvParts;
        private Krypton.Toolkit.KryptonButton btnAddPart;
        private Krypton.Toolkit.KryptonButton btnRemovePart;
        private Krypton.Toolkit.KryptonLabel lblTotalCost;
    }
}