﻿namespace IRepairIT.FRMS.FCUSTOMERS
{
    partial class FRM_CUSTOMERS_NEW_WIZARD
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panelHeader = new Krypton.Toolkit.KryptonPanel();
            this.lblStepDescription = new Krypton.Toolkit.KryptonLabel();
            this.lblStepTitle = new Krypton.Toolkit.KryptonLabel();
            this.panelContent = new Krypton.Toolkit.KryptonPanel();
            this.panelFooter = new Krypton.Toolkit.KryptonPanel();
            this.kryptonProgressBar1 = new Krypton.Toolkit.KryptonProgressBar();
            this.btnCancel = new Krypton.Toolkit.KryptonButton();
            this.btnFinish = new Krypton.Toolkit.KryptonButton();
            this.btnNext = new Krypton.Toolkit.KryptonButton();
            this.btnBack = new Krypton.Toolkit.KryptonButton();
            ((System.ComponentModel.ISupportInitialize)(this.panelHeader)).BeginInit();
            this.panelHeader.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelContent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelFooter)).BeginInit();
            this.panelFooter.SuspendLayout();
            this.SuspendLayout();
            //
            // panelHeader
            //
            this.panelHeader.Controls.Add(this.lblStepDescription);
            this.panelHeader.Controls.Add(this.lblStepTitle);
            this.panelHeader.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelHeader.Location = new System.Drawing.Point(0, 0);
            this.panelHeader.Name = "panelHeader";
            this.panelHeader.Size = new System.Drawing.Size(860, 71);
            this.panelHeader.TabIndex = 1;
            //
            // lblStepDescription
            //
            this.lblStepDescription.Location = new System.Drawing.Point(12, 42);
            this.lblStepDescription.Name = "lblStepDescription";
            this.lblStepDescription.Size = new System.Drawing.Size(254, 20);
            this.lblStepDescription.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblStepDescription.TabIndex = 1;
            this.lblStepDescription.Values.Text = "Veuillez sélectionner l\'année pour ce dossier.";
            //
            // lblStepTitle
            //
            this.lblStepTitle.Location = new System.Drawing.Point(12, 6);
            this.lblStepTitle.Name = "lblStepTitle";
            this.lblStepTitle.Size = new System.Drawing.Size(252, 30);
            this.lblStepTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14.25F, System.Drawing.FontStyle.Bold);
            this.lblStepTitle.TabIndex = 0;
            this.lblStepTitle.Values.Text = "Étape 1: Année du Dossier";
            //
            // panelContent
            //
            this.panelContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelContent.Location = new System.Drawing.Point(0, 71);
            this.panelContent.Name = "panelContent";
            this.panelContent.Size = new System.Drawing.Size(860, 321);
            this.panelContent.TabIndex = 2;
            //
            // panelFooter
            //
            this.panelFooter.Controls.Add(this.kryptonProgressBar1);
            this.panelFooter.Controls.Add(this.btnCancel);
            this.panelFooter.Controls.Add(this.btnFinish);
            this.panelFooter.Controls.Add(this.btnNext);
            this.panelFooter.Controls.Add(this.btnBack);
            this.panelFooter.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelFooter.Location = new System.Drawing.Point(0, 392);
            this.panelFooter.Name = "panelFooter";
            this.panelFooter.Size = new System.Drawing.Size(860, 58);
            this.panelFooter.TabIndex = 4;
            //
            // kryptonProgressBar1
            //
            this.kryptonProgressBar1.Location = new System.Drawing.Point(142, 23);
            this.kryptonProgressBar1.Name = "kryptonProgressBar1";
            this.kryptonProgressBar1.Size = new System.Drawing.Size(246, 10);
            this.kryptonProgressBar1.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.kryptonProgressBar1.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.kryptonProgressBar1.StateNormal.Back.Color1 = System.Drawing.Color.Transparent;
            this.kryptonProgressBar1.StateNormal.Back.Color2 = System.Drawing.Color.Transparent;
            this.kryptonProgressBar1.StateNormal.Back.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.kryptonProgressBar1.TabIndex = 17;
            this.kryptonProgressBar1.Values.Text = "";
            //
            // btnCancel
            //
            this.btnCancel.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnCancel.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnCancel.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnCancel.Location = new System.Drawing.Point(12, 11);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(124, 35);
            this.btnCancel.StateCommon.Content.Image.ImageH = Krypton.Toolkit.PaletteRelativeAlign.Far;
            this.btnCancel.StateCommon.Content.Image.ImageV = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.btnCancel.TabIndex = 16;
            this.btnCancel.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnCancel.Values.Text = "Annuler";
            //
            // btnFinish
            //
            this.btnFinish.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnFinish.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnFinish.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnFinish.Location = new System.Drawing.Point(740, 11);
            this.btnFinish.Name = "btnFinish";
            this.btnFinish.Size = new System.Drawing.Size(108, 35);
            this.btnFinish.StateCommon.Content.Image.ImageH = Krypton.Toolkit.PaletteRelativeAlign.Far;
            this.btnFinish.StateCommon.Content.Image.ImageV = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.btnFinish.TabIndex = 15;
            this.btnFinish.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnFinish.Values.Text = "Terminer";
            //
            // btnNext
            //
            this.btnNext.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnNext.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnNext.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnNext.Location = new System.Drawing.Point(740, 11);
            this.btnNext.Name = "btnNext";
            this.btnNext.Size = new System.Drawing.Size(108, 35);
            this.btnNext.StateCommon.Content.Image.ImageH = Krypton.Toolkit.PaletteRelativeAlign.Far;
            this.btnNext.StateCommon.Content.Image.ImageV = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.btnNext.TabIndex = 14;
            this.btnNext.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnNext.Values.Text = "Suivant >";
            //
            // btnBack
            //
            this.btnBack.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnBack.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnBack.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnBack.Location = new System.Drawing.Point(626, 12);
            this.btnBack.Name = "btnBack";
            this.btnBack.Size = new System.Drawing.Size(108, 35);
            this.btnBack.StateCommon.Content.Image.ImageH = Krypton.Toolkit.PaletteRelativeAlign.Far;
            this.btnBack.StateCommon.Content.Image.ImageV = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.btnBack.TabIndex = 13;
            this.btnBack.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnBack.Values.Text = "< Retour";
            //
            // FRM_CUSTOMERS_NEW_WIZARD
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(860, 450);
            this.Controls.Add(this.panelContent);
            this.Controls.Add(this.panelHeader);
            this.Controls.Add(this.panelFooter);
            this.Name = "FRM_CUSTOMERS_NEW_WIZARD";
            this.Text = "Assistant de création - Nouveau client";
            this.Load += new System.EventHandler(this.FRM_CUSTOMERS_NEW_WIZARD_Load);
            ((System.ComponentModel.ISupportInitialize)(this.panelHeader)).EndInit();
            this.panelHeader.ResumeLayout(false);
            this.panelHeader.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelContent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelFooter)).EndInit();
            this.panelFooter.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonPanel panelHeader;
        private Krypton.Toolkit.KryptonLabel lblStepDescription;
        private Krypton.Toolkit.KryptonLabel lblStepTitle;
        private Krypton.Toolkit.KryptonPanel panelContent;
        private Krypton.Toolkit.KryptonPanel panelFooter;
        private Krypton.Toolkit.KryptonProgressBar kryptonProgressBar1;
        private Krypton.Toolkit.KryptonButton btnCancel;
        private Krypton.Toolkit.KryptonButton btnFinish;
        private Krypton.Toolkit.KryptonButton btnNext;
        private Krypton.Toolkit.KryptonButton btnBack;
    }
}