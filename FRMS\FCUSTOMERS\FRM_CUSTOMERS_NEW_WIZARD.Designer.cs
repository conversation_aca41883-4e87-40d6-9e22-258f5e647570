﻿namespace IRepairIT.FRMS.FCUSTOMERS
{
    partial class FRM_CUSTOMERS_NEW_WIZARD
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panelHeader = new Krypton.Toolkit.KryptonPanel();
            this.lblStepDescription = new Krypton.Toolkit.KryptonLabel();
            this.lblStepTitle = new Krypton.Toolkit.KryptonLabel();
            this.panelContent = new Krypton.Toolkit.KryptonPanel();
            this.panelFooter = new Krypton.Toolkit.KryptonPanel();
            this.kryptonProgressBar1 = new Krypton.Toolkit.KryptonProgressBar();
            this.btnCancel = new Krypton.Toolkit.KryptonButton();
            this.btnFinish = new Krypton.Toolkit.KryptonButton();
            this.btnNext = new Krypton.Toolkit.KryptonButton();
            this.btnBack = new Krypton.Toolkit.KryptonButton();

            // Customer Panel Controls (Step 1)
            this.panelStep1 = new Krypton.Toolkit.KryptonPanel();
            this.lblCustomerTitle = new Krypton.Toolkit.KryptonLabel();
            this.lblCustomerName = new Krypton.Toolkit.KryptonLabel();
            this.txtCustomerName = new Krypton.Toolkit.KryptonTextBox();
            this.lblCustomerPhone = new Krypton.Toolkit.KryptonLabel();
            this.txtCustomerPhone = new Krypton.Toolkit.KryptonTextBox();
            this.lblCustomerEmail = new Krypton.Toolkit.KryptonLabel();
            this.txtCustomerEmail = new Krypton.Toolkit.KryptonTextBox();
            this.lblCustomerAddress = new Krypton.Toolkit.KryptonLabel();
            this.txtCustomerAddress = new Krypton.Toolkit.KryptonTextBox();
            this.lblCustomerNotes = new Krypton.Toolkit.KryptonLabel();
            this.txtCustomerNotes = new Krypton.Toolkit.KryptonTextBox();

            // Device Panel Controls (Step 2)
            this.panelStep2 = new Krypton.Toolkit.KryptonPanel();
            this.lblDeviceTitle = new Krypton.Toolkit.KryptonLabel();
            this.lblDeviceType = new Krypton.Toolkit.KryptonLabel();
            this.cmbDeviceType = new Krypton.Toolkit.KryptonComboBox();
            this.lblDeviceBrand = new Krypton.Toolkit.KryptonLabel();
            this.txtDeviceBrand = new Krypton.Toolkit.KryptonTextBox();
            this.lblDeviceModel = new Krypton.Toolkit.KryptonLabel();
            this.txtDeviceModel = new Krypton.Toolkit.KryptonTextBox();
            this.lblSerialNumber = new Krypton.Toolkit.KryptonLabel();
            this.txtSerialNumber = new Krypton.Toolkit.KryptonTextBox();
            this.lblIMEI = new Krypton.Toolkit.KryptonLabel();
            this.txtIMEI = new Krypton.Toolkit.KryptonTextBox();
            this.lblDeviceProblem = new Krypton.Toolkit.KryptonLabel();
            this.txtDeviceProblem = new Krypton.Toolkit.KryptonTextBox();
            this.lblDeviceCondition = new Krypton.Toolkit.KryptonLabel();
            this.txtDeviceCondition = new Krypton.Toolkit.KryptonTextBox();
            this.lblDevicePassword = new Krypton.Toolkit.KryptonLabel();
            this.txtDevicePassword = new Krypton.Toolkit.KryptonTextBox();
            this.lblAccessories = new Krypton.Toolkit.KryptonLabel();
            this.txtAccessories = new Krypton.Toolkit.KryptonTextBox();
            this.lblExpectedDelivery = new Krypton.Toolkit.KryptonLabel();
            this.dtpExpectedDelivery = new Krypton.Toolkit.KryptonDateTimePicker();

            ((System.ComponentModel.ISupportInitialize)(this.panelHeader)).BeginInit();
            this.panelHeader.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelContent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelFooter)).BeginInit();
            this.panelFooter.SuspendLayout();
            this.SuspendLayout();
            // 
            // panelHeader
            // 
            this.panelHeader.Controls.Add(this.lblStepDescription);
            this.panelHeader.Controls.Add(this.lblStepTitle);
            this.panelHeader.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelHeader.Location = new System.Drawing.Point(0, 0);
            this.panelHeader.Name = "panelHeader";
            this.panelHeader.Padding = new System.Windows.Forms.Padding(20, 15, 20, 15);
            this.panelHeader.Size = new System.Drawing.Size(900, 85);
            this.panelHeader.StateCommon.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(248)))), ((int)(((byte)(255)))));
            this.panelHeader.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(240)))), ((int)(((byte)(250)))));
            this.panelHeader.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.panelHeader.TabIndex = 1;
            // 
            // lblStepDescription
            // 
            this.lblStepDescription.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.lblStepDescription.Location = new System.Drawing.Point(20, 49);
            this.lblStepDescription.Name = "lblStepDescription";
            this.lblStepDescription.Size = new System.Drawing.Size(860, 21);
            this.lblStepDescription.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblStepDescription.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblStepDescription.TabIndex = 1;
            this.lblStepDescription.Values.Text = "Saisissez les informations du client.";
            // 
            // lblStepTitle
            // 
            this.lblStepTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblStepTitle.Location = new System.Drawing.Point(20, 15);
            this.lblStepTitle.Name = "lblStepTitle";
            this.lblStepTitle.Size = new System.Drawing.Size(860, 34);
            this.lblStepTitle.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.lblStepTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 16F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblStepTitle.TabIndex = 0;
            this.lblStepTitle.Values.Text = "Étape 1: Informations du client";
            //
            // panelContent
            //
            this.panelContent.Controls.Add(this.panelStep2);
            this.panelContent.Controls.Add(this.panelStep1);
            this.panelContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelContent.Location = new System.Drawing.Point(0, 85);
            this.panelContent.Name = "panelContent";
            this.panelContent.Padding = new System.Windows.Forms.Padding(25, 20, 25, 20);
            this.panelContent.Size = new System.Drawing.Size(900, 335);
            this.panelContent.StateCommon.Color1 = System.Drawing.Color.White;
            this.panelContent.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(255)))));
            this.panelContent.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.panelContent.TabIndex = 2;
            // 
            // panelFooter
            // 
            this.panelFooter.Controls.Add(this.kryptonProgressBar1);
            this.panelFooter.Controls.Add(this.btnCancel);
            this.panelFooter.Controls.Add(this.btnFinish);
            this.panelFooter.Controls.Add(this.btnNext);
            this.panelFooter.Controls.Add(this.btnBack);
            this.panelFooter.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelFooter.Location = new System.Drawing.Point(0, 420);
            this.panelFooter.Name = "panelFooter";
            this.panelFooter.Padding = new System.Windows.Forms.Padding(20, 10, 20, 10);
            this.panelFooter.Size = new System.Drawing.Size(900, 70);
            this.panelFooter.StateCommon.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(245)))), ((int)(((byte)(245)))), ((int)(((byte)(245)))));
            this.panelFooter.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(235)))), ((int)(((byte)(235)))));
            this.panelFooter.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.panelFooter.TabIndex = 4;
            // 
            // kryptonProgressBar1
            // 
            this.kryptonProgressBar1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonProgressBar1.Location = new System.Drawing.Point(150, 30);
            this.kryptonProgressBar1.Name = "kryptonProgressBar1";
            this.kryptonProgressBar1.Size = new System.Drawing.Size(439, 12);
            this.kryptonProgressBar1.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.kryptonProgressBar1.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(150)))), ((int)(((byte)(155)))));
            this.kryptonProgressBar1.StateCommon.Back.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.kryptonProgressBar1.StateNormal.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(220)))), ((int)(((byte)(220)))));
            this.kryptonProgressBar1.StateNormal.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.kryptonProgressBar1.StateNormal.Back.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.kryptonProgressBar1.TabIndex = 17;
            this.kryptonProgressBar1.Value = 33;
            this.kryptonProgressBar1.Values.Text = "";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnCancel.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnCancel.Location = new System.Drawing.Point(20, 20);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(120, 40);
            this.btnCancel.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(220)))), ((int)(((byte)(220)))));
            this.btnCancel.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.btnCancel.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.btnCancel.StateCommon.Border.Rounding = 6F;
            this.btnCancel.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.TabIndex = 16;
            this.btnCancel.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnCancel.Values.Text = "Annuler";
            // 
            // btnFinish
            // 
            this.btnFinish.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnFinish.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnFinish.Location = new System.Drawing.Point(760, 20);
            this.btnFinish.Name = "btnFinish";
            this.btnFinish.Size = new System.Drawing.Size(120, 40);
            this.btnFinish.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.btnFinish.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.btnFinish.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.btnFinish.StateCommon.Border.Rounding = 6F;
            this.btnFinish.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnFinish.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnFinish.TabIndex = 15;
            this.btnFinish.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnFinish.Values.Text = "Terminer";
            // 
            // btnNext
            // 
            this.btnNext.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNext.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnNext.Location = new System.Drawing.Point(760, 20);
            this.btnNext.Name = "btnNext";
            this.btnNext.Size = new System.Drawing.Size(120, 40);
            this.btnNext.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.btnNext.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.btnNext.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.btnNext.StateCommon.Border.Rounding = 6F;
            this.btnNext.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnNext.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnNext.TabIndex = 14;
            this.btnNext.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnNext.Values.Text = "Suivant >";
            // 
            // btnBack
            // 
            this.btnBack.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBack.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnBack.Location = new System.Drawing.Point(630, 20);
            this.btnBack.Name = "btnBack";
            this.btnBack.Size = new System.Drawing.Size(120, 40);
            this.btnBack.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.btnBack.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(220)))), ((int)(((byte)(220)))));
            this.btnBack.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.btnBack.StateCommon.Border.Rounding = 6F;
            this.btnBack.StateCommon.Content.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBack.TabIndex = 13;
            this.btnBack.Values.DropDownArrowColor = System.Drawing.Color.Empty;
            this.btnBack.Values.Text = "< Retour";
            //
            // panelStep1
            //
            this.panelStep1.Controls.Add(this.txtCustomerNotes);
            this.panelStep1.Controls.Add(this.lblCustomerNotes);
            this.panelStep1.Controls.Add(this.txtCustomerAddress);
            this.panelStep1.Controls.Add(this.lblCustomerAddress);
            this.panelStep1.Controls.Add(this.txtCustomerEmail);
            this.panelStep1.Controls.Add(this.lblCustomerEmail);
            this.panelStep1.Controls.Add(this.txtCustomerPhone);
            this.panelStep1.Controls.Add(this.lblCustomerPhone);
            this.panelStep1.Controls.Add(this.txtCustomerName);
            this.panelStep1.Controls.Add(this.lblCustomerName);
            this.panelStep1.Controls.Add(this.lblCustomerTitle);
            this.panelStep1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelStep1.Location = new System.Drawing.Point(25, 20);
            this.panelStep1.Name = "panelStep1";
            this.panelStep1.Padding = new System.Windows.Forms.Padding(20);
            this.panelStep1.Size = new System.Drawing.Size(850, 295);
            this.panelStep1.StateCommon.Color1 = System.Drawing.Color.White;
            this.panelStep1.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(254)))), ((int)(((byte)(255)))));
            this.panelStep1.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.panelStep1.TabIndex = 0;
            //
            // lblCustomerTitle
            //
            this.lblCustomerTitle.Location = new System.Drawing.Point(20, 20);
            this.lblCustomerTitle.Name = "lblCustomerTitle";
            this.lblCustomerTitle.Size = new System.Drawing.Size(254, 28);
            this.lblCustomerTitle.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.lblCustomerTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerTitle.TabIndex = 0;
            this.lblCustomerTitle.Values.Text = "📋 Informations du client";
            //
            // lblCustomerName
            //
            this.lblCustomerName.Location = new System.Drawing.Point(20, 70);
            this.lblCustomerName.Name = "lblCustomerName";
            this.lblCustomerName.Size = new System.Drawing.Size(120, 20);
            this.lblCustomerName.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblCustomerName.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerName.TabIndex = 1;
            this.lblCustomerName.Values.Text = "Nom du client *:";
            //
            // txtCustomerName
            //
            this.txtCustomerName.CueHint.CueHintText = "Saisissez le nom complet";
            this.txtCustomerName.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtCustomerName.Location = new System.Drawing.Point(20, 95);
            this.txtCustomerName.Name = "txtCustomerName";
            this.txtCustomerName.Size = new System.Drawing.Size(300, 26);
            this.txtCustomerName.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtCustomerName.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtCustomerName.StateCommon.Border.Rounding = 4F;
            this.txtCustomerName.StateCommon.Border.Width = 1;
            this.txtCustomerName.StateCommon.Content.Color1 = System.Drawing.Color.White;
            this.txtCustomerName.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCustomerName.TabIndex = 2;
            //
            // lblCustomerPhone
            //
            this.lblCustomerPhone.Location = new System.Drawing.Point(350, 70);
            this.lblCustomerPhone.Name = "lblCustomerPhone";
            this.lblCustomerPhone.Size = new System.Drawing.Size(150, 20);
            this.lblCustomerPhone.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblCustomerPhone.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerPhone.TabIndex = 3;
            this.lblCustomerPhone.Values.Text = "Numéro de téléphone *:";
            //
            // txtCustomerPhone
            //
            this.txtCustomerPhone.CueHint.CueHintText = "Ex: +213 555 123 456";
            this.txtCustomerPhone.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtCustomerPhone.Location = new System.Drawing.Point(350, 95);
            this.txtCustomerPhone.Name = "txtCustomerPhone";
            this.txtCustomerPhone.Size = new System.Drawing.Size(250, 26);
            this.txtCustomerPhone.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtCustomerPhone.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtCustomerPhone.StateCommon.Border.Rounding = 4F;
            this.txtCustomerPhone.StateCommon.Border.Width = 1;
            this.txtCustomerPhone.StateCommon.Content.Color1 = System.Drawing.Color.White;
            this.txtCustomerPhone.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCustomerPhone.TabIndex = 4;
            //
            // lblCustomerEmail
            //
            this.lblCustomerEmail.Location = new System.Drawing.Point(20, 140);
            this.lblCustomerEmail.Name = "lblCustomerEmail";
            this.lblCustomerEmail.Size = new System.Drawing.Size(100, 20);
            this.lblCustomerEmail.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblCustomerEmail.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerEmail.TabIndex = 5;
            this.lblCustomerEmail.Values.Text = "Adresse email:";
            //
            // txtCustomerEmail
            //
            this.txtCustomerEmail.CueHint.CueHintText = "<EMAIL>";
            this.txtCustomerEmail.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtCustomerEmail.Location = new System.Drawing.Point(20, 165);
            this.txtCustomerEmail.Name = "txtCustomerEmail";
            this.txtCustomerEmail.Size = new System.Drawing.Size(300, 26);
            this.txtCustomerEmail.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtCustomerEmail.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtCustomerEmail.StateCommon.Border.Rounding = 4F;
            this.txtCustomerEmail.StateCommon.Border.Width = 1;
            this.txtCustomerEmail.StateCommon.Content.Color1 = System.Drawing.Color.White;
            this.txtCustomerEmail.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));

            this.txtCustomerEmail.TabIndex = 6;
            //
            // lblCustomerAddress
            //
            this.lblCustomerAddress.Location = new System.Drawing.Point(350, 140);
            this.lblCustomerAddress.Name = "lblCustomerAddress";
            this.lblCustomerAddress.Size = new System.Drawing.Size(120, 20);
            this.lblCustomerAddress.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblCustomerAddress.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerAddress.TabIndex = 7;
            this.lblCustomerAddress.Values.Text = "Adresse complète:";
            //
            // txtCustomerAddress
            //
            this.txtCustomerAddress.CueHint.CueHintText = "Adresse, ville, code postal";
            this.txtCustomerAddress.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtCustomerAddress.Location = new System.Drawing.Point(350, 165);
            this.txtCustomerAddress.Multiline = true;
            this.txtCustomerAddress.Name = "txtCustomerAddress";
            this.txtCustomerAddress.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtCustomerAddress.Size = new System.Drawing.Size(300, 60);
            this.txtCustomerAddress.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtCustomerAddress.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtCustomerAddress.StateCommon.Border.Rounding = 4F;
            this.txtCustomerAddress.StateCommon.Border.Width = 1;
            this.txtCustomerAddress.StateCommon.Content.Color1 = System.Drawing.Color.White;
            this.txtCustomerAddress.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));

            this.txtCustomerAddress.TabIndex = 8;
            //
            // lblCustomerNotes
            //
            this.lblCustomerNotes.Location = new System.Drawing.Point(20, 210);
            this.lblCustomerNotes.Name = "lblCustomerNotes";
            this.lblCustomerNotes.Size = new System.Drawing.Size(140, 20);
            this.lblCustomerNotes.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblCustomerNotes.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerNotes.TabIndex = 9;
            this.lblCustomerNotes.Values.Text = "Notes supplémentaires:";
            //
            // txtCustomerNotes
            //
            this.txtCustomerNotes.CueHint.CueHintText = "Informations additionnelles";
            this.txtCustomerNotes.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtCustomerNotes.Location = new System.Drawing.Point(20, 235);
            this.txtCustomerNotes.Multiline = true;
            this.txtCustomerNotes.Name = "txtCustomerNotes";
            this.txtCustomerNotes.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtCustomerNotes.Size = new System.Drawing.Size(630, 60);
            this.txtCustomerNotes.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtCustomerNotes.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtCustomerNotes.StateCommon.Border.Rounding = 4F;
            this.txtCustomerNotes.StateCommon.Border.Width = 1;
            this.txtCustomerNotes.StateCommon.Content.Color1 = System.Drawing.Color.White;
            this.txtCustomerNotes.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));

            this.txtCustomerNotes.TabIndex = 10;
            //
            // panelStep2
            //
            this.panelStep2.Controls.Add(this.dtpExpectedDelivery);
            this.panelStep2.Controls.Add(this.lblExpectedDelivery);
            this.panelStep2.Controls.Add(this.txtAccessories);
            this.panelStep2.Controls.Add(this.lblAccessories);
            this.panelStep2.Controls.Add(this.txtDevicePassword);
            this.panelStep2.Controls.Add(this.lblDevicePassword);
            this.panelStep2.Controls.Add(this.txtDeviceCondition);
            this.panelStep2.Controls.Add(this.lblDeviceCondition);
            this.panelStep2.Controls.Add(this.txtDeviceProblem);
            this.panelStep2.Controls.Add(this.lblDeviceProblem);
            this.panelStep2.Controls.Add(this.txtIMEI);
            this.panelStep2.Controls.Add(this.lblIMEI);
            this.panelStep2.Controls.Add(this.txtSerialNumber);
            this.panelStep2.Controls.Add(this.lblSerialNumber);
            this.panelStep2.Controls.Add(this.txtDeviceModel);
            this.panelStep2.Controls.Add(this.lblDeviceModel);
            this.panelStep2.Controls.Add(this.txtDeviceBrand);
            this.panelStep2.Controls.Add(this.lblDeviceBrand);
            this.panelStep2.Controls.Add(this.cmbDeviceType);
            this.panelStep2.Controls.Add(this.lblDeviceType);
            this.panelStep2.Controls.Add(this.lblDeviceTitle);
            this.panelStep2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelStep2.Location = new System.Drawing.Point(25, 20);
            this.panelStep2.Name = "panelStep2";
            this.panelStep2.Padding = new System.Windows.Forms.Padding(20);
            this.panelStep2.Size = new System.Drawing.Size(850, 295);
            this.panelStep2.StateCommon.Color1 = System.Drawing.Color.White;
            this.panelStep2.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(254)))), ((int)(((byte)(255)))));
            this.panelStep2.StateCommon.ColorStyle = Krypton.Toolkit.PaletteColorStyle.Linear;
            this.panelStep2.TabIndex = 1;
            this.panelStep2.Visible = false;
            //
            // lblDeviceTitle
            //
            this.lblDeviceTitle.Location = new System.Drawing.Point(20, 20);
            this.lblDeviceTitle.Name = "lblDeviceTitle";
            this.lblDeviceTitle.Size = new System.Drawing.Size(280, 28);
            this.lblDeviceTitle.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(125)))), ((int)(((byte)(128)))));
            this.lblDeviceTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDeviceTitle.TabIndex = 0;
            this.lblDeviceTitle.Values.Text = "🔧 Informations de l'appareil";
            //
            // lblDeviceType
            //
            this.lblDeviceType.Location = new System.Drawing.Point(20, 70);
            this.lblDeviceType.Name = "lblDeviceType";
            this.lblDeviceType.Size = new System.Drawing.Size(130, 20);
            this.lblDeviceType.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblDeviceType.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDeviceType.TabIndex = 1;
            this.lblDeviceType.Values.Text = "Type d'appareil *:";
            //
            // cmbDeviceType
            //
            this.cmbDeviceType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbDeviceType.Location = new System.Drawing.Point(20, 95);
            this.cmbDeviceType.Name = "cmbDeviceType";
            this.cmbDeviceType.Size = new System.Drawing.Size(250, 26);
            this.cmbDeviceType.StateCommon.ComboBox.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.cmbDeviceType.StateCommon.ComboBox.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.cmbDeviceType.StateCommon.ComboBox.Border.Rounding = 4F;
            this.cmbDeviceType.StateCommon.ComboBox.Border.Width = 1;
            this.cmbDeviceType.StateCommon.ComboBox.Content.Color1 = System.Drawing.Color.White;
            this.cmbDeviceType.StateCommon.ComboBox.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));

            this.cmbDeviceType.TabIndex = 2;
            //
            // lblDeviceBrand
            //
            this.lblDeviceBrand.Location = new System.Drawing.Point(290, 70);
            this.lblDeviceBrand.Name = "lblDeviceBrand";
            this.lblDeviceBrand.Size = new System.Drawing.Size(70, 20);
            this.lblDeviceBrand.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblDeviceBrand.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDeviceBrand.TabIndex = 3;
            this.lblDeviceBrand.Values.Text = "Marque *:";
            //
            // txtDeviceBrand
            //
            this.txtDeviceBrand.CueHint.CueHintText = "Ex: Samsung, Apple, HP";
            this.txtDeviceBrand.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtDeviceBrand.Location = new System.Drawing.Point(290, 95);
            this.txtDeviceBrand.Name = "txtDeviceBrand";
            this.txtDeviceBrand.Size = new System.Drawing.Size(200, 26);
            this.txtDeviceBrand.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtDeviceBrand.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtDeviceBrand.StateCommon.Border.Rounding = 4F;
            this.txtDeviceBrand.StateCommon.Border.Width = 1;
            this.txtDeviceBrand.StateCommon.Content.Color1 = System.Drawing.Color.White;
            this.txtDeviceBrand.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDeviceBrand.TabIndex = 4;
            //
            // lblDeviceModel
            //
            this.lblDeviceModel.Location = new System.Drawing.Point(510, 70);
            this.lblDeviceModel.Name = "lblDeviceModel";
            this.lblDeviceModel.Size = new System.Drawing.Size(70, 20);
            this.lblDeviceModel.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(100)))), ((int)(((byte)(105)))));
            this.lblDeviceModel.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDeviceModel.TabIndex = 5;
            this.lblDeviceModel.Values.Text = "Modèle *:";
            //
            // txtDeviceModel
            //
            this.txtDeviceModel.CueHint.CueHintText = "Ex: Galaxy S21, iPhone 13";
            this.txtDeviceModel.CueHint.Padding = new System.Windows.Forms.Padding(0);
            this.txtDeviceModel.Location = new System.Drawing.Point(510, 95);
            this.txtDeviceModel.Name = "txtDeviceModel";
            this.txtDeviceModel.Size = new System.Drawing.Size(200, 26);
            this.txtDeviceModel.StateCommon.Border.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(200)))), ((int)(((byte)(200)))));
            this.txtDeviceModel.StateCommon.Border.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.txtDeviceModel.StateCommon.Border.Rounding = 4F;
            this.txtDeviceModel.StateCommon.Border.Width = 1;
            this.txtDeviceModel.StateCommon.Content.Color1 = System.Drawing.Color.White;
            this.txtDeviceModel.StateCommon.Content.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDeviceModel.TabIndex = 6;
            //
            // FRM_CUSTOMERS_NEW_WIZARD
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(900, 490);
            this.Controls.Add(this.panelContent);
            this.Controls.Add(this.panelHeader);
            this.Controls.Add(this.panelFooter);
            this.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FRM_CUSTOMERS_NEW_WIZARD";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Assistant de création - Nouveau client et ordre de réparation";
            this.Load += new System.EventHandler(this.FRM_CUSTOMERS_NEW_WIZARD_Load);
            ((System.ComponentModel.ISupportInitialize)(this.panelHeader)).EndInit();
            this.panelHeader.ResumeLayout(false);
            this.panelHeader.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelContent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelFooter)).EndInit();
            this.panelFooter.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Krypton.Toolkit.KryptonPanel panelHeader;
        private Krypton.Toolkit.KryptonLabel lblStepDescription;
        private Krypton.Toolkit.KryptonLabel lblStepTitle;
        private Krypton.Toolkit.KryptonPanel panelContent;
        private Krypton.Toolkit.KryptonPanel panelFooter;
        private Krypton.Toolkit.KryptonProgressBar kryptonProgressBar1;
        private Krypton.Toolkit.KryptonButton btnCancel;
        private Krypton.Toolkit.KryptonButton btnFinish;
        private Krypton.Toolkit.KryptonButton btnNext;
        private Krypton.Toolkit.KryptonButton btnBack;

        // Customer Panel Controls
        private Krypton.Toolkit.KryptonPanel panelStep1;
        private Krypton.Toolkit.KryptonLabel lblCustomerTitle;
        private Krypton.Toolkit.KryptonLabel lblCustomerName;
        private Krypton.Toolkit.KryptonTextBox txtCustomerName;
        private Krypton.Toolkit.KryptonLabel lblCustomerPhone;
        private Krypton.Toolkit.KryptonTextBox txtCustomerPhone;
        private Krypton.Toolkit.KryptonLabel lblCustomerEmail;
        private Krypton.Toolkit.KryptonTextBox txtCustomerEmail;
        private Krypton.Toolkit.KryptonLabel lblCustomerAddress;
        private Krypton.Toolkit.KryptonTextBox txtCustomerAddress;
        private Krypton.Toolkit.KryptonLabel lblCustomerNotes;
        private Krypton.Toolkit.KryptonTextBox txtCustomerNotes;

        // Device Panel Controls
        private Krypton.Toolkit.KryptonPanel panelStep2;
        private Krypton.Toolkit.KryptonLabel lblDeviceTitle;
        private Krypton.Toolkit.KryptonLabel lblDeviceType;
        private Krypton.Toolkit.KryptonComboBox cmbDeviceType;
        private Krypton.Toolkit.KryptonLabel lblDeviceBrand;
        private Krypton.Toolkit.KryptonTextBox txtDeviceBrand;
        private Krypton.Toolkit.KryptonLabel lblDeviceModel;
        private Krypton.Toolkit.KryptonTextBox txtDeviceModel;
        private Krypton.Toolkit.KryptonLabel lblSerialNumber;
        private Krypton.Toolkit.KryptonTextBox txtSerialNumber;
        private Krypton.Toolkit.KryptonLabel lblIMEI;
        private Krypton.Toolkit.KryptonTextBox txtIMEI;
        private Krypton.Toolkit.KryptonLabel lblDeviceProblem;
        private Krypton.Toolkit.KryptonTextBox txtDeviceProblem;
        private Krypton.Toolkit.KryptonLabel lblDeviceCondition;
        private Krypton.Toolkit.KryptonTextBox txtDeviceCondition;
        private Krypton.Toolkit.KryptonLabel lblDevicePassword;
        private Krypton.Toolkit.KryptonTextBox txtDevicePassword;
        private Krypton.Toolkit.KryptonLabel lblAccessories;
        private Krypton.Toolkit.KryptonTextBox txtAccessories;
        private Krypton.Toolkit.KryptonLabel lblExpectedDelivery;
        private Krypton.Toolkit.KryptonDateTimePicker dtpExpectedDelivery;
    }
}