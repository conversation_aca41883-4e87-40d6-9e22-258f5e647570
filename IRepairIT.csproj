<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C57C0B34-6DC2-4B1C-ACCE-FE3EF66E9747}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>IRepairIT</RootNamespace>
    <AssemblyName>IRepairIT</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>logo.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AForge, Version=2.2.5.0, Culture=neutral, PublicKeyToken=c1db6ff4eaa06aeb, processorArchitecture=MSIL">
      <HintPath>packages\AForge.2.2.5\lib\AForge.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Video, Version=2.2.5.0, Culture=neutral, PublicKeyToken=cbfb6e07d173c401, processorArchitecture=MSIL">
      <HintPath>packages\AForge.Video.2.2.5\lib\AForge.Video.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Video.DirectShow, Version=2.2.5.0, Culture=neutral, PublicKeyToken=61ea4348d43881b7, processorArchitecture=MSIL">
      <HintPath>packages\AForge.Video.DirectShow.2.2.5\lib\AForge.Video.DirectShow.dll</HintPath>
    </Reference>
    <Reference Include="Dapper, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\Dapper.2.1.66\lib\net461\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="FastReport, Version=2020.3.7.0, Culture=neutral, PublicKeyToken=db7e5ce63278458c" />
    <Reference Include="FastReport.Bars, Version=2020.3.7.0, Culture=neutral, PublicKeyToken=db7e5ce63278458c" />
    <Reference Include="FastReport.Compat, Version=2020.3.2.0, Culture=neutral, PublicKeyToken=406e1f4c3c8ef97e" />
    <Reference Include="FastReport.DataVisualization, Version=2020.3.1.0, Culture=neutral, PublicKeyToken=5ceb240df42bf6e8" />
    <Reference Include="FastReport.Editor, Version=2020.3.7.0, Culture=neutral, PublicKeyToken=db7e5ce63278458c" />
    <Reference Include="Krypton.Navigator, Version=90.24.11.317, Culture=neutral, PublicKeyToken=a87e673e9ecb6e8e, processorArchitecture=MSIL">
      <HintPath>packages\Krypton.Navigator.90.24.11.317\lib\net462\Krypton.Navigator.dll</HintPath>
    </Reference>
    <Reference Include="Krypton.Toolkit, Version=90.24.11.317, Culture=neutral, PublicKeyToken=a87e673e9ecb6e8e, processorArchitecture=MSIL">
      <HintPath>packages\Krypton.Toolkit.90.24.11.317\lib\net462\Krypton.Toolkit.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=9.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Bcl.AsyncInterfaces.9.0.1\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data, Version=9.2.0.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\MySQL\MySQL Connector NET 9.2\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.CompilerServices.Unsafe.4.5.3\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Data\Common\DeviceCommands.cs" />
    <Compile Include="Data\Common\InventoryCommands.cs" />
    <Compile Include="Data\Common\DashboardCommands.cs" />
    <Compile Include="Data\Common\DataAccess.cs" />
    <Compile Include="Data\Common\DebtCommands.cs" />
    <Compile Include="Data\Common\CustomerCommands.cs" />
    <Compile Include="Data\Common\PartCommands.cs" />
    <Compile Include="Data\Common\RepairOrderCommands.cs" />
    <Compile Include="Data\Common\ServiceCommands.cs" />
    <Compile Include="Data\Common\ShopInfoCommands.cs" />
    <Compile Include="Data\Common\UserCommands.cs" />
    <Compile Include="Data\DatabaseManager.cs" />
    <Compile Include="DB\ConnectionSettings.cs" />
    <Compile Include="Enums\ItemType.cs" />
    <Compile Include="Enums\PaymentStatus.cs" />
    <Compile Include="Enums\RepairOrderStatus.cs" />
    <Compile Include="Enums\TransactionType.cs" />
    <Compile Include="Enums\UserRoles.cs" />
    <Compile Include="Enums\UserStatus.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FCOMMON\FRM_SELECT_CUSTOMER.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FCOMMON\FRM_SELECT_CUSTOMER.Designer.cs">
      <DependentUpon>FRM_SELECT_CUSTOMER.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FCOMMON\FRM_SELECT_ITEM.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FCOMMON\FRM_SELECT_ITEM.Designer.cs">
      <DependentUpon>FRM_SELECT_ITEM.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FCUSTOMERS\FRM_CUSTOMER.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FCUSTOMERS\FRM_CUSTOMER.Designer.cs">
      <DependentUpon>FRM_CUSTOMER.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FCUSTOMERS\FRM_CUSTOMERS_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FCUSTOMERS\FRM_CUSTOMERS_LIST.Designer.cs">
      <DependentUpon>FRM_CUSTOMERS_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FCUSTOMERS\FRM_CUSTOMERS_NEW_WIZARD.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FCUSTOMERS\FRM_CUSTOMERS_NEW_WIZARD.Designer.cs">
      <DependentUpon>FRM_CUSTOMERS_NEW_WIZARD.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FDB\FRM_DATABASELIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FDB\FRM_DATABASELIST.Designer.cs">
      <DependentUpon>FRM_DATABASELIST.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FDB\FRM_DBCONNECTIONSTRING.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FDB\FRM_DBCONNECTIONSTRING.Designer.cs">
      <DependentUpon>FRM_DBCONNECTIONSTRING.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FDB\FRM_SETUPDB.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FDB\FRM_SETUPDB.Designer.cs">
      <DependentUpon>FRM_SETUPDB.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FDB\FRM_YEARINPUT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FDB\FRM_YEARINPUT.Designer.cs">
      <DependentUpon>FRM_YEARINPUT.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FDEVICES\FRM_DEVICE.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FDEVICES\FRM_DEVICE.Designer.cs">
      <DependentUpon>FRM_DEVICE.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FDEVICES\FRM_DEVICES_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FDEVICES\FRM_DEVICES_LIST.Designer.cs">
      <DependentUpon>FRM_DEVICES_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FDEVICES\FRM_WEBCAM_CAPTURE.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FDEVICES\FRM_WEBCAM_CAPTURE.Designer.cs">
      <DependentUpon>FRM_WEBCAM_CAPTURE.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FDEBTS\FRM_DEBT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FDEBTS\FRM_DEBT.Designer.cs">
      <DependentUpon>FRM_DEBT.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FDEBTS\FRM_DEBTS_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FDEBTS\FRM_DEBTS_LIST.Designer.cs">
      <DependentUpon>FRM_DEBTS_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FREPORTS\FRM_REPORTS_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FREPORTS\FRM_REPORTS_LIST.Designer.cs">
      <DependentUpon>FRM_REPORTS_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FREPORTS\FRM_SALES_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FREPORTS\FRM_SALES_REPORT.Designer.cs">
      <DependentUpon>FRM_SALES_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FINVENTORY\FRM_INVENTORY.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FINVENTORY\FRM_INVENTORY.Designer.cs">
      <DependentUpon>FRM_INVENTORY.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FINVENTORY\FRM_INVENTORY_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FINVENTORY\FRM_INVENTORY_LIST.Designer.cs">
      <DependentUpon>FRM_INVENTORY_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FINVENTORY\FRM_INVENTORY_TRANSACTIONS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FINVENTORY\FRM_INVENTORY_TRANSACTIONS.Designer.cs">
      <DependentUpon>FRM_INVENTORY_TRANSACTIONS.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FINVENTORY\FRM_PART_SELECTOR.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FINVENTORY\FRM_PART_SELECTOR.Designer.cs">
      <DependentUpon>FRM_PART_SELECTOR.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FLOGIN\FRM_CHANGEPASSWORD.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FLOGIN\FRM_CHANGEPASSWORD.Designer.cs">
      <DependentUpon>FRM_CHANGEPASSWORD.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FLOGIN\FRM_LOGIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FLOGIN\FRM_LOGIN.Designer.cs">
      <DependentUpon>FRM_LOGIN.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FLOGIN\FRM_PASSWORDINPUT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FLOGIN\FRM_PASSWORDINPUT.Designer.cs">
      <DependentUpon>FRM_PASSWORDINPUT.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FMAIN\FRM_DASHBOARD.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FMAIN\FRM_DASHBOARD.Designer.cs">
      <DependentUpon>FRM_DASHBOARD.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FMAIN\FRM_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FMAIN\FRM_MAIN.Designer.cs">
      <DependentUpon>FRM_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FMAIN\FRM_SESSION_INFO.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FMAIN\FRM_SESSION_INFO.Designer.cs">
      <DependentUpon>FRM_SESSION_INFO.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FMAIN\FRM_SESSION_PASSWORD.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FMAIN\FRM_SESSION_PASSWORD.Designer.cs">
      <DependentUpon>FRM_SESSION_PASSWORD.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FREPAIRS\FRM_PAYMENT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FREPAIRS\FRM_PAYMENT.Designer.cs">
      <DependentUpon>FRM_PAYMENT.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FREPAIRS\FRM_REPAIR_ORDER.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FREPAIRS\FRM_REPAIR_ORDER.Designer.cs">
      <DependentUpon>FRM_REPAIR_ORDER.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FREPAIRS\FRM_REPAIR_ORDERS_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FREPAIRS\FRM_REPAIR_ORDERS_LIST.Designer.cs">
      <DependentUpon>FRM_REPAIR_ORDERS_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FSERVICES\FRM_SERVICES.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FSERVICES\FRM_SERVICES.Designer.cs">
      <DependentUpon>FRM_SERVICES.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FSERVICES\FRM_SERVICES_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FSERVICES\FRM_SERVICES_LIST.Designer.cs">
      <DependentUpon>FRM_SERVICES_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FSERVICES\FRM_SERVICE_SELECTOR.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FSERVICES\FRM_SERVICE_SELECTOR.Designer.cs">
      <DependentUpon>FRM_SERVICE_SELECTOR.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FSHOP\FRM_SHOP.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FSHOP\FRM_SHOP.Designer.cs">
      <DependentUpon>FRM_SHOP.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FUSERS\FRM_USERS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FUSERS\FRM_USERS.Designer.cs">
      <DependentUpon>FRM_USERS.cs</DependentUpon>
    </Compile>
    <Compile Include="FRMS\FUSERS\FRM_USERS_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FUSERS\FRM_USERS_LIST.Designer.cs">
      <DependentUpon>FRM_USERS_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="Models\Customer.cs" />
    <Compile Include="Models\Debt.cs" />
    <Compile Include="Models\Device.cs" />
    <Compile Include="Models\DeviceImage.cs" />
    <Compile Include="Models\DTO\LatestCustomerDto.cs" />
    <Compile Include="Models\DTO\LatestDto.cs" />
    <Compile Include="Models\DTO\PagedUsers.cs" />
    <Compile Include="Models\Expense.cs" />
    <Compile Include="Models\InventoryTransaction.cs" />
    <Compile Include="Models\Part.cs" />
    <Compile Include="Models\Payment.cs" />
    <Compile Include="Models\Repair.cs" />
    <Compile Include="Models\RepairOrder.cs" />
    <Compile Include="Models\RepairOrderItem.cs" />
    <Compile Include="Models\RepairOrderPart.cs" />
    <Compile Include="Models\RepairOrderService.cs" />
    <Compile Include="Models\Service.cs" />
    <Compile Include="Models\Setting.cs" />
    <Compile Include="Models\ShopInfo.cs" />
    <Compile Include="Models\StatusHistory.cs" />
    <Compile Include="Models\User.cs" />
    <Compile Include="Models\UserSession.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="FRMS\FSPLASH\FRM_SPLASH.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRMS\FSPLASH\FRM_SPLASH.Designer.cs">
      <DependentUpon>FRM_SPLASH.cs</DependentUpon>
    </Compile>
    <Compile Include="utilities\DGV_STYLE.cs" />
    <Compile Include="utilities\EntityValidator.cs" />
    <Compile Include="utilities\TabManager.cs" />
    <Compile Include="utilities\WebcamCapture.cs" />
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FCOMMON\FRM_SELECT_CUSTOMER.resx">
      <DependentUpon>FRM_SELECT_CUSTOMER.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FCOMMON\FRM_SELECT_ITEM.resx">
      <DependentUpon>FRM_SELECT_ITEM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FCUSTOMERS\FRM_CUSTOMER.resx">
      <DependentUpon>FRM_CUSTOMER.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FCUSTOMERS\FRM_CUSTOMERS_LIST.resx">
      <DependentUpon>FRM_CUSTOMERS_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FCUSTOMERS\FRM_CUSTOMERS_NEW_WIZARD.resx">
      <DependentUpon>FRM_CUSTOMERS_NEW_WIZARD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FDB\FRM_DATABASELIST.resx">
      <DependentUpon>FRM_DATABASELIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FDB\FRM_DBCONNECTIONSTRING.resx">
      <DependentUpon>FRM_DBCONNECTIONSTRING.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FDB\FRM_SETUPDB.resx">
      <DependentUpon>FRM_SETUPDB.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FDB\FRM_YEARINPUT.resx">
      <DependentUpon>FRM_YEARINPUT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FDEVICES\FRM_DEVICES_LIST.resx">
      <DependentUpon>FRM_DEVICES_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FDEBTS\FRM_DEBT.resx">
      <DependentUpon>FRM_DEBT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FDEBTS\FRM_DEBTS_LIST.resx">
      <DependentUpon>FRM_DEBTS_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FDEVICES\FRM_WEBCAM_CAPTURE.resx">
      <DependentUpon>FRM_WEBCAM_CAPTURE.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FREPAIRS\FRM_REPAIR_ORDERS_LIST.resx">
      <DependentUpon>FRM_REPAIR_ORDERS_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FREPORTS\FRM_REPORTS_LIST.resx">
      <DependentUpon>FRM_REPORTS_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FREPORTS\FRM_SALES_REPORT.resx">
      <DependentUpon>FRM_SALES_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FINVENTORY\FRM_INVENTORY.resx">
      <DependentUpon>FRM_INVENTORY.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FINVENTORY\FRM_INVENTORY_LIST.resx">
      <DependentUpon>FRM_INVENTORY_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FINVENTORY\FRM_PART_SELECTOR.resx">
      <DependentUpon>FRM_PART_SELECTOR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FLOGIN\FRM_CHANGEPASSWORD.resx">
      <DependentUpon>FRM_CHANGEPASSWORD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FLOGIN\FRM_LOGIN.resx">
      <DependentUpon>FRM_LOGIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FLOGIN\FRM_PASSWORDINPUT.resx">
      <DependentUpon>FRM_PASSWORDINPUT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FMAIN\FRM_DASHBOARD.resx">
      <DependentUpon>FRM_DASHBOARD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FMAIN\FRM_MAIN.resx">
      <DependentUpon>FRM_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FMAIN\FRM_SESSION_INFO.resx">
      <DependentUpon>FRM_SESSION_INFO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FMAIN\FRM_SESSION_PASSWORD.resx">
      <DependentUpon>FRM_SESSION_PASSWORD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FREPAIRS\FRM_PAYMENT.resx">
      <DependentUpon>FRM_PAYMENT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FREPAIRS\FRM_REPAIR_ORDER.resx">
      <DependentUpon>FRM_REPAIR_ORDER.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FSERVICES\FRM_SERVICES.resx">
      <DependentUpon>FRM_SERVICES.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FSERVICES\FRM_SERVICES_LIST.resx">
      <DependentUpon>FRM_SERVICES_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FSERVICES\FRM_SERVICE_SELECTOR.resx">
      <DependentUpon>FRM_SERVICE_SELECTOR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FSHOP\FRM_SHOP.resx">
      <DependentUpon>FRM_SHOP.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FUSERS\FRM_USERS.resx">
      <DependentUpon>FRM_USERS.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRMS\FUSERS\FRM_USERS_LIST.resx">
      <DependentUpon>FRM_USERS_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="FRMS\FSPLASH\FRM_SPLASH.resx">
      <DependentUpon>FRM_SPLASH.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.6.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.6.2 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Content Include="DB\db.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="logo.ico" />
    <None Include="Reports\CustomersList.frx" />
    <None Include="Reports\RepairOrderReceipt.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\RepairOrderReceiptA4.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\RepairOrderReceiptA5.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\UsersList.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\DebtsReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\PaymentsReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\CustomerDebtsReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\OverdueDebtsReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\RepairsReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\MonthlyRepairsReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\MonthlyRevenueReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\TotalOrdersReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\TotalServicesReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\TotalPartsReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\TopServicesReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\TopPartsReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Reports\SalesDataReport.frx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Resources\tools.png" />
    <None Include="Resources\give-money-2.png" />
    <None Include="Resources\tablet-iphone.png" />
    <None Include="Resources\people-customers.png" />
    <None Include="Resources\tool-pencil.png" />
    <None Include="Resources\trash.png" />
    <None Include="Resources\close-window.png" />
    <None Include="Resources\refresh.png" />
    <None Include="Resources\folder.png" />
    <None Include="Resources\database-network.png" />
    <None Include="Resources\button-circle-add.png" />
    <None Include="Resources\eye-no.png" />
    <None Include="Resources\eye.png" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>