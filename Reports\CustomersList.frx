<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="01/01/2023 00:00:00" ReportInfo.Modified="01/01/2023 00:00:00" ReportInfo.CreatorVersion="*******">
  <Dictionary>
    <TableDataSource Name="Customers" ReferenceName="Customers">
      <Column Name="Nom" DataType="System.String"/>
      <Column Name="Telephone" DataType="System.String"/>
      <Column Name="Email" DataType="System.String"/>
      <Column Name="DateInscription" DataType="System.String"/>
    </TableDataSource>
    <Parameter Name="TotalCustomers" DataType="System.String"/>
    <Parameter Name="Date" DataType="System.String"/>
  </Dictionary>
  <ReportPage Name="Page1" Landscape="false" PaperWidth="210" PaperHeight="297" RawPaperSize="9" FirstPageSource="15" OtherPagesSource="15">
    <ReportTitleBand Name="ReportTitle1" Width="718.2" Height="75.6">
      <TextObject Name="Text1" Width="718.2" Height="28.35" Text="Liste des Clients" HorzAlign="Center" VertAlign="Center" Font="Arial, 14pt, style=Bold"/>
      <TextObject Name="Text2" Left="529.2" Top="28.35" Width="189" Height="18.9" Text="Date: [Date]" HorzAlign="Right" Font="Arial, 10pt"/>
      <TextObject Name="Text3" Top="47.25" Width="718.2" Height="18.9" Text="Nombre total de clients: [TotalCustomers]" Font="Arial, 10pt"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="79.6" Width="718.2" Height="28.35" Fill.Color="WhiteSmoke">
      <TextObject Name="Header0" Width="179.55" Height="28.35" Text="Nom" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold" Border.Lines="All"/>
      <TextObject Name="Header1" Left="179.55" Width="179.55" Height="28.35" Text="Téléphone" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold" Border.Lines="All"/>
      <TextObject Name="Header2" Left="359.1" Width="179.55" Height="28.35" Text="Email" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold" Border.Lines="All"/>
      <TextObject Name="Header3" Left="538.65" Width="179.55" Height="28.35" Text="Date d'inscription" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold" Border.Lines="All"/>
    </PageHeaderBand>
    <DataBand Name="Data1" Top="111.95" Width="718.2" Height="18.9" DataSource="Customers">
      <TextObject Name="Data0" Width="179.55" Height="18.9" Text="[Customers.Nom]" HorzAlign="Center" VertAlign="Center" Font="Arial, 9pt" Border.Lines="All"/>
      <TextObject Name="Data1" Left="179.55" Width="179.55" Height="18.9" Text="[Customers.Telephone]" HorzAlign="Center" VertAlign="Center" Font="Arial, 9pt" Border.Lines="All"/>
      <TextObject Name="Data2" Left="359.1" Width="179.55" Height="18.9" Text="[Customers.Email]" HorzAlign="Center" VertAlign="Center" Font="Arial, 9pt" Border.Lines="All"/>
      <TextObject Name="Data3" Left="538.65" Width="179.55" Height="18.9" Text="[Customers.DateInscription]" HorzAlign="Center" VertAlign="Center" Font="Arial, 9pt" Border.Lines="All"/>
    </DataBand>
    <PageFooterBand Name="PageFooter1" Top="134.85" Width="718.2" Height="18.9">
      <TextObject Name="Text4" Width="718.2" Height="18.9" Text="Page [Page] de [TotalPages]" HorzAlign="Right" Font="Arial, 8pt"/>
    </PageFooterBand>
  </ReportPage>
</Report>
