﻿﻿namespace IRepairIT.FRMS.FDEVICES
{
    partial class FRM_DEVICES_LIST
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FRM_DEVICES_LIST));
            this.btnFirst = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnPrev = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnNext = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnLast = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.kryptonComboBox1 = new Krypton.Toolkit.KryptonComboBox();
            this.kryptonHeaderGroup1 = new Krypton.Toolkit.KryptonHeaderGroup();
            this.btnAdd = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnEdit = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnDelete = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnSelect = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.kryptonDataGridView1 = new Krypton.Toolkit.KryptonDataGridView();
            this.TXTSearch = new Krypton.Toolkit.KryptonTextBox();
            this.btnSearch = new Krypton.Toolkit.ButtonSpecAny();
            this.btnClearSearch = new Krypton.Toolkit.ButtonSpecAny();
            this.kryptonLabel1 = new Krypton.Toolkit.KryptonLabel();
            this.btnAdvancedSearch = new Krypton.Toolkit.KryptonButton();
            this.panelAdvancedSearch = new Krypton.Toolkit.KryptonPanel();
            this.cmbDeviceType = new Krypton.Toolkit.KryptonComboBox();
            this.cmbBrand = new Krypton.Toolkit.KryptonComboBox();
            this.cmbClient = new Krypton.Toolkit.KryptonComboBox();
            this.dtpStartDate = new Krypton.Toolkit.KryptonDateTimePicker();
            this.dtpEndDate = new Krypton.Toolkit.KryptonDateTimePicker();
            this.lblDeviceType = new Krypton.Toolkit.KryptonLabel();
            this.lblBrand = new Krypton.Toolkit.KryptonLabel();
            this.lblClient = new Krypton.Toolkit.KryptonLabel();
            this.lblStartDate = new Krypton.Toolkit.KryptonLabel();
            this.lblEndDate = new Krypton.Toolkit.KryptonLabel();
            this.btnApplyFilters = new Krypton.Toolkit.KryptonButton();
            this.btnResetFilters = new Krypton.Toolkit.KryptonButton();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonComboBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1.Panel)).BeginInit();
            this.kryptonHeaderGroup1.Panel.SuspendLayout();
            this.kryptonHeaderGroup1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelAdvancedSearch)).BeginInit();
            this.panelAdvancedSearch.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDeviceType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbBrand)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbClient)).BeginInit();
            this.SuspendLayout();
            //
            // btnFirst
            //
            this.btnFirst.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowLeft;
            this.btnFirst.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            //
            // btnPrev
            //
            this.btnPrev.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowLeft;
            this.btnPrev.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            //
            // btnNext
            //
            this.btnNext.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowRight;
            this.btnNext.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            //
            // btnLast
            //
            this.btnLast.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowRight;
            this.btnLast.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            //
            // kryptonComboBox1
            //
            this.kryptonComboBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonComboBox1.DropDownWidth = 121;
            this.kryptonComboBox1.Location = new System.Drawing.Point(1000, 12);
            this.kryptonComboBox1.Name = "kryptonComboBox1";
            this.kryptonComboBox1.Size = new System.Drawing.Size(121, 25);
            this.kryptonComboBox1.TabIndex = 2;
            //
            // kryptonHeaderGroup1
            //
            this.kryptonHeaderGroup1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonHeaderGroup1.ButtonSpecs.AddRange(new Krypton.Toolkit.ButtonSpecHeaderGroup[] {
            this.btnFirst,
            this.btnPrev,
            this.btnNext,
            this.btnLast,
            this.btnAdd,
            this.btnEdit,
            this.btnDelete});
            this.kryptonHeaderGroup1.HeaderVisibleSecondary = false;
            this.kryptonHeaderGroup1.Location = new System.Drawing.Point(12, 43);
            this.kryptonHeaderGroup1.Name = "kryptonHeaderGroup1";
            //
            // kryptonHeaderGroup1.Panel
            //
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.kryptonDataGridView1);
            this.kryptonHeaderGroup1.Size = new System.Drawing.Size(1109, 606);
            this.kryptonHeaderGroup1.TabIndex = 3;
            this.kryptonHeaderGroup1.ValuesPrimary.Heading = "Appareils";
            this.kryptonHeaderGroup1.ValuesPrimary.Image = global::IRepairIT.Properties.Resources.tablet_iphone;
            this.kryptonHeaderGroup1.ValuesSecondary.Heading = "Total: 0 - Page: 1/1";
            //
            // btnAdd
            //
            this.btnAdd.Image = global::IRepairIT.Properties.Resources.button_circle_add;
            this.btnAdd.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            //
            // btnEdit
            //
            this.btnEdit.Image = global::IRepairIT.Properties.Resources.tool_pencil;
            this.btnEdit.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            //
            // btnDelete
            //
            this.btnDelete.Image = global::IRepairIT.Properties.Resources.trash;
            this.btnDelete.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            //
            // kryptonDataGridView1
            //
            this.kryptonDataGridView1.AllowUserToAddRows = false;
            this.kryptonDataGridView1.AllowUserToDeleteRows = false;
            this.kryptonDataGridView1.AllowUserToResizeRows = false;
            this.kryptonDataGridView1.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.kryptonDataGridView1.ColumnHeadersHeight = 36;
            this.kryptonDataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonDataGridView1.Location = new System.Drawing.Point(0, 0);
            this.kryptonDataGridView1.MultiSelect = false;
            this.kryptonDataGridView1.Name = "kryptonDataGridView1";
            this.kryptonDataGridView1.ReadOnly = true;
            this.kryptonDataGridView1.RowHeadersVisible = false;
            this.kryptonDataGridView1.RowHeadersWidth = 51;
            this.kryptonDataGridView1.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.kryptonDataGridView1.Size = new System.Drawing.Size(1107, 574);
            this.kryptonDataGridView1.TabIndex = 0;
            //
            // TXTSearch
            //
            this.TXTSearch.ButtonSpecs.AddRange(new Krypton.Toolkit.ButtonSpecAny[] {
            this.btnSearch,
            this.btnClearSearch});
            this.TXTSearch.Location = new System.Drawing.Point(83, 12);
            this.TXTSearch.Name = "TXTSearch";
            this.TXTSearch.Size = new System.Drawing.Size(350, 27);
            this.TXTSearch.TabIndex = 0;
            //
            // btnSearch
            //
            this.btnSearch.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            //
            // btnClearSearch
            //
            this.btnClearSearch.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            //
            // kryptonLabel1
            //
            this.kryptonLabel1.Location = new System.Drawing.Point(12, 15);
            this.kryptonLabel1.Name = "kryptonLabel1";
            this.kryptonLabel1.Size = new System.Drawing.Size(65, 24);
            this.kryptonLabel1.TabIndex = 1;
            this.kryptonLabel1.Values.Text = "Rechercher:";
            //
            // btnAdvancedSearch
            //
            this.btnAdvancedSearch.Location = new System.Drawing.Point(439, 12);
            this.btnAdvancedSearch.Name = "btnAdvancedSearch";
            this.btnAdvancedSearch.Size = new System.Drawing.Size(150, 27);
            this.btnAdvancedSearch.TabIndex = 4;
            this.btnAdvancedSearch.Values.Text = "Filtres avancés";
            this.btnAdvancedSearch.Click += new System.EventHandler(this.BtnAdvancedSearch_Click);
            //
            // panelAdvancedSearch
            //
            this.panelAdvancedSearch.Controls.Add(this.btnResetFilters);
            this.panelAdvancedSearch.Controls.Add(this.btnApplyFilters);
            this.panelAdvancedSearch.Controls.Add(this.lblEndDate);
            this.panelAdvancedSearch.Controls.Add(this.lblStartDate);
            this.panelAdvancedSearch.Controls.Add(this.lblClient);
            this.panelAdvancedSearch.Controls.Add(this.lblBrand);
            this.panelAdvancedSearch.Controls.Add(this.lblDeviceType);
            this.panelAdvancedSearch.Controls.Add(this.dtpEndDate);
            this.panelAdvancedSearch.Controls.Add(this.dtpStartDate);
            this.panelAdvancedSearch.Controls.Add(this.cmbClient);
            this.panelAdvancedSearch.Controls.Add(this.cmbBrand);
            this.panelAdvancedSearch.Controls.Add(this.cmbDeviceType);
            this.panelAdvancedSearch.Location = new System.Drawing.Point(12, 43);
            this.panelAdvancedSearch.Name = "panelAdvancedSearch";
            this.panelAdvancedSearch.Size = new System.Drawing.Size(1109, 80);
            this.panelAdvancedSearch.TabIndex = 5;
            this.panelAdvancedSearch.Visible = false;
            //
            // cmbDeviceType
            //
            this.cmbDeviceType.DropDownWidth = 200;
            this.cmbDeviceType.Location = new System.Drawing.Point(12, 30);
            this.cmbDeviceType.Name = "cmbDeviceType";
            this.cmbDeviceType.Size = new System.Drawing.Size(200, 25);
            this.cmbDeviceType.TabIndex = 0;
            //
            // cmbBrand
            //
            this.cmbBrand.DropDownWidth = 200;
            this.cmbBrand.Location = new System.Drawing.Point(218, 30);
            this.cmbBrand.Name = "cmbBrand";
            this.cmbBrand.Size = new System.Drawing.Size(200, 25);
            this.cmbBrand.TabIndex = 1;
            //
            // cmbClient
            //
            this.cmbClient.DropDownWidth = 200;
            this.cmbClient.Location = new System.Drawing.Point(424, 30);
            this.cmbClient.Name = "cmbClient";
            this.cmbClient.Size = new System.Drawing.Size(200, 25);
            this.cmbClient.TabIndex = 2;
            //
            // dtpStartDate
            //
            this.dtpStartDate.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.dtpStartDate.Location = new System.Drawing.Point(630, 30);
            this.dtpStartDate.Name = "dtpStartDate";
            this.dtpStartDate.Size = new System.Drawing.Size(120, 25);
            this.dtpStartDate.TabIndex = 3;
            //
            // dtpEndDate
            //
            this.dtpEndDate.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.dtpEndDate.Location = new System.Drawing.Point(756, 30);
            this.dtpEndDate.Name = "dtpEndDate";
            this.dtpEndDate.Size = new System.Drawing.Size(120, 25);
            this.dtpEndDate.TabIndex = 4;
            //
            // lblDeviceType
            //
            this.lblDeviceType.Location = new System.Drawing.Point(12, 3);
            this.lblDeviceType.Name = "lblDeviceType";
            this.lblDeviceType.Size = new System.Drawing.Size(42, 24);
            this.lblDeviceType.TabIndex = 5;
            this.lblDeviceType.Values.Text = "Type";
            //
            // lblBrand
            //
            this.lblBrand.Location = new System.Drawing.Point(218, 3);
            this.lblBrand.Name = "lblBrand";
            this.lblBrand.Size = new System.Drawing.Size(58, 24);
            this.lblBrand.TabIndex = 6;
            this.lblBrand.Values.Text = "Marque";
            //
            // lblClient
            //
            this.lblClient.Location = new System.Drawing.Point(424, 3);
            this.lblClient.Name = "lblClient";
            this.lblClient.Size = new System.Drawing.Size(45, 24);
            this.lblClient.TabIndex = 7;
            this.lblClient.Values.Text = "Client";
            //
            // lblStartDate
            //
            this.lblStartDate.Location = new System.Drawing.Point(630, 3);
            this.lblStartDate.Name = "lblStartDate";
            this.lblStartDate.Size = new System.Drawing.Size(87, 24);
            this.lblStartDate.TabIndex = 8;
            this.lblStartDate.Values.Text = "Date début";
            //
            // lblEndDate
            //
            this.lblEndDate.Location = new System.Drawing.Point(756, 3);
            this.lblEndDate.Name = "lblEndDate";
            this.lblEndDate.Size = new System.Drawing.Size(63, 24);
            this.lblEndDate.TabIndex = 9;
            this.lblEndDate.Values.Text = "Date fin";
            //
            // btnApplyFilters
            //
            this.btnApplyFilters.Location = new System.Drawing.Point(882, 30);
            this.btnApplyFilters.Name = "btnApplyFilters";
            this.btnApplyFilters.Size = new System.Drawing.Size(100, 25);
            this.btnApplyFilters.TabIndex = 10;
            this.btnApplyFilters.Values.Text = "Appliquer";
            this.btnApplyFilters.Click += new System.EventHandler(this.BtnApplyFilters_Click);
            //
            // btnResetFilters
            //
            this.btnResetFilters.Location = new System.Drawing.Point(988, 30);
            this.btnResetFilters.Name = "btnResetFilters";
            this.btnResetFilters.Size = new System.Drawing.Size(100, 25);
            this.btnResetFilters.TabIndex = 11;
            this.btnResetFilters.Values.Text = "Réinitialiser";
            this.btnResetFilters.Click += new System.EventHandler(this.BtnResetFilters_Click);
            //
            // FRM_DEVICES_LIST
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1133, 661);
            this.Controls.Add(this.kryptonLabel1);
            this.Controls.Add(this.TXTSearch);
            this.Controls.Add(this.btnAdvancedSearch);
            this.Controls.Add(this.panelAdvancedSearch);
            this.Controls.Add(this.kryptonHeaderGroup1);
            this.Controls.Add(this.kryptonComboBox1);
            this.Name = "FRM_DEVICES_LIST";
            this.Text = "Liste des appareils";
            this.Load += new System.EventHandler(this.FRM_DEVICES_LIST_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonComboBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1.Panel)).EndInit();
            this.kryptonHeaderGroup1.Panel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1)).EndInit();
            this.kryptonHeaderGroup1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelAdvancedSearch)).EndInit();
            this.panelAdvancedSearch.ResumeLayout(false);
            this.panelAdvancedSearch.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDeviceType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbBrand)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbClient)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Krypton.Toolkit.ButtonSpecHeaderGroup btnFirst;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnPrev;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnNext;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnLast;
        private Krypton.Toolkit.KryptonComboBox kryptonComboBox1;
        private Krypton.Toolkit.KryptonHeaderGroup kryptonHeaderGroup1;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnAdd;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnEdit;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnDelete;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnSelect;
        private Krypton.Toolkit.KryptonDataGridView kryptonDataGridView1;
        private Krypton.Toolkit.KryptonTextBox TXTSearch;
        private Krypton.Toolkit.ButtonSpecAny btnSearch;
        private Krypton.Toolkit.ButtonSpecAny btnClearSearch;
        private Krypton.Toolkit.KryptonLabel kryptonLabel1;
        private Krypton.Toolkit.KryptonButton btnAdvancedSearch;
        private Krypton.Toolkit.KryptonPanel panelAdvancedSearch;
        private Krypton.Toolkit.KryptonComboBox cmbDeviceType;
        private Krypton.Toolkit.KryptonComboBox cmbBrand;
        private Krypton.Toolkit.KryptonComboBox cmbClient;
        private Krypton.Toolkit.KryptonDateTimePicker dtpStartDate;
        private Krypton.Toolkit.KryptonDateTimePicker dtpEndDate;
        private Krypton.Toolkit.KryptonLabel lblDeviceType;
        private Krypton.Toolkit.KryptonLabel lblBrand;
        private Krypton.Toolkit.KryptonLabel lblClient;
        private Krypton.Toolkit.KryptonLabel lblStartDate;
        private Krypton.Toolkit.KryptonLabel lblEndDate;
        private Krypton.Toolkit.KryptonButton btnApplyFilters;
        private Krypton.Toolkit.KryptonButton btnResetFilters;
    }
}
