﻿using IRepairIT.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace IRepairIT.Models
{
    public class InventoryTransaction
    {
        [Display(Name = "Identifiant")]
        public int Id { get; set; }

        [Display(Name = "Identifiant de la pièce")]
        public int PartId { get; set; }

        [Display(Name = "Type de transaction")]
        public TransactionType TransactionType { get; set; }

        [Display(Name = "Quantité")]
        public int Quantity { get; set; }

        [Display(Name = "Référence")]
        public string Reference { get; set; }

        [Display(Name = "Notes")]
        public string Notes { get; set; }

        [Display(Name = "Identifiant de l'utilisateur")]
        public int? UserId { get; set; }

        [Display(Name = "Date de création")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Pièce")]
        public Part Part { get; set; }

        [Display(Name = "Utilisateur")]
        public User User { get; set; }
    }
}
