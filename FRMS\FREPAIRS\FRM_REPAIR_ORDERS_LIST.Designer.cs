﻿﻿namespace IRepairIT.FRMS.FREPAIRS
{
    partial class FRM_REPAIR_ORDERS_LIST
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnFirst = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnPrev = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnNext = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnLast = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.kryptonComboBox1 = new Krypton.Toolkit.KryptonComboBox();
            this.kryptonHeaderGroup1 = new Krypton.Toolkit.KryptonHeaderGroup();
            this.kryptonDataGridView1 = new Krypton.Toolkit.KryptonDataGridView();
            this.TXTSearch = new Krypton.Toolkit.KryptonTextBox();
            this.btnSearch = new Krypton.Toolkit.ButtonSpecAny();
            this.btnClearSearch = new Krypton.Toolkit.ButtonSpecAny();
            this.kryptonLabel1 = new Krypton.Toolkit.KryptonLabel();
            this.btnAdd = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnEdit = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            this.btnDelete = new Krypton.Toolkit.ButtonSpecHeaderGroup();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonComboBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1.Panel)).BeginInit();
            this.kryptonHeaderGroup1.Panel.SuspendLayout();
            this.kryptonHeaderGroup1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // btnFirst
            // 
            this.btnFirst.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowLeft;
            this.btnFirst.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            // 
            // btnPrev
            // 
            this.btnPrev.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowLeft;
            this.btnPrev.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            // 
            // btnNext
            // 
            this.btnNext.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowRight;
            this.btnNext.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            // 
            // btnLast
            // 
            this.btnLast.Type = Krypton.Toolkit.PaletteButtonSpecStyle.ArrowRight;
            this.btnLast.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            // 
            // kryptonComboBox1
            // 
            this.kryptonComboBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonComboBox1.DropDownWidth = 121;
            this.kryptonComboBox1.IntegralHeight = false;
            this.kryptonComboBox1.Location = new System.Drawing.Point(750, 10);
            this.kryptonComboBox1.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.kryptonComboBox1.Name = "kryptonComboBox1";
            this.kryptonComboBox1.Size = new System.Drawing.Size(91, 24);
            this.kryptonComboBox1.TabIndex = 2;
            // 
            // kryptonHeaderGroup1
            // 
            this.kryptonHeaderGroup1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnFirst);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnPrev);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnNext);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnLast);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnAdd);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnEdit);
            this.kryptonHeaderGroup1.ButtonSpecs.Add(this.btnDelete);
            this.kryptonHeaderGroup1.HeaderVisibleSecondary = false;
            this.kryptonHeaderGroup1.Location = new System.Drawing.Point(9, 39);
            this.kryptonHeaderGroup1.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.kryptonHeaderGroup1.Name = "kryptonHeaderGroup1";
            // 
            // kryptonHeaderGroup1.Panel
            // 
            this.kryptonHeaderGroup1.Panel.Controls.Add(this.kryptonDataGridView1);
            this.kryptonHeaderGroup1.Size = new System.Drawing.Size(832, 488);
            this.kryptonHeaderGroup1.TabIndex = 3;
            this.kryptonHeaderGroup1.ValuesPrimary.Heading = "Ordres de réparation";
            this.kryptonHeaderGroup1.ValuesPrimary.Image = global::IRepairIT.Properties.Resources.tool_pencil;
            this.kryptonHeaderGroup1.ValuesSecondary.Heading = "Total: 0 - Page: 1/1";
            // 
            // kryptonDataGridView1
            // 
            this.kryptonDataGridView1.AllowUserToAddRows = false;
            this.kryptonDataGridView1.AllowUserToDeleteRows = false;
            this.kryptonDataGridView1.AllowUserToResizeRows = false;
            this.kryptonDataGridView1.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.kryptonDataGridView1.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.kryptonDataGridView1.ColumnHeadersHeight = 36;
            this.kryptonDataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.kryptonDataGridView1.Location = new System.Drawing.Point(0, 0);
            this.kryptonDataGridView1.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.kryptonDataGridView1.MultiSelect = false;
            this.kryptonDataGridView1.Name = "kryptonDataGridView1";
            this.kryptonDataGridView1.ReadOnly = true;
            this.kryptonDataGridView1.RowHeadersVisible = false;
            this.kryptonDataGridView1.RowHeadersWidth = 51;
            this.kryptonDataGridView1.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.kryptonDataGridView1.Size = new System.Drawing.Size(830, 455);
            this.kryptonDataGridView1.TabIndex = 0;
            // 
            // TXTSearch
            // 
            this.TXTSearch.ButtonSpecs.Add(this.btnSearch);
            this.TXTSearch.ButtonSpecs.Add(this.btnClearSearch);
            this.TXTSearch.Location = new System.Drawing.Point(95, 10);
            this.TXTSearch.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.TXTSearch.Name = "TXTSearch";
            this.TXTSearch.Size = new System.Drawing.Size(229, 25);
            this.TXTSearch.TabIndex = 0;
            // 
            // btnSearch
            // 
            this.btnSearch.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            // 
            // btnClearSearch
            // 
            this.btnClearSearch.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            // 
            // kryptonLabel1
            // 
            this.kryptonLabel1.Location = new System.Drawing.Point(9, 12);
            this.kryptonLabel1.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.kryptonLabel1.Name = "kryptonLabel1";
            this.kryptonLabel1.Size = new System.Drawing.Size(82, 21);
            this.kryptonLabel1.TabIndex = 1;
            this.kryptonLabel1.Values.Text = "Rechercher:";
            // 
            // btnAdd
            // 
            this.btnAdd.Image = global::IRepairIT.Properties.Resources.button_circle_add;
            this.btnAdd.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            // 
            // btnEdit
            // 
            this.btnEdit.Image = global::IRepairIT.Properties.Resources.tool_pencil;
            this.btnEdit.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            // 
            // btnDelete
            // 
            this.btnDelete.Image = global::IRepairIT.Properties.Resources.trash;
            this.btnDelete.UniqueName = "F0E0D8A09A0A4B0DF0E0D8A09A0A4B0D";
            // 
            // FRM_REPAIR_ORDERS_LIST
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(850, 537);
            this.Controls.Add(this.kryptonLabel1);
            this.Controls.Add(this.TXTSearch);
            this.Controls.Add(this.kryptonHeaderGroup1);
            this.Controls.Add(this.kryptonComboBox1);
            this.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.Name = "FRM_REPAIR_ORDERS_LIST";
            this.Text = "Liste des ordres de réparation";
            this.Load += new System.EventHandler(this.FRM_REPAIR_ORDERS_LIST_Load);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonComboBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1.Panel)).EndInit();
            this.kryptonHeaderGroup1.Panel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonHeaderGroup1)).EndInit();
            this.kryptonHeaderGroup1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.kryptonDataGridView1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Krypton.Toolkit.ButtonSpecHeaderGroup btnFirst;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnPrev;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnNext;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnLast;
        private Krypton.Toolkit.KryptonComboBox kryptonComboBox1;
        private Krypton.Toolkit.KryptonHeaderGroup kryptonHeaderGroup1;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnAdd;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnEdit;
        private Krypton.Toolkit.ButtonSpecHeaderGroup btnDelete;
        private Krypton.Toolkit.KryptonDataGridView kryptonDataGridView1;
        private Krypton.Toolkit.KryptonTextBox TXTSearch;
        private Krypton.Toolkit.ButtonSpecAny btnSearch;
        private Krypton.Toolkit.ButtonSpecAny btnClearSearch;
        private Krypton.Toolkit.KryptonLabel kryptonLabel1;
    }
}
