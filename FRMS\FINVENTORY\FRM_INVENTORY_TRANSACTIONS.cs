using IRepairIT.Data.Common;
using IRepairIT.Enums;
using IRepairIT.Models;
using IRepairIT.Utilities;
using Krypton.Toolkit;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IRepairIT.FRMS.FINVENTORY
{
    public partial class FRM_INVENTORY_TRANSACTIONS : KryptonForm
    {
        private readonly InventoryCommands _inventoryCommands;
        private int _partId;
        private Part _part;
        private int _pageSize;
        private int _currentPage = 1;
        private int _totalRows = 0;

        public FRM_INVENTORY_TRANSACTIONS()
        {
            InitializeComponent();
            _inventoryCommands = new InventoryCommands();
            _partId = 0;

            // Set default page size to 50
            cboPageSize.Items.AddRange(new object[] { "10", "25", "50", "100", "250" });
            cboPageSize.SelectedIndex = 2; // Select 50 by default
            _pageSize = 50;

            // Set up event handlers
            cboPageSize.SelectedIndexChanged += CboPageSize_SelectedIndexChanged;

            // Set pagination button text
            btnFirst.Text = "Premier";
            btnPrev.Text = "Précédent";
            btnNext.Text = "Suivant";
            btnLast.Text = "Dernier";

            // Set up button click handlers
            btnFirst.Click += BtnFirst_Click;
            btnPrev.Click += BtnPrev_Click;
            btnNext.Click += BtnNext_Click;
            btnLast.Click += BtnLast_Click;
        }

        public FRM_INVENTORY_TRANSACTIONS(int partId) : this()
        {
            _partId = partId;
        }

        private async void FRM_INVENTORY_TRANSACTIONS_Load(object sender, EventArgs e)
        {
            try
            {
                // Set form title
                this.Text = "Mouvements de stock";
                kryptonHeaderGroup1.ValuesPrimary.Heading = "Mouvements de stock";

                // Load part information if partId is provided
                if (_partId > 0)
                {
                    _part = await _inventoryCommands.GetByIdAsync(_partId);
                    if (_part != null)
                    {
                        kryptonHeaderGroup1.ValuesPrimary.Heading = $"Mouvements de stock - {_part.Name} ({_part.Code})";
                    }
                }

                // Load transactions
                await LoadTransactions();

                // Set up context menu
                SetupContextMenu();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du formulaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void SetupContextMenu()
        {
            try
            {
                // Create a standard context menu strip
                ContextMenuStrip contextMenuStrip = new ContextMenuStrip();

                // Add items to the standard context menu
                ToolStripMenuItem refreshItem = new ToolStripMenuItem("Rafraîchir", Properties.Resources.refresh, async (s, e) => await LoadTransactions());
                ToolStripMenuItem addItem = new ToolStripMenuItem("Ajouter un mouvement", Properties.Resources.button_circle_add, BtnAddTransaction_Click);
                ToolStripSeparator separator = new ToolStripSeparator();
                ToolStripMenuItem printItem = new ToolStripMenuItem("Imprimer", Properties.Resources.tablet_iphone, PrintTransactions);

                // Add items to the menu
                contextMenuStrip.Items.Add(refreshItem);
                contextMenuStrip.Items.Add(addItem);
                contextMenuStrip.Items.Add(separator);
                contextMenuStrip.Items.Add(printItem);

                // Attach the context menu to the DataGridView
                kryptonDataGridView1.ContextMenuStrip = contextMenuStrip;

                // Style the context menu
                contextMenuStrip.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
                contextMenuStrip.ShowImageMargin = true;
                contextMenuStrip.ShowCheckMargin = false;
                contextMenuStrip.RightToLeft = RightToLeft.Yes;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la configuration du menu contextuel: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async Task LoadTransactions()
        {
            try
            {
                // Get page size from combo box
                if (cboPageSize.SelectedItem != null)
                {
                    _pageSize = Convert.ToInt32(cboPageSize.Text);
                }

                // Calculate offset based on current page and page size
                int offset = (_currentPage - 1) * _pageSize;

                // Get transactions with pagination
                var result = await GetTransactions(offset, _pageSize);
                _totalRows = await GetTransactionsCount();

                // Set data source
                kryptonDataGridView1.DataSource = result;

                // Update pagination info
                kryptonHeaderGroup1.ValuesSecondary.Heading = $"Total: {_totalRows} - Page: {_currentPage}/{Math.Ceiling((double)_totalRows / _pageSize)}";

                // Enable/disable pagination buttons based on current page
                UpdatePaginationButtons();

                // Apply styling to the DataGridView
                DGV_STYLE.DesignDGV(kryptonDataGridView1);

                // Configure DataGridView columns
                ConfigureDataGridViewColumns();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des mouvements: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async Task<IEnumerable<dynamic>> GetTransactions(int offset, int pageSize)
        {
            return await _inventoryCommands.GetInventoryTransactionsAsync(_partId > 0 ? (int?)_partId : null, offset, pageSize);
        }

        private async Task<int> GetTransactionsCount()
        {
            return await _inventoryCommands.GetInventoryTransactionsCountAsync(_partId > 0 ? (int?)_partId : null);
        }

        private void UpdatePaginationButtons()
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);

            // For Krypton ButtonSpecHeaderGroup, we need to use ButtonEnabled enum instead of bool
            btnFirst.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnPrev.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnNext.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
            btnLast.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
        }

        private async void CboPageSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            _currentPage = 1; // Reset to first page when changing page size
            await LoadTransactions();
        }

        private async void BtnFirst_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage = 1;
                await LoadTransactions();
            }
        }

        private async void BtnPrev_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadTransactions();
            }
        }

        private async void BtnNext_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage++;
                await LoadTransactions();
            }
        }

        private async void BtnLast_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage = totalPages;
                await LoadTransactions();
            }
        }

        private void BtnAddTransaction_Click(object sender, EventArgs e)
        {
            // TODO: Implement this method to add a new transaction
            KryptonMessageBox.Show("Cette fonctionnalité sera implémentée prochainement", "Information",
                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
        }

        private void PrintTransactions(object sender, EventArgs e)
        {
            // TODO: Implement this method to print transactions
            KryptonMessageBox.Show("Cette fonctionnalité sera implémentée prochainement", "Information",
                KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
        }

        private void ConfigureDataGridViewColumns()
        {
            try
            {
                // Clear existing columns
                kryptonDataGridView1.Columns.Clear();

                // Add columns
                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "id",
                    DataPropertyName = "id",
                    HeaderText = "#",
                    Width = 40,
                    ReadOnly = true
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "part_name",
                    DataPropertyName = "part_name",
                    HeaderText = "Nom de l'article",
                    Width = 150,
                    ReadOnly = true
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "transaction_type_display",
                    DataPropertyName = "transaction_type_display",
                    HeaderText = "Type de mouvement",
                    Width = 100,
                    ReadOnly = true
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "quantity",
                    DataPropertyName = "quantity",
                    HeaderText = "Quantité",
                    Width = 80,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N0",
                        Alignment = DataGridViewContentAlignment.MiddleCenter
                    }
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "reference",
                    DataPropertyName = "reference",
                    HeaderText = "Référence",
                    Width = 120,
                    ReadOnly = true
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "notes",
                    DataPropertyName = "notes",
                    HeaderText = "Notes",
                    Width = 200,
                    ReadOnly = true
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "user_name",
                    DataPropertyName = "user_name",
                    HeaderText = "Utilisateur",
                    Width = 100,
                    ReadOnly = true
                });

                kryptonDataGridView1.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "created_at",
                    DataPropertyName = "created_at",
                    HeaderText = "Date de création",
                    Width = 120,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "dd/MM/yyyy HH:mm",
                        Alignment = DataGridViewContentAlignment.MiddleCenter
                    }
                });

                // Set row formatting
                kryptonDataGridView1.RowTemplate.Height = 35;
                kryptonDataGridView1.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(245, 245, 245);

                // Add transaction type color indicator
                kryptonDataGridView1.CellFormatting += (sender, e) =>
                {
                    try
                    {
                        if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
                        {
                            // Check if this is the transaction type column
                            string columnName = kryptonDataGridView1.Columns[e.ColumnIndex].Name;
                            if (columnName.Equals("transaction_type_display", StringComparison.OrdinalIgnoreCase))
                            {
                                DataGridViewRow row = kryptonDataGridView1.Rows[e.RowIndex];
                                string transactionType = row.Cells[e.ColumnIndex].Value?.ToString();

                                if (!string.IsNullOrEmpty(transactionType))
                                {
                                    if (transactionType.Contains("Ajout") || transactionType.Contains("ajout"))
                                    {
                                        e.CellStyle.BackColor = Color.FromArgb(200, 255, 200);
                                        e.CellStyle.ForeColor = Color.DarkGreen;
                                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                                    }
                                    else if (transactionType.Contains("Retrait") || transactionType.Contains("retrait"))
                                    {
                                        e.CellStyle.BackColor = Color.FromArgb(255, 200, 200);
                                        e.CellStyle.ForeColor = Color.DarkRed;
                                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                                    }
                                    else if (transactionType.Contains("Ajustement") || transactionType.Contains("ajustement"))
                                    {
                                        e.CellStyle.BackColor = Color.FromArgb(200, 200, 255);
                                        e.CellStyle.ForeColor = Color.DarkBlue;
                                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                                    }
                                }
                            }
                            // Format quantity column
                            else if (columnName.Equals("quantity", StringComparison.OrdinalIgnoreCase))
                            {
                                DataGridViewRow row = kryptonDataGridView1.Rows[e.RowIndex];
                                string transactionType = row.Cells["transaction_type_display"].Value?.ToString();

                                if (!string.IsNullOrEmpty(transactionType))
                                {
                                    if (transactionType.Contains("Ajout") || transactionType.Contains("ajout"))
                                    {
                                        e.CellStyle.ForeColor = Color.DarkGreen;
                                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                                    }
                                    else if (transactionType.Contains("Retrait") || transactionType.Contains("retrait"))
                                    {
                                        e.CellStyle.ForeColor = Color.DarkRed;
                                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Silently handle any errors in the cell formatting
                        System.Diagnostics.Debug.WriteLine($"Error in CellFormatting: {ex.Message}");
                    }
                };
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la configuration des colonnes du tableau: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
    }
}
