﻿using Krypton.Toolkit;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace IRepairIT.Utilities
{
    public class DGV_STYLE
    {
        public static void DesignDGV(KryptonDataGridView dgv)
        {
            // Basic settings
            dgv.AllowUserToAddRows = false;
            dgv.ShowCellErrors = false;
            dgv.ShowRowErrors = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgv.RowHeadersVisible = true;
            dgv.RowHeadersWidth = 40;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.Height = 32;

        }

    }
}
