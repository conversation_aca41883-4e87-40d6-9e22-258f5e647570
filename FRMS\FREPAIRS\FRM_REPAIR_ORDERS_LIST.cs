﻿﻿using FastReport;
using IRepairIT.Data.Common;
using IRepairIT.Enums;
using IRepairIT.Models;
using IRepairIT.Utilities;
using Krypton.Toolkit;
using System;
using System.Data;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using IRepairIT.FRMS.FREPAIRS;

namespace IRepairIT.FRMS.FREPAIRS
{
    public partial class FRM_REPAIR_ORDERS_LIST : KryptonForm
    {
        private readonly RepairOrderCommands _cmd;
        private int _pageSize;
        private int _currentPage = 1;
        private int _totalRows = 0;
        private string _searchTerm = "";

        public FRM_REPAIR_ORDERS_LIST()
        {
            InitializeComponent();
            _cmd = new RepairOrderCommands();

            // Set default page size to 100
            kryptonComboBox1.Items.AddRange(new object[] { "10", "25", "50", "100", "250", "500" });
            kryptonComboBox1.SelectedIndex = 3; // Select 100 by default
            _pageSize = 100;

            // Set up event handlers
            kryptonComboBox1.SelectedIndexChanged += CmbPageView_SelectedIndexChanged;
            TXTSearch.TextChanged += TXTSearch_TextChanged;

            // Set pagination button text
            btnFirst.Text = "Premier";
            btnPrev.Text = "Précédent";
            btnNext.Text = "Suivant";
            btnLast.Text = "Dernier";

            // Set up button click handlers
            btnFirst.Click += BtnFirst_Click;
            btnPrev.Click += BtnPrev_Click;
            btnNext.Click += BtnNext_Click;
            btnLast.Click += BtnLast_Click;
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnSearch.Click += BtnSearch_Click;
            btnClearSearch.Click += BtnClearSearch_Click;
        }

        public async Task LoadRepairOrders()
        {
            try
            {
                // Get page size from combo box
                if (kryptonComboBox1.SelectedItem != null)
                {
                    _pageSize = Convert.ToInt32(kryptonComboBox1.Text);
                }

                // Calculate offset based on current page and page size
                int offset = (_currentPage - 1) * _pageSize;

                // Get repair orders with pagination
                var result = await _cmd.GetAll(_searchTerm, offset, _pageSize);
                _totalRows = await _cmd.GetCount(_searchTerm);

                // Set data source
                kryptonDataGridView1.DataSource = result;

                // Format status column
                kryptonDataGridView1.CellFormatting += KryptonDataGridView1_CellFormatting;

                // Update pagination info
                kryptonHeaderGroup1.ValuesSecondary.Heading = $"Total: {_totalRows} - Page: {_currentPage}/{Math.Ceiling((double)_totalRows / _pageSize)}";

                // Enable/disable pagination buttons based on current page
                UpdatePaginationButtons();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement des ordres de réparation: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void KryptonDataGridView1_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex >= 0 && kryptonDataGridView1.Columns[e.ColumnIndex].Name == "status" && e.Value != null)
            {
                // Format status column
                string status = e.Value.ToString();
                if (Enum.TryParse(status, out RepairOrderStatus statusEnum))
                {
                    // Get display name from enum
                    var displayAttribute = statusEnum.GetType()
                        .GetField(statusEnum.ToString())
                        .GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.DisplayAttribute), false);

                    if (displayAttribute.Length > 0)
                    {
                        e.Value = ((System.ComponentModel.DataAnnotations.DisplayAttribute)displayAttribute[0]).Name;
                    }

                    // Set cell color based on status
                    switch (statusEnum)
                    {
                        case RepairOrderStatus.received:
                            e.CellStyle.BackColor = Color.LightBlue;
                            break;
                        case RepairOrderStatus.diagnosed:
                            e.CellStyle.BackColor = Color.LightYellow;
                            break;
                        case RepairOrderStatus.waiting_parts:
                            e.CellStyle.BackColor = Color.LightPink;
                            break;
                        case RepairOrderStatus.in_progress:
                            e.CellStyle.BackColor = Color.LightGreen;
                            break;
                        case RepairOrderStatus.repaired:
                            e.CellStyle.BackColor = Color.LightGreen;
                            break;
                        case RepairOrderStatus.ready:
                            e.CellStyle.BackColor = Color.LightGreen;
                            break;
                        case RepairOrderStatus.delivered:
                            e.CellStyle.BackColor = Color.LightGray;
                            break;
                        case RepairOrderStatus.cancelled:
                            e.CellStyle.BackColor = Color.LightCoral;
                            break;
                        case RepairOrderStatus.unrepairable:
                            e.CellStyle.BackColor = Color.LightCoral;
                            break;
                    }
                }
            }

            if (e.ColumnIndex >= 0 && kryptonDataGridView1.Columns[e.ColumnIndex].Name == "payment_status" && e.Value != null)
            {
                // Format payment status column
                string paymentStatus = e.Value.ToString();
                if (Enum.TryParse(paymentStatus, out PaymentStatus paymentStatusEnum))
                {
                    // Get display name from enum
                    var displayAttribute = paymentStatusEnum.GetType()
                        .GetField(paymentStatusEnum.ToString())
                        .GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.DisplayAttribute), false);

                    if (displayAttribute.Length > 0)
                    {
                        e.Value = ((System.ComponentModel.DataAnnotations.DisplayAttribute)displayAttribute[0]).Name;
                    }

                    // Set cell color based on payment status
                    switch (paymentStatusEnum)
                    {
                        case PaymentStatus.unpaid:
                            e.CellStyle.BackColor = Color.LightCoral;
                            break;
                        case PaymentStatus.partially_paid:
                            e.CellStyle.BackColor = Color.LightYellow;
                            break;
                        case PaymentStatus.paid:
                            e.CellStyle.BackColor = Color.LightGreen;
                            break;
                    }
                }
            }
        }

        private async void FRM_REPAIR_ORDERS_LIST_Load(object sender, EventArgs e)
        {
            try
            {
                // Set row height
                kryptonDataGridView1.RowTemplate.Height = 32;

                // Load repair orders data
                await LoadRepairOrders();

                // Apply enhanced styling
                DGV_STYLE.DesignDGV(kryptonDataGridView1);

                // Set up context menu
                SetupContextMenu();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors du chargement du formulaire: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private void SetupContextMenu()
        {
            try
            {
                // Create a standard context menu strip
                ContextMenuStrip contextMenuStrip = new ContextMenuStrip();

                // Add items to the standard context menu
                ToolStripMenuItem refreshItem = new ToolStripMenuItem("Rafraîchir", null, (s, e) => { _ = LoadRepairOrders(); });
                ToolStripMenuItem addItem = new ToolStripMenuItem("Ajouter un ordre de réparation", null, BtnAdd_Click);
                ToolStripMenuItem editItem = new ToolStripMenuItem("Modifier", null, BtnEdit_Click);
                ToolStripMenuItem deleteItem = new ToolStripMenuItem("Supprimer", null, BtnDelete_Click);
                ToolStripSeparator separator = new ToolStripSeparator();
                ToolStripMenuItem printItem = new ToolStripMenuItem("Imprimer", null, PrintDataGridView);

                // Add status change submenu
                ToolStripMenuItem changeStatusItem = new ToolStripMenuItem("Changer le statut", null);

                // Add status options
                foreach (RepairOrderStatus status in Enum.GetValues(typeof(RepairOrderStatus)))
                {
                    var displayAttribute = status.GetType()
                        .GetField(status.ToString())
                        .GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.DisplayAttribute), false);

                    string statusName = status.ToString();
                    if (displayAttribute.Length > 0)
                    {
                        statusName = ((System.ComponentModel.DataAnnotations.DisplayAttribute)displayAttribute[0]).Name;
                    }

                    ToolStripMenuItem statusItem = new ToolStripMenuItem(statusName, null, (s, e) => { ChangeStatus(status); });
                    changeStatusItem.DropDownItems.Add(statusItem);
                }

                // Add items to the menu
                contextMenuStrip.Items.Add(refreshItem);
                contextMenuStrip.Items.Add(addItem);
                contextMenuStrip.Items.Add(editItem);
                contextMenuStrip.Items.Add(deleteItem);
                contextMenuStrip.Items.Add(separator);
                contextMenuStrip.Items.Add(changeStatusItem);
                contextMenuStrip.Items.Add(printItem);

                // Attach the context menu to the DataGridView
                kryptonDataGridView1.ContextMenuStrip = contextMenuStrip;

                // Style the context menu
                contextMenuStrip.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
                contextMenuStrip.ShowImageMargin = true;
                contextMenuStrip.ShowCheckMargin = false;
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de la configuration du menu contextuel: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }

        private async void ChangeStatus(RepairOrderStatus status)
        {
            if (kryptonDataGridView1.CurrentRow != null)
            {
                try
                {
                    int repairOrderId = Convert.ToInt32(kryptonDataGridView1.CurrentRow.Cells["id"].Value);
                    string orderNumber = kryptonDataGridView1.CurrentRow.Cells["order_number"].Value.ToString();

                    // Ask for confirmation
                    var result = KryptonMessageBox.Show(
                        $"Êtes-vous sûr de vouloir changer le statut de l'ordre de réparation {orderNumber} à '{GetStatusDisplayName(status)}'?",
                        "Confirmation",
                        KryptonMessageBoxButtons.YesNo,
                        KryptonMessageBoxIcon.Question
                    );

                    if (result == DialogResult.Yes)
                    {
                        // Ask for notes
                        string notes = "";
                        using (var inputForm = new KryptonForm())
                        {
                            inputForm.Text = "Notes de changement de statut";
                            inputForm.Size = new Size(400, 200);
                            inputForm.StartPosition = FormStartPosition.CenterParent;
                            inputForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                            inputForm.MaximizeBox = false;
                            inputForm.MinimizeBox = false;

                            var label = new KryptonLabel
                            {
                                Text = "Notes (optionnel):",
                                Location = new Point(10, 10),
                                Size = new Size(380, 20)
                            };

                            var textBox = new KryptonTextBox
                            {
                                Location = new Point(10, 40),
                                Size = new Size(360, 60),
                                Multiline = true
                            };

                            var okButton = new KryptonButton
                            {
                                Text = "OK",
                                Location = new Point(200, 120),
                                Size = new Size(80, 25),
                                DialogResult = DialogResult.OK
                            };

                            var cancelButton = new KryptonButton
                            {
                                Text = "Annuler",
                                Location = new Point(290, 120),
                                Size = new Size(80, 25),
                                DialogResult = DialogResult.Cancel
                            };

                            inputForm.Controls.Add(label);
                            inputForm.Controls.Add(textBox);
                            inputForm.Controls.Add(okButton);
                            inputForm.Controls.Add(cancelButton);

                            inputForm.AcceptButton = okButton;
                            inputForm.CancelButton = cancelButton;

                            if (inputForm.ShowDialog() == DialogResult.OK)
                            {
                                notes = textBox.Text;

                                // Update status
                                bool success = await _cmd.UpdateStatusAsync(repairOrderId, status, notes, Properties.Settings.Default.CurrentUserId);

                                if (success)
                                {
                                    await LoadRepairOrders();
                                    KryptonMessageBox.Show("Le statut a été mis à jour avec succès", "Succès",
                                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                                }
                                else
                                {
                                    KryptonMessageBox.Show("Erreur lors de la mise à jour du statut", "Erreur",
                                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    KryptonMessageBox.Show($"Erreur lors du changement de statut: {ex.Message}", "Erreur",
                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                }
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un ordre de réparation", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private string GetStatusDisplayName(RepairOrderStatus status)
        {
            var displayAttribute = status.GetType()
                .GetField(status.ToString())
                .GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.DisplayAttribute), false);

            if (displayAttribute.Length > 0)
            {
                return ((System.ComponentModel.DataAnnotations.DisplayAttribute)displayAttribute[0]).Name;
            }

            return status.ToString();
        }

        private void UpdatePaginationButtons()
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);

            // For Krypton ButtonSpecHeaderGroup, we need to use ButtonEnabled enum instead of bool
            btnFirst.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnPrev.Enabled = _currentPage > 1 ? ButtonEnabled.True : ButtonEnabled.False;
            btnNext.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
            btnLast.Enabled = _currentPage < totalPages ? ButtonEnabled.True : ButtonEnabled.False;
        }

        private async void CmbPageView_SelectedIndexChanged(object sender, EventArgs e)
        {
            _currentPage = 1; // Reset to first page when changing page size
            await LoadRepairOrders();
        }

        private async void TXTSearch_TextChanged(object sender, EventArgs e)
        {
            _searchTerm = TXTSearch.Text.Trim();
            _currentPage = 1; // Reset to first page when searching
            await LoadRepairOrders();
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            _searchTerm = TXTSearch.Text.Trim();
            _currentPage = 1; // Reset to first page when searching
            await LoadRepairOrders();
        }

        private async void BtnClearSearch_Click(object sender, EventArgs e)
        {
            TXTSearch.Text = "";
            _searchTerm = "";
            _currentPage = 1; // Reset to first page when clearing search
            await LoadRepairOrders();
        }

        private async void BtnFirst_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage = 1;
                await LoadRepairOrders();
            }
        }

        private async void BtnPrev_Click(object sender, EventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadRepairOrders();
            }
        }

        private async void BtnNext_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage++;
                await LoadRepairOrders();
            }
        }

        private async void BtnLast_Click(object sender, EventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalRows / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage = totalPages;
                await LoadRepairOrders();
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var frm = new FRM_REPAIR_ORDER();
            frm.FormClosed += (s, args) =>
            {
                if (frm.HasChanges)
                {
                    _ = LoadRepairOrders();
                }
            };
            frm.ShowDialog();
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (kryptonDataGridView1.CurrentRow != null)
            {
                int repairOrderId = Convert.ToInt32(kryptonDataGridView1.CurrentRow.Cells["id"].Value);
                var frm = new FRM_REPAIR_ORDER(repairOrderId);
                frm.FormClosed += (s, args) =>
                {
                    if (frm.HasChanges)
                    {
                        _ = LoadRepairOrders();
                    }
                };
                frm.ShowDialog();
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un ordre de réparation à modifier", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (kryptonDataGridView1.CurrentRow != null)
            {
                int repairOrderId = Convert.ToInt32(kryptonDataGridView1.CurrentRow.Cells["id"].Value);
                string orderNumber = kryptonDataGridView1.CurrentRow.Cells["order_number"].Value.ToString();

                var result = KryptonMessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer l'ordre de réparation '{orderNumber}'?",
                    "Confirmation de suppression",
                    KryptonMessageBoxButtons.YesNo,
                    KryptonMessageBoxIcon.Question
                );

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        // Try to delete the repair order without dependencies first
                        try
                        {
                            bool success = await _cmd.DeleteAsync(repairOrderId);
                            if (success)
                            {
                                await LoadRepairOrders();
                                KryptonMessageBox.Show("L'ordre de réparation a été supprimé avec succès", "Succès",
                                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                                return;
                            }
                        }
                        catch (Exception ex)
                        {
                            // If deletion fails due to dependencies, ask if user wants to delete with dependencies
                            var deleteWithDepsResult = KryptonMessageBox.Show(
                                $"{ex.Message}\n\nVoulez-vous supprimer cet ordre de réparation avec toutes ses dépendances (services, pièces, paiements, historique) ?",
                                "Supprimer avec dépendances",
                                KryptonMessageBoxButtons.YesNo,
                                KryptonMessageBoxIcon.Warning
                            );

                            if (deleteWithDepsResult == DialogResult.Yes)
                            {
                                // Delete with dependencies
                                bool success = await _cmd.DeleteWithDependenciesAsync(repairOrderId);
                                if (success)
                                {
                                    await LoadRepairOrders();
                                    KryptonMessageBox.Show("L'ordre de réparation et toutes ses dépendances ont été supprimés avec succès", "Succès",
                                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
                                    return;
                                }
                                else
                                {
                                    KryptonMessageBox.Show("Erreur lors de la suppression de l'ordre de réparation et ses dépendances", "Erreur",
                                        KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                                    return;
                                }
                            }
                            else
                            {
                                // User chose not to delete with dependencies
                                return;
                            }
                        }

                        // If we get here, something went wrong with the basic delete
                        KryptonMessageBox.Show("Erreur lors de la suppression de l'ordre de réparation", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }
                    catch (Exception ex)
                    {
                        KryptonMessageBox.Show($"Erreur lors de la suppression de l'ordre de réparation: {ex.Message}", "Erreur",
                            KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                KryptonMessageBox.Show("Veuillez sélectionner un ordre de réparation à supprimer", "Information",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Information);
            }
        }

        private void PrintDataGridView(object sender, EventArgs e)
        {
            try
            {
                // Create a simple report design
                string reportXml = @"<?xml version=""1.0"" encoding=""utf-8""?>
<Report ScriptLanguage=""CSharp"" ReportInfo.Created=""01/01/2023 00:00:00"" ReportInfo.Modified=""01/01/2023 00:00:00"" ReportInfo.CreatorVersion=""*******"">
  <Dictionary>
    <TableDataSource Name=""RepairOrders"" ReferenceName=""RepairOrders"">
      <Column Name=""OrderNumber"" DataType=""System.String""/>
      <Column Name=""Client"" DataType=""System.String""/>
      <Column Name=""Device"" DataType=""System.String""/>
      <Column Name=""Status"" DataType=""System.String""/>
      <Column Name=""TotalCost"" DataType=""System.Decimal""/>
      <Column Name=""PaymentStatus"" DataType=""System.String""/>
      <Column Name=""DateCreation"" DataType=""System.DateTime""/>
      <Column Name=""Technician"" DataType=""System.String""/>
    </TableDataSource>
  </Dictionary>
  <ReportPage Name=""Page1"" Watermark.Font=""Arial, 60pt"">
    <ReportTitleBand Name=""ReportTitle1"" Width=""718.2"" Height=""37.8"">
      <TextObject Name=""Title"" Width=""718.2"" Height=""37.8"" Dock=""Fill"" Text=""Liste des Ordres de Réparation"" HorzAlign=""Center"" VertAlign=""Center"" Font=""Arial, 14pt, style=Bold""/>
    </ReportTitleBand>
    <PageHeaderBand Name=""PageHeader1"" Top=""41.8"" Width=""718.2"" Height=""28.35"">
      <TextObject Name=""Header1"" Width=""94.5"" Height=""28.35"" Text=""N° Commande"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
      <TextObject Name=""Header2"" Left=""94.5"" Width=""113.4"" Height=""28.35"" Text=""Client"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
      <TextObject Name=""Header3"" Left=""207.9"" Width=""113.4"" Height=""28.35"" Text=""Appareil"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
      <TextObject Name=""Header4"" Left=""321.3"" Width=""94.5"" Height=""28.35"" Text=""Statut"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
      <TextObject Name=""Header5"" Left=""415.8"" Width=""94.5"" Height=""28.35"" Text=""Coût Total"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
      <TextObject Name=""Header6"" Left=""510.3"" Width=""94.5"" Height=""28.35"" Text=""Paiement"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
      <TextObject Name=""Header7"" Left=""604.8"" Width=""113.4"" Height=""28.35"" Text=""Date"" VertAlign=""Center"" Font=""Arial, 10pt, style=Bold""/>
    </PageHeaderBand>
    <DataBand Name=""Data1"" Top=""74.15"" Width=""718.2"" Height=""18.9"" DataSource=""RepairOrders"">
      <TextObject Name=""Data1"" Width=""94.5"" Height=""18.9"" Text=""[RepairOrders.OrderNumber]"" VertAlign=""Center"" Font=""Arial, 10pt""/>
      <TextObject Name=""Data2"" Left=""94.5"" Width=""113.4"" Height=""18.9"" Text=""[RepairOrders.Client]"" VertAlign=""Center"" Font=""Arial, 10pt""/>
      <TextObject Name=""Data3"" Left=""207.9"" Width=""113.4"" Height=""18.9"" Text=""[RepairOrders.Device]"" VertAlign=""Center"" Font=""Arial, 10pt""/>
      <TextObject Name=""Data4"" Left=""321.3"" Width=""94.5"" Height=""18.9"" Text=""[RepairOrders.Status]"" VertAlign=""Center"" Font=""Arial, 10pt""/>
      <TextObject Name=""Data5"" Left=""415.8"" Width=""94.5"" Height=""18.9"" Text=""[RepairOrders.TotalCost]"" Format=""Currency"" Format.UseLocale=""true"" VertAlign=""Center"" Font=""Arial, 10pt""/>
      <TextObject Name=""Data6"" Left=""510.3"" Width=""94.5"" Height=""18.9"" Text=""[RepairOrders.PaymentStatus]"" VertAlign=""Center"" Font=""Arial, 10pt""/>
      <TextObject Name=""Data7"" Left=""604.8"" Width=""113.4"" Height=""18.9"" Text=""[RepairOrders.DateCreation]"" Format=""Date"" Format.Format=""d"" VertAlign=""Center"" Font=""Arial, 10pt""/>
    </DataBand>
    <PageFooterBand Name=""PageFooter1"" Top=""97.05"" Width=""718.2"" Height=""18.9"">
      <TextObject Name=""Footer"" Width=""718.2"" Height=""18.9"" Dock=""Fill"" Text=""Page [Page] de [TotalPages]"" HorzAlign=""Right"" VertAlign=""Center"" Font=""Arial, 10pt""/>
    </PageFooterBand>
  </ReportPage>
</Report>";

                // Create a new report
                Report report = new Report();
                report.LoadFromString(reportXml);

                // Create a data table for the report
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("OrderNumber", typeof(string));
                dataTable.Columns.Add("Client", typeof(string));
                dataTable.Columns.Add("Device", typeof(string));
                dataTable.Columns.Add("Status", typeof(string));
                dataTable.Columns.Add("TotalCost", typeof(decimal));
                dataTable.Columns.Add("PaymentStatus", typeof(string));
                dataTable.Columns.Add("DateCreation", typeof(DateTime));
                dataTable.Columns.Add("Technician", typeof(string));

                // Add data from the DataGridView
                foreach (DataGridViewRow row in kryptonDataGridView1.Rows)
                {
                    if (row.Cells["order_number"].Value != null)
                    {
                        // Get status display name
                        string status = row.Cells["status"].Value.ToString();
                        string statusDisplay = status;
                        if (Enum.TryParse(status, out RepairOrderStatus statusEnum))
                        {
                            statusDisplay = GetStatusDisplayName(statusEnum);
                        }

                        // Get payment status display name
                        string paymentStatus = row.Cells["payment_status"].Value.ToString();
                        string paymentStatusDisplay = paymentStatus;
                        if (Enum.TryParse(paymentStatus, out PaymentStatus paymentStatusEnum))
                        {
                            var displayAttribute = paymentStatusEnum.GetType()
                                .GetField(paymentStatusEnum.ToString())
                                .GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.DisplayAttribute), false);

                            if (displayAttribute.Length > 0)
                            {
                                paymentStatusDisplay = ((System.ComponentModel.DataAnnotations.DisplayAttribute)displayAttribute[0]).Name;
                            }
                        }

                        dataTable.Rows.Add(
                            row.Cells["order_number"].Value.ToString(),
                            row.Cells["client"].Value.ToString(),
                            row.Cells["device"].Value.ToString(),
                            statusDisplay,
                            Convert.ToDecimal(row.Cells["total_cost"].Value),
                            paymentStatusDisplay,
                            Convert.ToDateTime(row.Cells["date_creation"].Value),
                            row.Cells["technician"].Value != null ? row.Cells["technician"].Value.ToString() : ""
                        );
                    }
                }

                // Register the data table
                report.RegisterData(dataTable, "RepairOrders");

                // Show the report
                report.Show();
            }
            catch (Exception ex)
            {
                KryptonMessageBox.Show($"Erreur lors de l'impression: {ex.Message}", "Erreur",
                    KryptonMessageBoxButtons.OK, KryptonMessageBoxIcon.Error);
            }
        }
    }
}
